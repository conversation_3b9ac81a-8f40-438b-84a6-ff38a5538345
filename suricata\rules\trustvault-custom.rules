# TrustVault Custom Suricata Rules
# ============================================================================

# ============================================================================
# WEB APPLICATION ATTACKS
# ============================================================================

# SQL Injection Detection
alert http $EXTERNAL_NET any -> $HTTP_SERVERS $HTTP_PORTS (msg:"TrustVault: SQL Injection Attempt"; flow:established,to_server; content:"union"; nocase; content:"select"; nocase; distance:0; within:100; pcre:"/union\s+select/i"; classtype:web-application-attack; sid:2000001; rev:1;)

alert http $EXTERNAL_NET any -> $HTTP_SERVERS $HTTP_PORTS (msg:"TrustVault: SQL Injection - OR 1=1"; flow:established,to_server; content:"or"; nocase; content:"1=1"; nocase; distance:0; within:20; classtype:web-application-attack; sid:2000002; rev:1;)

alert http $EXTERNAL_NET any -> $HTTP_SERVERS $HTTP_PORTS (msg:"TrustVault: SQL Injection - Information Schema"; flow:established,to_server; content:"information_schema"; nocase; classtype:web-application-attack; sid:2000003; rev:1;)

# Cross-Site Scripting (XSS)
alert http $EXTERNAL_NET any -> $HTTP_SERVERS $HTTP_PORTS (msg:"TrustVault: XSS Attempt - Script Tag"; flow:established,to_server; content:"<script"; nocase; classtype:web-application-attack; sid:2000010; rev:1;)

alert http $EXTERNAL_NET any -> $HTTP_SERVERS $HTTP_PORTS (msg:"TrustVault: XSS Attempt - JavaScript Event"; flow:established,to_server; pcre:"/on(load|error|click|mouseover)\s*=/i"; classtype:web-application-attack; sid:2000011; rev:1;)

alert http $EXTERNAL_NET any -> $HTTP_SERVERS $HTTP_PORTS (msg:"TrustVault: XSS Attempt - JavaScript Protocol"; flow:established,to_server; content:"javascript:"; nocase; classtype:web-application-attack; sid:2000012; rev:1;)

# Directory Traversal
alert http $EXTERNAL_NET any -> $HTTP_SERVERS $HTTP_PORTS (msg:"TrustVault: Directory Traversal Attempt"; flow:established,to_server; content:"../"; classtype:web-application-attack; sid:2000020; rev:1;)

alert http $EXTERNAL_NET any -> $HTTP_SERVERS $HTTP_PORTS (msg:"TrustVault: Windows Directory Traversal"; flow:established,to_server; content:"..\\"; classtype:web-application-attack; sid:2000021; rev:1;)

alert http $EXTERNAL_NET any -> $HTTP_SERVERS $HTTP_PORTS (msg:"TrustVault: Sensitive File Access - /etc/passwd"; flow:established,to_server; content:"/etc/passwd"; nocase; classtype:web-application-attack; sid:2000022; rev:1;)

# Command Injection
alert http $EXTERNAL_NET any -> $HTTP_SERVERS $HTTP_PORTS (msg:"TrustVault: Command Injection - System"; flow:established,to_server; content:"system("; nocase; classtype:web-application-attack; sid:2000030; rev:1;)

alert http $EXTERNAL_NET any -> $HTTP_SERVERS $HTTP_PORTS (msg:"TrustVault: Command Injection - Exec"; flow:established,to_server; content:"exec("; nocase; classtype:web-application-attack; sid:2000031; rev:1;)

alert http $EXTERNAL_NET any -> $HTTP_SERVERS $HTTP_PORTS (msg:"TrustVault: Command Injection - Shell"; flow:established,to_server; content:"shell_exec"; nocase; classtype:web-application-attack; sid:2000032; rev:1;)

# ============================================================================
# AUTHENTICATION ATTACKS
# ============================================================================

# Brute Force Detection
alert http $EXTERNAL_NET any -> $HTTP_SERVERS $HTTP_PORTS (msg:"TrustVault: Brute Force Login Attempt"; flow:established,to_server; content:"POST"; http_method; content:"/api/v1/auth/login"; http_uri; threshold:type both, track by_src, count 5, seconds 60; classtype:attempted-recon; sid:2000100; rev:1;)

alert http $EXTERNAL_NET any -> $HTTP_SERVERS $HTTP_PORTS (msg:"TrustVault: Multiple Failed Login Attempts"; flow:established,to_server; content:"401"; http_stat_code; content:"/api/v1/auth/login"; http_uri; threshold:type both, track by_src, count 3, seconds 30; classtype:attempted-recon; sid:2000101; rev:1;)

# Credential Stuffing
alert http $EXTERNAL_NET any -> $HTTP_SERVERS $HTTP_PORTS (msg:"TrustVault: Potential Credential Stuffing"; flow:established,to_server; content:"POST"; http_method; content:"password"; http_client_body; content:"username"; http_client_body; threshold:type both, track by_src, count 10, seconds 300; classtype:attempted-recon; sid:2000102; rev:1;)

# ============================================================================
# API SECURITY
# ============================================================================

# API Abuse Detection
alert http $EXTERNAL_NET any -> $HTTP_SERVERS $HTTP_PORTS (msg:"TrustVault: High API Request Rate"; flow:established,to_server; content:"/api/"; http_uri; threshold:type both, track by_src, count 100, seconds 60; classtype:attempted-dos; sid:2000200; rev:1;)

alert http $EXTERNAL_NET any -> $HTTP_SERVERS $HTTP_PORTS (msg:"TrustVault: Unauthorized API Access"; flow:established,to_server; content:"/api/"; http_uri; content:"401"; http_stat_code; threshold:type both, track by_src, count 5, seconds 60; classtype:attempted-recon; sid:2000201; rev:1;)

# Portfolio API Protection
alert http $EXTERNAL_NET any -> $HTTP_SERVERS $HTTP_PORTS (msg:"TrustVault: Portfolio Data Access"; flow:established,to_server; content:"/api/v1/portfolio"; http_uri; classtype:policy-violation; sid:2000210; rev:1;)

alert http $EXTERNAL_NET any -> $HTTP_SERVERS $HTTP_PORTS (msg:"TrustVault: Financial Transaction API"; flow:established,to_server; content:"/api/v1/transactions"; http_uri; classtype:policy-violation; sid:2000211; rev:1;)

alert http $EXTERNAL_NET any -> $HTTP_SERVERS $HTTP_PORTS (msg:"TrustVault: Balance Inquiry API"; flow:established,to_server; content:"/api/v1/balance"; http_uri; classtype:policy-violation; sid:2000212; rev:1;)

# ============================================================================
# DATA EXFILTRATION
# ============================================================================

# Large Data Transfer
alert http $HTTP_SERVERS $HTTP_PORTS -> $EXTERNAL_NET any (msg:"TrustVault: Large Data Transfer Outbound"; flow:established,from_server; dsize:>1000000; classtype:policy-violation; sid:2000300; rev:1;)

alert http $HTTP_SERVERS $HTTP_PORTS -> $EXTERNAL_NET any (msg:"TrustVault: Bulk Data Export"; flow:established,from_server; content:"export"; http_uri; content:"application/json"; http_header; dsize:>100000; classtype:policy-violation; sid:2000301; rev:1;)

# Suspicious File Downloads
alert http $HTTP_SERVERS $HTTP_PORTS -> $EXTERNAL_NET any (msg:"TrustVault: Database Backup Download"; flow:established,from_server; content:".sql"; http_uri; classtype:policy-violation; sid:2000310; rev:1;)

alert http $HTTP_SERVERS $HTTP_PORTS -> $EXTERNAL_NET any (msg:"TrustVault: Configuration File Download"; flow:established,from_server; content:".conf"; http_uri; classtype:policy-violation; sid:2000311; rev:1;)

# ============================================================================
# MALWARE AND EXPLOITS
# ============================================================================

# Web Shell Detection
alert http $EXTERNAL_NET any -> $HTTP_SERVERS $HTTP_PORTS (msg:"TrustVault: Web Shell Upload Attempt"; flow:established,to_server; content:"POST"; http_method; content:"php"; http_client_body; content:"eval"; http_client_body; classtype:trojan-activity; sid:2000400; rev:1;)

alert http $EXTERNAL_NET any -> $HTTP_SERVERS $HTTP_PORTS (msg:"TrustVault: Suspicious PHP Code"; flow:established,to_server; content:"<?php"; http_client_body; content:"system"; http_client_body; classtype:trojan-activity; sid:2000401; rev:1;)

# Reverse Shell Attempts
alert http $EXTERNAL_NET any -> $HTTP_SERVERS $HTTP_PORTS (msg:"TrustVault: Reverse Shell Attempt"; flow:established,to_server; content:"nc -e"; http_client_body; classtype:trojan-activity; sid:2000410; rev:1;)

alert http $EXTERNAL_NET any -> $HTTP_SERVERS $HTTP_PORTS (msg:"TrustVault: Python Reverse Shell"; flow:established,to_server; content:"socket.socket"; http_client_body; content:"subprocess"; http_client_body; classtype:trojan-activity; sid:2000411; rev:1;)

# ============================================================================
# NETWORK RECONNAISSANCE
# ============================================================================

# Port Scanning
alert tcp $EXTERNAL_NET any -> $HOME_NET any (msg:"TrustVault: Port Scan Detected"; flags:S,12; threshold:type both, track by_src, count 10, seconds 60; classtype:attempted-recon; sid:2000500; rev:1;)

# Service Enumeration
alert http $EXTERNAL_NET any -> $HTTP_SERVERS $HTTP_PORTS (msg:"TrustVault: Service Enumeration"; flow:established,to_server; content:"GET"; http_method; pcre:"/\/(admin|config|test|debug|info)/i"; classtype:attempted-recon; sid:2000510; rev:1;)

# Directory Enumeration
alert http $EXTERNAL_NET any -> $HTTP_SERVERS $HTTP_PORTS (msg:"TrustVault: Directory Enumeration"; flow:established,to_server; content:"404"; http_stat_code; threshold:type both, track by_src, count 20, seconds 60; classtype:attempted-recon; sid:2000520; rev:1;)

# ============================================================================
# COMPLIANCE AND MONITORING
# ============================================================================

# GDPR Data Access
alert http $EXTERNAL_NET any -> $HTTP_SERVERS $HTTP_PORTS (msg:"TrustVault: Personal Data Access"; flow:established,to_server; content:"/api/v1/users"; http_uri; content:"personal"; http_uri; classtype:policy-violation; sid:2000600; rev:1;)

# Administrative Access
alert http $EXTERNAL_NET any -> $HTTP_SERVERS $HTTP_PORTS (msg:"TrustVault: Admin Panel Access"; flow:established,to_server; content:"/admin"; http_uri; classtype:policy-violation; sid:2000610; rev:1;)

alert http $EXTERNAL_NET any -> $HTTP_SERVERS $HTTP_PORTS (msg:"TrustVault: Django Admin Access"; flow:established,to_server; content:"/admin/"; http_uri; classtype:policy-violation; sid:2000611; rev:1;)

# ============================================================================
# CUSTOM THREAT INTELLIGENCE
# ============================================================================

# Known Bad User Agents
alert http $EXTERNAL_NET any -> $HTTP_SERVERS $HTTP_PORTS (msg:"TrustVault: Suspicious User Agent - Scanner"; flow:established,to_server; content:"User-Agent|3a 20|"; http_header; content:"sqlmap"; http_header; classtype:trojan-activity; sid:2000700; rev:1;)

alert http $EXTERNAL_NET any -> $HTTP_SERVERS $HTTP_PORTS (msg:"TrustVault: Suspicious User Agent - Nikto"; flow:established,to_server; content:"User-Agent|3a 20|"; http_header; content:"Nikto"; http_header; classtype:trojan-activity; sid:2000701; rev:1;)

# Suspicious Referrers
alert http $EXTERNAL_NET any -> $HTTP_SERVERS $HTTP_PORTS (msg:"TrustVault: Suspicious Referrer"; flow:established,to_server; content:"Referer|3a 20|"; http_header; pcre:"/Referer:\s*https?:\/\/[^\/]*\.(tk|ml|ga|cf)\//i"; classtype:trojan-activity; sid:2000710; rev:1;)

# ============================================================================
# PERFORMANCE AND AVAILABILITY
# ============================================================================

# DDoS Detection
alert tcp $EXTERNAL_NET any -> $HTTP_SERVERS $HTTP_PORTS (msg:"TrustVault: Potential DDoS Attack"; flags:S; threshold:type both, track by_dst, count 100, seconds 10; classtype:attempted-dos; sid:2000800; rev:1;)

# Resource Exhaustion
alert http $EXTERNAL_NET any -> $HTTP_SERVERS $HTTP_PORTS (msg:"TrustVault: Large Request Body"; flow:established,to_server; dsize:>1000000; classtype:attempted-dos; sid:2000810; rev:1;)

# Slowloris Attack
alert tcp $EXTERNAL_NET any -> $HTTP_SERVERS $HTTP_PORTS (msg:"TrustVault: Slowloris Attack"; flow:established; flowbits:set,http.slow; threshold:type limit, track by_src, count 1, seconds 300; classtype:attempted-dos; sid:2000820; rev:1;)

// TrustVault - Reports Page

import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>rid,
  Card,
  CardContent,
  Button,
  Chip,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  LinearProgress,
} from '@mui/material';
import {
  Add,
  Download,
  Delete,
  PictureAsPdf,
  TableChart,
  Assessment,
  History,
  TrendingUp,
  AccountBalance,
} from '@mui/icons-material';
import { Helmet } from 'react-helmet-async';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { toast } from 'react-hot-toast';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';

// Services
import apiService from '../../services/api';

// Types
interface Report {
  id: string;
  name: string;
  report_type: string;
  report_format: string;
  status: string;
  portfolio_name?: string;
  start_date: string;
  end_date: string;
  file_size_mb?: number;
  generated_at?: string;
  expires_at?: string;
  download_url?: string;
  created_at: string;
}

interface ReportFormData {
  name: string;
  report_type: string;
  report_format: string;
  portfolio?: string;
  start_date: string;
  end_date: string;
  description?: string;
}

// Validation schema
const reportSchema = yup.object().shape({
  name: yup.string().required('Report name is required'),
  report_type: yup.string().required('Report type is required'),
  report_format: yup.string().required('Report format is required'),
  start_date: yup.string().required('Start date is required'),
  end_date: yup.string().required('End date is required'),
});

const ReportsPage: React.FC = () => {
  const queryClient = useQueryClient();
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [reportToDelete, setReportToDelete] = useState<Report | null>(null);

  // Fetch reports
  const { data: reports = [], isLoading: reportsLoading } = useQuery(
    'reports',
    apiService.getReports,
    {
      onError: (error: any) => {
        toast.error('Failed to load reports');
      },
    }
  );

  // Fetch portfolios for dropdown
  const { data: portfolios = [] } = useQuery(
    'portfolios',
    apiService.getPortfolios,
    {
      onError: () => {
        // Silently fail for portfolios
      },
    }
  );

  // Create report mutation
  const createReportMutation = useMutation(
    (data: ReportFormData) => apiService.createReport(data),
    {
      onSuccess: () => {
        toast.success('Report generation started');
        queryClient.invalidateQueries('reports');
        setCreateDialogOpen(false);
        reset();
      },
      onError: (error: any) => {
        const message = error.response?.data?.message || 'Failed to create report';
        toast.error(message);
      },
    }
  );

  // Delete report mutation
  const deleteReportMutation = useMutation(
    (id: string) => apiService.deleteReport(id),
    {
      onSuccess: () => {
        toast.success('Report deleted successfully');
        queryClient.invalidateQueries('reports');
        setDeleteDialogOpen(false);
        setReportToDelete(null);
      },
      onError: (error: any) => {
        const message = error.response?.data?.message || 'Failed to delete report';
        toast.error(message);
      },
    }
  );

  // Form handling
  const {
    control,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<ReportFormData>({
    resolver: yupResolver(reportSchema),
    defaultValues: {
      name: '',
      report_type: '',
      report_format: 'PDF',
      start_date: '',
      end_date: '',
      description: '',
    },
  });

  const handleCreateReport = () => {
    reset();
    setCreateDialogOpen(true);
  };

  const handleDeleteReport = (report: Report) => {
    setReportToDelete(report);
    setDeleteDialogOpen(true);
  };

  const handleDownloadReport = async (report: Report) => {
    try {
      const blob = await apiService.downloadReport(report.id);
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${report.name}.${report.report_format.toLowerCase()}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      toast.success('Report downloaded successfully');
    } catch (error) {
      toast.error('Failed to download report');
    }
  };

  const onSubmit = (data: ReportFormData) => {
    createReportMutation.mutate(data);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return 'success';
      case 'GENERATING':
        return 'info';
      case 'PENDING':
        return 'warning';
      case 'FAILED':
        return 'error';
      case 'EXPIRED':
        return 'error';
      default:
        return 'default';
    }
  };

  const getReportTypeIcon = (reportType: string) => {
    switch (reportType) {
      case 'PORTFOLIO_PERFORMANCE':
        return <TrendingUp />;
      case 'TRANSACTION_HISTORY':
        return <History />;
      case 'TAX_REPORT':
        return <AccountBalance />;
      case 'RISK_ANALYSIS':
        return <Assessment />;
      default:
        return <TableChart />;
    }
  };

  const getFormatIcon = (format: string) => {
    switch (format) {
      case 'PDF':
        return <PictureAsPdf />;
      case 'EXCEL':
        return <TableChart />;
      default:
        return <TableChart />;
    }
  };

  const reportTypes = [
    { value: 'PORTFOLIO_PERFORMANCE', label: 'Portfolio Performance' },
    { value: 'TRANSACTION_HISTORY', label: 'Transaction History' },
    { value: 'TAX_REPORT', label: 'Tax Report' },
    { value: 'RISK_ANALYSIS', label: 'Risk Analysis' },
    { value: 'ALLOCATION_REPORT', label: 'Asset Allocation' },
    { value: 'DIVIDEND_REPORT', label: 'Dividend Report' },
  ];

  const reportFormats = [
    { value: 'PDF', label: 'PDF' },
    { value: 'EXCEL', label: 'Excel' },
    { value: 'CSV', label: 'CSV' },
    { value: 'JSON', label: 'JSON' },
  ];

  return (
    <>
      <Helmet>
        <title>Reports - TrustVault</title>
      </Helmet>

      <Box sx={{ p: 3 }}>
        {/* Header */}
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
          <Typography variant="h4" component="h1">
            Reports
          </Typography>
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={handleCreateReport}
          >
            Generate Report
          </Button>
        </Box>

        {/* Reports Table */}
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Your Reports
            </Typography>
            
            {reportsLoading ? (
              <Box display="flex" justifyContent="center" p={3}>
                <CircularProgress />
              </Box>
            ) : reports.length === 0 ? (
              <Box textAlign="center" py={4}>
                <Assessment sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                <Typography variant="h6" color="text.secondary" gutterBottom>
                  No reports generated yet
                </Typography>
                <Typography variant="body2" color="text.secondary" mb={3}>
                  Generate your first report to analyze your portfolio performance
                </Typography>
                <Button
                  variant="contained"
                  startIcon={<Add />}
                  onClick={handleCreateReport}
                >
                  Generate Your First Report
                </Button>
              </Box>
            ) : (
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Report</TableCell>
                      <TableCell>Type</TableCell>
                      <TableCell>Format</TableCell>
                      <TableCell>Period</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Size</TableCell>
                      <TableCell>Generated</TableCell>
                      <TableCell>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {reports.map((report: Report) => (
                      <TableRow key={report.id}>
                        <TableCell>
                          <Box display="flex" alignItems="center" gap={1}>
                            {getReportTypeIcon(report.report_type)}
                            <Box>
                              <Typography variant="body2" fontWeight="bold">
                                {report.name}
                              </Typography>
                              {report.portfolio_name && (
                                <Typography variant="caption" color="text.secondary">
                                  Portfolio: {report.portfolio_name}
                                </Typography>
                              )}
                            </Box>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {report.report_type.replace('_', ' ')}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Box display="flex" alignItems="center" gap={1}>
                            {getFormatIcon(report.report_format)}
                            <Typography variant="body2">
                              {report.report_format}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {new Date(report.start_date).toLocaleDateString()} - {' '}
                            {new Date(report.end_date).toLocaleDateString()}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Box>
                            <Chip
                              label={report.status}
                              color={getStatusColor(report.status) as any}
                              size="small"
                            />
                            {report.status === 'GENERATING' && (
                              <LinearProgress sx={{ mt: 1, width: 100 }} />
                            )}
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {report.file_size_mb ? `${report.file_size_mb} MB` : '-'}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {report.generated_at 
                              ? new Date(report.generated_at).toLocaleDateString()
                              : '-'
                            }
                          </Typography>
                        </TableCell>
                        <TableCell>
                          {report.status === 'COMPLETED' && (
                            <IconButton
                              size="small"
                              color="primary"
                              onClick={() => handleDownloadReport(report)}
                            >
                              <Download />
                            </IconButton>
                          )}
                          <IconButton
                            size="small"
                            color="error"
                            onClick={() => handleDeleteReport(report)}
                          >
                            <Delete />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            )}
          </CardContent>
        </Card>
      </Box>

      {/* Create Report Dialog */}
      <Dialog
        open={createDialogOpen}
        onClose={() => setCreateDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <form onSubmit={handleSubmit(onSubmit)}>
          <DialogTitle>Generate New Report</DialogTitle>
          <DialogContent>
            <Grid container spacing={3} sx={{ mt: 1 }}>
              <Grid item xs={12}>
                <Controller
                  name="name"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Report Name"
                      fullWidth
                      error={!!errors.name}
                      helperText={errors.name?.message}
                    />
                  )}
                />
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <Controller
                  name="report_type"
                  control={control}
                  render={({ field }) => (
                    <FormControl fullWidth error={!!errors.report_type}>
                      <InputLabel>Report Type</InputLabel>
                      <Select {...field} label="Report Type">
                        {reportTypes.map((type) => (
                          <MenuItem key={type.value} value={type.value}>
                            {type.label}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  )}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <Controller
                  name="report_format"
                  control={control}
                  render={({ field }) => (
                    <FormControl fullWidth>
                      <InputLabel>Format</InputLabel>
                      <Select {...field} label="Format">
                        {reportFormats.map((format) => (
                          <MenuItem key={format.value} value={format.value}>
                            {format.label}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  )}
                />
              </Grid>

              <Grid item xs={12}>
                <Controller
                  name="portfolio"
                  control={control}
                  render={({ field }) => (
                    <FormControl fullWidth>
                      <InputLabel>Portfolio (Optional)</InputLabel>
                      <Select {...field} label="Portfolio (Optional)">
                        <MenuItem value="">All Portfolios</MenuItem>
                        {portfolios.map((portfolio: any) => (
                          <MenuItem key={portfolio.id} value={portfolio.id}>
                            {portfolio.name}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  )}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <Controller
                  name="start_date"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Start Date"
                      type="date"
                      fullWidth
                      InputLabelProps={{ shrink: true }}
                      error={!!errors.start_date}
                      helperText={errors.start_date?.message}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <Controller
                  name="end_date"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="End Date"
                      type="date"
                      fullWidth
                      InputLabelProps={{ shrink: true }}
                      error={!!errors.end_date}
                      helperText={errors.end_date?.message}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12}>
                <Controller
                  name="description"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Description (Optional)"
                      multiline
                      rows={3}
                      fullWidth
                    />
                  )}
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setCreateDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              type="submit"
              variant="contained"
              disabled={createReportMutation.isLoading}
            >
              {createReportMutation.isLoading ? (
                <CircularProgress size={20} />
              ) : (
                'Generate Report'
              )}
            </Button>
          </DialogActions>
        </form>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
      >
        <DialogTitle>Delete Report</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete the report "{reportToDelete?.name}"?
            This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>
            Cancel
          </Button>
          <Button
            color="error"
            variant="contained"
            onClick={() => reportToDelete && deleteReportMutation.mutate(reportToDelete.id)}
            disabled={deleteReportMutation.isLoading}
          >
            {deleteReportMutation.isLoading ? <CircularProgress size={20} /> : 'Delete'}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default ReportsPage;

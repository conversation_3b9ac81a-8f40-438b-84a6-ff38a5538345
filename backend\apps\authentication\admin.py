# TrustVault - Authentication Admin

from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.utils.html import format_html
from .models import User, UserRole, UserRoleAssignment, LoginAttempt, UserSession, PasswordHistory


@admin.register(User)
class UserAdmin(BaseUserAdmin):
    """Admin interface for User model."""
    
    list_display = ['email', 'username', 'first_name', 'last_name', 'is_active', 'is_mfa_enabled', 'account_status', 'date_joined']
    list_filter = ['is_active', 'is_staff', 'is_superuser', 'is_mfa_enabled', 'date_joined']
    search_fields = ['email', 'username', 'first_name', 'last_name']
    ordering = ['-date_joined']
    
    fieldsets = (
        (None, {'fields': ('email', 'username', 'password')}),
        ('Personal info', {'fields': ('first_name', 'last_name', 'phone_number', 'profile_picture')}),
        ('Permissions', {
            'fields': ('is_active', 'is_staff', 'is_superuser', 'groups', 'user_permissions'),
        }),
        ('Security', {
            'fields': ('is_mfa_enabled', 'failed_login_attempts', 'account_locked_until', 'last_password_change', 'password_reset_required')
        }),
        ('Preferences', {
            'fields': ('timezone', 'language')
        }),
        ('Privacy & Consent', {
            'fields': ('gdpr_consent', 'gdpr_consent_date', 'marketing_consent')
        }),
        ('Important dates', {'fields': ('last_login', 'date_joined')}),
        ('Audit Info', {
            'fields': ('last_login_ip', 'last_login_user_agent'),
            'classes': ('collapse',)
        })
    )
    
    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': ('email', 'username', 'password1', 'password2', 'first_name', 'last_name'),
        }),
    )
    
    readonly_fields = ['last_login', 'date_joined', 'last_login_ip', 'last_login_user_agent']
    
    def account_status(self, obj):
        """Display account status with color."""
        if obj.is_account_locked:
            return format_html('<span style="color: red;">🔒 Locked</span>')
        elif not obj.is_active:
            return format_html('<span style="color: orange;">⏸️ Inactive</span>')
        else:
            return format_html('<span style="color: green;">✅ Active</span>')
    account_status.short_description = 'Status'


@admin.register(UserRole)
class UserRoleAdmin(admin.ModelAdmin):
    """Admin interface for UserRole."""
    
    list_display = ['name', 'description', 'created_at']
    search_fields = ['name', 'description']
    readonly_fields = ['id', 'created_at', 'updated_at']
    
    fieldsets = (
        ('Role Information', {
            'fields': ('name', 'description')
        }),
        ('Permissions', {
            'fields': ('permissions',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )


@admin.register(UserRoleAssignment)
class UserRoleAssignmentAdmin(admin.ModelAdmin):
    """Admin interface for UserRoleAssignment."""
    
    list_display = ['user', 'role', 'assigned_by', 'assigned_at', 'expires_at']
    list_filter = ['role', 'assigned_at', 'expires_at']
    search_fields = ['user__email', 'role__name']
    readonly_fields = ['id', 'assigned_at', 'created_at', 'updated_at']
    
    fieldsets = (
        ('Assignment', {
            'fields': ('user', 'role', 'assigned_by', 'expires_at')
        }),
        ('Timestamps', {
            'fields': ('assigned_at', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )


@admin.register(LoginAttempt)
class LoginAttemptAdmin(admin.ModelAdmin):
    """Admin interface for LoginAttempt."""
    
    list_display = ['created_at', 'email_attempted', 'attempt_type', 'ip_address', 'is_suspicious', 'attempt_status']
    list_filter = ['attempt_type', 'is_suspicious', 'created_at']
    search_fields = ['email_attempted', 'ip_address']
    readonly_fields = ['id', 'created_at', 'updated_at']
    date_hierarchy = 'created_at'
    
    fieldsets = (
        ('Attempt Information', {
            'fields': ('user', 'email_attempted', 'attempt_type')
        }),
        ('Source Information', {
            'fields': ('ip_address', 'user_agent', 'location')
        }),
        ('Security', {
            'fields': ('is_suspicious',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    def attempt_status(self, obj):
        """Display attempt status with color."""
        if obj.attempt_type == 'SUCCESS':
            return format_html('<span style="color: green;">✅ Success</span>')
        elif obj.is_suspicious:
            return format_html('<span style="color: red;">🚨 Suspicious</span>')
        else:
            return format_html('<span style="color: orange;">❌ Failed</span>')
    attempt_status.short_description = 'Status'
    
    def has_add_permission(self, request):
        """Disable adding login attempts through admin."""
        return False
    
    def has_change_permission(self, request, obj=None):
        """Disable changing login attempts through admin."""
        return False


@admin.register(UserSession)
class UserSessionAdmin(admin.ModelAdmin):
    """Admin interface for UserSession."""
    
    list_display = ['user', 'session_key_preview', 'ip_address', 'is_active', 'last_activity', 'expires_at']
    list_filter = ['is_active', 'created_at', 'expires_at']
    search_fields = ['user__email', 'ip_address', 'session_key']
    readonly_fields = ['id', 'created_at', 'updated_at', 'last_activity']
    date_hierarchy = 'created_at'
    
    fieldsets = (
        ('Session Information', {
            'fields': ('user', 'session_key', 'is_active')
        }),
        ('Source Information', {
            'fields': ('ip_address', 'user_agent', 'location')
        }),
        ('Timing', {
            'fields': ('last_activity', 'expires_at')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    def session_key_preview(self, obj):
        """Show preview of session key."""
        return f"{obj.session_key[:8]}..."
    session_key_preview.short_description = 'Session Key'


@admin.register(PasswordHistory)
class PasswordHistoryAdmin(admin.ModelAdmin):
    """Admin interface for PasswordHistory."""
    
    list_display = ['user', 'created_at']
    list_filter = ['created_at']
    search_fields = ['user__email']
    readonly_fields = ['id', 'created_at', 'updated_at', 'password_hash']
    date_hierarchy = 'created_at'
    
    fieldsets = (
        ('Password History', {
            'fields': ('user', 'password_hash')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    def has_add_permission(self, request):
        """Disable adding password history through admin."""
        return False
    
    def has_change_permission(self, request, obj=None):
        """Disable changing password history through admin."""
        return False

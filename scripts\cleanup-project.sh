#!/bin/bash

# TrustVault - Script de Nettoyage Automatique
# Ce script nettoie les fichiers temporaires et optimise le projet

set -e

echo "🧹 TrustVault - Nettoyage Automatique du Projet"
echo "================================================"

# Couleurs pour l'affichage
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Fonction d'affichage
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Vérifier si on est dans le bon répertoire
if [ ! -f "docker-compose.yml" ]; then
    log_error "Ce script doit être exécuté depuis la racine du projet TrustVault"
    exit 1
fi

log_info "Démarrage du nettoyage du projet..."

# 1. Nettoyage des fichiers Python
log_info "Nettoyage des fichiers Python..."
find . -type f -name "*.pyc" -delete 2>/dev/null || true
find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
find . -type f -name "*.pyo" -delete 2>/dev/null || true
find . -type f -name "*.pyd" -delete 2>/dev/null || true
log_success "Fichiers Python nettoyés"

# 2. Nettoyage des fichiers de base de données temporaires
log_info "Nettoyage des bases de données temporaires..."
find . -name "db.sqlite3" -delete 2>/dev/null || true
find . -name "db.sqlite3-journal" -delete 2>/dev/null || true
log_success "Bases de données temporaires supprimées"

# 3. Nettoyage des logs
log_info "Nettoyage des fichiers de logs..."
find ./logs -name "*.log" -delete 2>/dev/null || true
find . -name "*.log" -not -path "./node_modules/*" -delete 2>/dev/null || true
log_success "Fichiers de logs nettoyés"

# 4. Nettoyage des fichiers de test temporaires
log_info "Nettoyage des fichiers de test temporaires..."
find . -name "test_*.py" -not -path "./backend/tests/*" -delete 2>/dev/null || true
find . -name "*_test.py" -not -path "./backend/tests/*" -delete 2>/dev/null || true
find . -name "security_api_test_results.json" -delete 2>/dev/null || true
find . -name "security_validation_report.json" -delete 2>/dev/null || true
log_success "Fichiers de test temporaires supprimés"

# 5. Nettoyage des fichiers de cache
log_info "Nettoyage des fichiers de cache..."
find . -type d -name ".cache" -exec rm -rf {} + 2>/dev/null || true
find . -type d -name ".pytest_cache" -exec rm -rf {} + 2>/dev/null || true
find . -type d -name ".coverage" -exec rm -rf {} + 2>/dev/null || true
find . -type d -name "htmlcov" -exec rm -rf {} + 2>/dev/null || true
log_success "Fichiers de cache supprimés"

# 6. Nettoyage des fichiers temporaires
log_info "Nettoyage des fichiers temporaires..."
find . -name "*.tmp" -delete 2>/dev/null || true
find . -name "*.temp" -delete 2>/dev/null || true
find . -name "*.bak" -delete 2>/dev/null || true
find . -name "*.backup" -delete 2>/dev/null || true
find . -name "*~" -delete 2>/dev/null || true
log_success "Fichiers temporaires supprimés"

# 7. Nettoyage des fichiers d'éditeur
log_info "Nettoyage des fichiers d'éditeur..."
find . -name "*.swp" -delete 2>/dev/null || true
find . -name "*.swo" -delete 2>/dev/null || true
find . -name ".*.swp" -delete 2>/dev/null || true
find . -name ".*.swo" -delete 2>/dev/null || true
log_success "Fichiers d'éditeur supprimés"

# 8. Nettoyage des fichiers de build frontend (optionnel)
if [ "$1" = "--full" ]; then
    log_info "Nettoyage complet - Suppression des node_modules et build..."
    rm -rf frontend/node_modules 2>/dev/null || true
    rm -rf frontend/build 2>/dev/null || true
    log_success "Node_modules et build supprimés"
fi

# 9. Nettoyage des volumes Docker orphelins (optionnel)
if [ "$1" = "--docker" ] || [ "$1" = "--full" ]; then
    log_info "Nettoyage des volumes Docker orphelins..."
    docker system prune -f 2>/dev/null || log_warning "Docker non disponible"
    docker volume prune -f 2>/dev/null || log_warning "Impossible de nettoyer les volumes Docker"
    log_success "Volumes Docker nettoyés"
fi

# 10. Optimisation des permissions
log_info "Optimisation des permissions..."
chmod +x scripts/*.sh 2>/dev/null || true
chmod +x deploy-security.sh 2>/dev/null || true
chmod +x run_complete_security_validation.py 2>/dev/null || true
log_success "Permissions optimisées"

# 11. Vérification de l'intégrité
log_info "Vérification de l'intégrité du projet..."

# Vérifier les fichiers essentiels
essential_files=(
    "docker-compose.yml"
    "backend/manage.py"
    "backend/requirements.txt"
    "frontend/package.json"
    "deploy-security.sh"
    "SECURITY_IMPLEMENTATION_GUIDE.md"
    "SECURITY_VALIDATION_REPORT.md"
)

missing_files=()
for file in "${essential_files[@]}"; do
    if [ ! -f "$file" ]; then
        missing_files+=("$file")
    fi
done

if [ ${#missing_files[@]} -eq 0 ]; then
    log_success "Tous les fichiers essentiels sont présents"
else
    log_error "Fichiers essentiels manquants:"
    for file in "${missing_files[@]}"; do
        echo "  - $file"
    done
fi

# 12. Statistiques de nettoyage
log_info "Calcul des statistiques..."

# Compter les fichiers par type
python_files=$(find . -name "*.py" -not -path "./frontend/node_modules/*" | wc -l)
js_files=$(find . -name "*.js" -o -name "*.jsx" -o -name "*.ts" -o -name "*.tsx" | grep -v node_modules | wc -l)
docker_files=$(find . -name "Dockerfile" -o -name "docker-compose*.yml" | wc -l)
config_files=$(find . -name "*.yml" -o -name "*.yaml" -o -name "*.json" -o -name "*.conf" | grep -v node_modules | wc -l)

echo ""
echo "📊 Statistiques du projet après nettoyage:"
echo "  - Fichiers Python: $python_files"
echo "  - Fichiers JS/TS: $js_files"
echo "  - Fichiers Docker: $docker_files"
echo "  - Fichiers de config: $config_files"

# Taille du projet
project_size=$(du -sh . 2>/dev/null | cut -f1)
echo "  - Taille totale: $project_size"

echo ""
log_success "Nettoyage terminé avec succès!"

echo ""
echo "💡 Conseils d'utilisation:"
echo "  - Nettoyage standard: ./scripts/cleanup-project.sh"
echo "  - Nettoyage complet: ./scripts/cleanup-project.sh --full"
echo "  - Avec Docker: ./scripts/cleanup-project.sh --docker"

echo ""
echo "🛡️ TrustVault - Projet optimisé et prêt pour la production!"

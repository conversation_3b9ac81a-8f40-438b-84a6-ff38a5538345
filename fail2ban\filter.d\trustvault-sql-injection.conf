# TrustVault SQL Injection Filter

[Definition]

# SQL injection patterns
failregex = ^<HOST> - .* ".*(union|UNION).*(select|SELECT).*" .*$
            ^<HOST> - .* ".*(\\'|%27).*(or|OR).*(1=1|\\\'1\\\'=\\\'1\\\').*" .*$
            ^<HOST> - .* ".*(drop|DROP).*(table|TABLE).*" .*$
            ^<HOST> - .* ".*(insert|INSERT).*(into|INTO).*" .*$
            ^<HOST> - .* ".*(delete|DELETE).*(from|FROM).*" .*$
            ^<HOST> - .* ".*(update|UPDATE).*(set|SET).*" .*$
            ^<HOST> - .* ".*information_schema.*" .*$
            ^<HOST> - .* ".*(exec|EXEC).*(xp_|sp_).*" .*$
            ^<HOST> - .* ".*(\\'|%27).*(;|%3B).*(--|\\/\\*).*" .*$

# Date pattern
datepattern = ^%%d/%%b/%%Y:%%H:%%M:%%S %%z

# TrustVault - Report Generators

import os
import json
import csv
import hashlib
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, List, Any, Optional
from django.conf import settings
from django.template.loader import render_to_string
from django.utils import timezone
from reportlab.lib import colors
from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, Image
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
import pandas as pd
import logging

from .models import Report
from apps.portfolio.models import Portfolio, Transaction, Holding

logger = logging.getLogger(__name__)


class BaseReportGenerator:
    """Base class for report generators."""
    
    def __init__(self, report: Report):
        self.report = report
        self.user = report.user
        self.portfolio = report.portfolio
        self.start_date = report.start_date
        self.end_date = report.end_date
        self.parameters = report.parameters or {}
        
        # Create reports directory if it doesn't exist
        self.reports_dir = os.path.join(settings.MEDIA_ROOT, 'reports')
        os.makedirs(self.reports_dir, exist_ok=True)
    
    def generate(self) -> str:
        """Generate the report and return file path."""
        try:
            self.report.mark_generating()
            
            # Collect data
            data = self.collect_data()
            
            # Generate file based on format
            if self.report.report_format == 'PDF':
                file_path = self.generate_pdf(data)
            elif self.report.report_format == 'EXCEL':
                file_path = self.generate_excel(data)
            elif self.report.report_format == 'CSV':
                file_path = self.generate_csv(data)
            elif self.report.report_format == 'JSON':
                file_path = self.generate_json(data)
            else:
                raise ValueError(f"Unsupported report format: {self.report.report_format}")
            
            # Calculate file size and hash
            file_size = os.path.getsize(file_path)
            file_hash = self._calculate_file_hash(file_path)
            
            # Mark report as completed
            self.report.mark_completed(file_path, file_size, file_hash)
            
            logger.info(f"Report {self.report.id} generated successfully: {file_path}")
            return file_path
            
        except Exception as e:
            logger.error(f"Failed to generate report {self.report.id}: {str(e)}")
            self.report.mark_failed(str(e))
            raise
    
    def collect_data(self) -> Dict[str, Any]:
        """Collect data for the report. Override in subclasses."""
        raise NotImplementedError("Subclasses must implement collect_data method")
    
    def generate_pdf(self, data: Dict[str, Any]) -> str:
        """Generate PDF report."""
        filename = f"{self.report.report_type}_{self.report.id}.pdf"
        file_path = os.path.join(self.reports_dir, filename)
        
        doc = SimpleDocTemplate(file_path, pagesize=A4)
        styles = getSampleStyleSheet()
        story = []
        
        # Title
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=24,
            spaceAfter=30,
            alignment=1  # Center
        )
        story.append(Paragraph(f"TrustVault {self.report.name}", title_style))
        story.append(Spacer(1, 20))
        
        # Report info
        info_data = [
            ['Report Type:', self.report.get_report_type_display()],
            ['Generated:', timezone.now().strftime('%B %d, %Y at %I:%M %p')],
            ['Period:', f"{self.start_date.strftime('%B %d, %Y')} - {self.end_date.strftime('%B %d, %Y')}"],
            ['User:', self.user.get_full_name() or self.user.email],
        ]
        
        if self.portfolio:
            info_data.append(['Portfolio:', self.portfolio.name])
        
        info_table = Table(info_data, colWidths=[2*inch, 4*inch])
        info_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
        ]))
        story.append(info_table)
        story.append(Spacer(1, 30))
        
        # Add report-specific content
        self._add_pdf_content(story, data, styles)
        
        doc.build(story)
        return file_path
    
    def generate_excel(self, data: Dict[str, Any]) -> str:
        """Generate Excel report."""
        filename = f"{self.report.report_type}_{self.report.id}.xlsx"
        file_path = os.path.join(self.reports_dir, filename)
        
        with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
            # Add report-specific sheets
            self._add_excel_content(writer, data)
        
        return file_path
    
    def generate_csv(self, data: Dict[str, Any]) -> str:
        """Generate CSV report."""
        filename = f"{self.report.report_type}_{self.report.id}.csv"
        file_path = os.path.join(self.reports_dir, filename)
        
        with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
            self._add_csv_content(csvfile, data)
        
        return file_path
    
    def generate_json(self, data: Dict[str, Any]) -> str:
        """Generate JSON report."""
        filename = f"{self.report.report_type}_{self.report.id}.json"
        file_path = os.path.join(self.reports_dir, filename)
        
        # Add metadata
        report_data = {
            'report_info': {
                'id': str(self.report.id),
                'type': self.report.report_type,
                'name': self.report.name,
                'generated_at': timezone.now().isoformat(),
                'period': {
                    'start_date': self.start_date.isoformat(),
                    'end_date': self.end_date.isoformat()
                },
                'user': self.user.email,
                'portfolio': self.portfolio.name if self.portfolio else None
            },
            'data': data
        }
        
        with open(file_path, 'w', encoding='utf-8') as jsonfile:
            json.dump(report_data, jsonfile, indent=2, default=str)
        
        return file_path
    
    def _add_pdf_content(self, story: List, data: Dict[str, Any], styles):
        """Add PDF-specific content. Override in subclasses."""
        story.append(Paragraph("Report content not implemented", styles['Normal']))
    
    def _add_excel_content(self, writer, data: Dict[str, Any]):
        """Add Excel-specific content. Override in subclasses."""
        df = pd.DataFrame([{'message': 'Report content not implemented'}])
        df.to_excel(writer, sheet_name='Report', index=False)
    
    def _add_csv_content(self, csvfile, data: Dict[str, Any]):
        """Add CSV-specific content. Override in subclasses."""
        writer = csv.writer(csvfile)
        writer.writerow(['message'])
        writer.writerow(['Report content not implemented'])
    
    def _calculate_file_hash(self, file_path: str) -> str:
        """Calculate SHA-256 hash of the file."""
        hash_sha256 = hashlib.sha256()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_sha256.update(chunk)
        return hash_sha256.hexdigest()


class PortfolioPerformanceReportGenerator(BaseReportGenerator):
    """Generator for portfolio performance reports."""
    
    def collect_data(self) -> Dict[str, Any]:
        """Collect portfolio performance data."""
        if not self.portfolio:
            raise ValueError("Portfolio is required for portfolio performance report")
        
        # Get transactions in date range
        transactions = Transaction.objects.filter(
            portfolio=self.portfolio,
            transaction_date__range=[self.start_date, self.end_date]
        ).order_by('transaction_date')
        
        # Get current holdings
        holdings = Holding.objects.filter(portfolio=self.portfolio)
        
        # Calculate performance metrics
        total_invested = sum(
            t.quantity * t.price for t in transactions 
            if t.transaction_type == 'BUY'
        )
        
        current_value = sum(
            h.quantity * h.asset.current_price for h in holdings
        )
        
        total_return = current_value - total_invested
        return_percentage = (total_return / total_invested * 100) if total_invested > 0 else 0
        
        return {
            'portfolio': {
                'name': self.portfolio.name,
                'description': self.portfolio.description,
                'created_at': self.portfolio.created_at,
                'total_value': str(self.portfolio.total_value),
                'cash_balance': str(self.portfolio.cash_balance),
            },
            'performance': {
                'total_invested': str(total_invested),
                'current_value': str(current_value),
                'total_return': str(total_return),
                'return_percentage': f"{return_percentage:.2f}%",
            },
            'transactions': [
                {
                    'date': t.transaction_date.isoformat(),
                    'type': t.transaction_type,
                    'asset': t.asset.symbol,
                    'quantity': str(t.quantity),
                    'price': str(t.price),
                    'total': str(t.quantity * t.price),
                }
                for t in transactions
            ],
            'holdings': [
                {
                    'asset': h.asset.symbol,
                    'asset_name': h.asset.name,
                    'quantity': str(h.quantity),
                    'avg_cost': str(h.average_cost),
                    'current_price': str(h.asset.current_price),
                    'market_value': str(h.quantity * h.asset.current_price),
                    'unrealized_gain_loss': str(
                        (h.asset.current_price - h.average_cost) * h.quantity
                    ),
                }
                for h in holdings
            ]
        }
    
    def _add_pdf_content(self, story: List, data: Dict[str, Any], styles):
        """Add portfolio performance content to PDF."""
        # Performance summary
        story.append(Paragraph("Performance Summary", styles['Heading2']))
        
        perf_data = [
            ['Total Invested:', f"${data['performance']['total_invested']}"],
            ['Current Value:', f"${data['performance']['current_value']}"],
            ['Total Return:', f"${data['performance']['total_return']}"],
            ['Return %:', data['performance']['return_percentage']],
        ]
        
        perf_table = Table(perf_data, colWidths=[2*inch, 2*inch])
        perf_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 12),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
        ]))
        story.append(perf_table)
        story.append(Spacer(1, 20))
        
        # Holdings table
        if data['holdings']:
            story.append(Paragraph("Current Holdings", styles['Heading2']))
            
            holdings_data = [['Asset', 'Quantity', 'Avg Cost', 'Current Price', 'Market Value', 'Gain/Loss']]
            for holding in data['holdings']:
                holdings_data.append([
                    holding['asset'],
                    holding['quantity'],
                    f"${holding['avg_cost']}",
                    f"${holding['current_price']}",
                    f"${holding['market_value']}",
                    f"${holding['unrealized_gain_loss']}",
                ])
            
            holdings_table = Table(holdings_data)
            holdings_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, -1), 9),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ]))
            story.append(holdings_table)


class TransactionHistoryReportGenerator(BaseReportGenerator):
    """Generator for transaction history reports."""
    
    def collect_data(self) -> Dict[str, Any]:
        """Collect transaction history data."""
        query = Transaction.objects.filter(
            user=self.user,
            transaction_date__range=[self.start_date, self.end_date]
        )
        
        if self.portfolio:
            query = query.filter(portfolio=self.portfolio)
        
        transactions = query.order_by('-transaction_date')
        
        return {
            'transactions': [
                {
                    'date': t.transaction_date.isoformat(),
                    'portfolio': t.portfolio.name,
                    'type': t.transaction_type,
                    'asset': t.asset.symbol,
                    'asset_name': t.asset.name,
                    'quantity': str(t.quantity),
                    'price': str(t.price),
                    'total': str(t.quantity * t.price),
                    'fees': str(t.fees or 0),
                    'notes': t.notes or '',
                }
                for t in transactions
            ],
            'summary': {
                'total_transactions': transactions.count(),
                'buy_transactions': transactions.filter(transaction_type='BUY').count(),
                'sell_transactions': transactions.filter(transaction_type='SELL').count(),
                'total_volume': str(sum(t.quantity * t.price for t in transactions)),
            }
        }


# Report generator registry
REPORT_GENERATORS = {
    'PORTFOLIO_PERFORMANCE': PortfolioPerformanceReportGenerator,
    'TRANSACTION_HISTORY': TransactionHistoryReportGenerator,
    # Add more generators as needed
}


def generate_report(report_id: str) -> str:
    """Generate a report by ID."""
    try:
        report = Report.objects.get(id=report_id)
        generator_class = REPORT_GENERATORS.get(report.report_type)
        
        if not generator_class:
            raise ValueError(f"No generator found for report type: {report.report_type}")
        
        generator = generator_class(report)
        return generator.generate()
        
    except Report.DoesNotExist:
        raise ValueError(f"Report {report_id} not found")
    except Exception as e:
        logger.error(f"Failed to generate report {report_id}: {str(e)}")
        raise

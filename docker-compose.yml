version: '3.8'

networks:
  frontend:
    driver: bridge
  backend:
    driver: bridge
  security:
    driver: bridge
  database:
    driver: bridge
    internal: true

# Volumes defined at the end of file

services:
  # ============================================================================
  # SECURITY PERIMETER - WAF & REVERSE PROXY
  # ============================================================================
  
  nginx:
    build: ./nginx
    container_name: trustvault-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./nginx/ssl:/etc/nginx/ssl
      - ./logs/nginx:/var/log/nginx
    networks:
      - frontend
      - backend
    depends_on:
      - django
      - react
    restart: unless-stopped
    environment:
      - NGINX_ENVSUBST_TEMPLATE_SUFFIX=.template
    labels:
      - "com.trustvault.service=nginx"
      - "com.trustvault.security.level=high"

  # ModSecurity WAF
  modsecurity:
    image: owasp/modsecurity-crs:nginx
    container_name: trustvault-waf
    ports:
      - "8082:80"
    volumes:
      - ./modsecurity/conf:/etc/modsecurity.d
      - ./logs/modsecurity:/var/log/modsec
    networks:
      - frontend
    environment:
      - PARANOIA=2
      - ANOMALY_INBOUND=5
      - ANOMALY_OUTBOUND=4
    restart: unless-stopped

  # Fail2Ban for DDoS Protection
  fail2ban:
    image: crazymax/fail2ban:latest
    container_name: trustvault-fail2ban
    network_mode: "host"
    cap_add:
      - NET_ADMIN
      - NET_RAW
    volumes:
      - ./fail2ban:/data
      - ./logs:/var/log:ro
    environment:
      - TZ=Europe/Paris
      - F2B_LOG_LEVEL=INFO
    restart: unless-stopped

  # ============================================================================
  # APPLICATION LAYER
  # ============================================================================
  
  django:
    build: ./backend
    container_name: trustvault-django
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
      - django_media:/app/media
      - django_static:/app/staticfiles
      - ./logs/django:/app/logs
    networks:
      - backend
      - database
      - security
    environment:
      - DEBUG=${DEBUG:-False}
      - DJANGO_SECRET_KEY=${DJANGO_SECRET_KEY}
      - DB_PASSWORD=${DB_PASSWORD}
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - VAULT_ROOT_TOKEN=${VAULT_ROOT_TOKEN}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
      - ENCRYPTION_KEY=${ENCRYPTION_KEY}
      - POSTGRES_DB=trustvault
      - POSTGRES_USER=trustvault
      - DB_HOST=postgres
      - DB_PORT=5432
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - VAULT_URL=http://vault:8200
    depends_on:
      - postgres
      - redis
      - vault
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health/"]
      interval: 30s
      timeout: 10s
      retries: 3
    labels:
      - "com.trustvault.service=django"
      - "com.trustvault.security.level=high"

  react:
    build: ./frontend
    container_name: trustvault-react
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    networks:
      - frontend
    environment:
      - REACT_APP_API_URL=https://api.trustvault.local
      - REACT_APP_ENVIRONMENT=production
    restart: unless-stopped

  # Celery Worker
  celery-worker:
    build: ./backend
    container_name: trustvault-celery-worker
    command: celery -A trustvault worker -l info
    volumes:
      - ./backend:/app
      - ./logs/celery:/app/logs
    networks:
      - backend
      - database
    environment:
      - DEBUG=${DEBUG:-False}
      - DJANGO_SECRET_KEY=${DJANGO_SECRET_KEY}
      - DB_PASSWORD=${DB_PASSWORD}
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - VAULT_ROOT_TOKEN=${VAULT_ROOT_TOKEN}
      - POSTGRES_DB=trustvault
      - POSTGRES_USER=trustvault
      - DB_HOST=postgres
      - DB_PORT=5432
    depends_on:
      - postgres
      - redis
    restart: unless-stopped

  # Celery Beat (Scheduler)
  celery-beat:
    build: ./backend
    container_name: trustvault-celery-beat
    command: celery -A trustvault beat -l info
    volumes:
      - ./backend:/app
      - ./logs/celery:/app/logs
    networks:
      - backend
      - database
    environment:
      - DEBUG=${DEBUG:-False}
      - DJANGO_SECRET_KEY=${DJANGO_SECRET_KEY}
      - DB_PASSWORD=${DB_PASSWORD}
      - REDIS_PASSWORD=${REDIS_PASSWORD}
    depends_on:
      - postgres
      - redis
    restart: unless-stopped

  # ============================================================================
  # DATA LAYER - ENCRYPTED DATABASES
  # ============================================================================
  
  postgres:
    image: postgres:15-alpine
    container_name: trustvault-postgres
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./postgres/init:/docker-entrypoint-initdb.d
    networks:
      - database
    environment:
      - POSTGRES_DB=trustvault
      - POSTGRES_USER=trustvault
      - POSTGRES_PASSWORD_FILE=/run/secrets/db_password
      - POSTGRES_INITDB_ARGS=--auth-host=scram-sha-256
    secrets:
      - db_password
    command: >
      postgres
      -c ssl=off
      -c log_statement=all
      -c log_destination=stderr
    restart: unless-stopped
    labels:
      - "com.trustvault.service=postgres"
      - "com.trustvault.security.level=critical"

  redis:
    image: redis:7-alpine
    container_name: trustvault-redis
    volumes:
      - redis_data:/data
      - ./redis/redis.conf:/usr/local/etc/redis/redis.conf
    networks:
      - backend
    command: redis-server /usr/local/etc/redis/redis.conf --requirepass ${REDIS_PASSWORD}
    restart: unless-stopped

  # PostgreSQL Exporter
  postgres-exporter:
    image: prometheuscommunity/postgres-exporter:latest
    container_name: trustvault-postgres-exporter
    ports:
      - "9187:9187"
    networks:
      - database
      - security
    environment:
      - DATA_SOURCE_NAME=postgresql://trustvault:${DB_PASSWORD}@postgres:5432/trustvault?sslmode=disable
    depends_on:
      - postgres
    restart: unless-stopped

  # Redis Exporter
  redis-exporter:
    image: oliver006/redis_exporter:latest
    container_name: trustvault-redis-exporter
    ports:
      - "9121:9121"
    networks:
      - backend
      - security
    environment:
      - REDIS_ADDR=redis://redis:6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
    depends_on:
      - redis
    restart: unless-stopped

  # Nginx Exporter
  nginx-exporter:
    image: nginx/nginx-prometheus-exporter:latest
    container_name: trustvault-nginx-exporter
    ports:
      - "9113:9113"
    networks:
      - frontend
      - security
    command:
      - '-nginx.scrape-uri=http://nginx:8080/nginx_status'
    depends_on:
      - nginx
    restart: unless-stopped

  # HashiCorp Vault for Secrets Management
  vault:
    image: hashicorp/vault:latest
    container_name: trustvault-vault
    cap_add:
      - IPC_LOCK
    volumes:
      - vault_data:/vault/data
      - ./vault/config:/vault/config
    networks:
      - backend
    environment:
      - VAULT_DEV_ROOT_TOKEN_ID=${VAULT_ROOT_TOKEN}
      - VAULT_DEV_LISTEN_ADDRESS=0.0.0.0:8200
    command: vault server -config=/vault/config/vault.hcl
    restart: unless-stopped

  # ============================================================================
  # SECURITY & MONITORING - SIEM/SOC
  # ============================================================================
  
  # Wazuh Manager (SIEM)
  wazuh-manager:
    image: wazuh/wazuh-manager:4.7.0
    container_name: trustvault-wazuh-manager
    hostname: wazuh-manager
    volumes:
      - wazuh_data:/var/ossec/data
      - ./wazuh/config:/wazuh-config-mount
      - ./logs:/var/log/external:ro
    networks:
      - security
    environment:
      - INDEXER_URL=https://wazuh-indexer:9200
      - INDEXER_USERNAME=admin
      - INDEXER_PASSWORD=${WAZUH_PASSWORD}
      - FILEBEAT_SSL_VERIFICATION_MODE=full
    restart: unless-stopped

  # Wazuh Indexer (Elasticsearch)
  wazuh-indexer:
    image: wazuh/wazuh-indexer:4.7.0
    container_name: trustvault-wazuh-indexer
    hostname: wazuh-indexer
    volumes:
      - elasticsearch_data:/var/lib/wazuh-indexer
      - ./wazuh/indexer:/usr/share/wazuh-indexer/config
    networks:
      - security
    environment:
      - "OPENSEARCH_JAVA_OPTS=-Xms1g -Xmx1g"
      - bootstrap.memory_lock=true
    ulimits:
      memlock:
        soft: -1
        hard: -1
      nofile:
        soft: 65536
        hard: 65536
    restart: unless-stopped

  # Wazuh Dashboard
  wazuh-dashboard:
    image: wazuh/wazuh-dashboard:4.7.0
    container_name: trustvault-wazuh-dashboard
    hostname: wazuh-dashboard
    ports:
      - "5601:5601"
    networks:
      - security
      - frontend
    environment:
      - INDEXER_USERNAME=admin
      - INDEXER_PASSWORD=${WAZUH_PASSWORD}
      - WAZUH_API_URL=https://wazuh-manager
      - API_USERNAME=wazuh-wui
      - API_PASSWORD=${WAZUH_API_PASSWORD}
    depends_on:
      - wazuh-indexer
      - wazuh-manager
    restart: unless-stopped

  # Suricata IDS/IPS
  suricata:
    image: jasonish/suricata:latest
    container_name: trustvault-suricata
    network_mode: host
    cap_add:
      - NET_ADMIN
      - SYS_NICE
    volumes:
      - ./suricata/config:/etc/suricata
      - ./suricata/rules:/var/lib/suricata/rules
      - ./logs/suricata:/var/log/suricata
    command: >
      suricata -c /etc/suricata/suricata.yaml -i eth0 -v
    restart: unless-stopped

  # ============================================================================
  # MONITORING & METRICS
  # ============================================================================

  prometheus:
    image: prom/prometheus:latest
    container_name: trustvault-prometheus
    ports:
      - "9090:9090"
    volumes:
      - prometheus_data:/prometheus
      - ./prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - ./prometheus/security_rules.yml:/etc/prometheus/security_rules.yml
    networks:
      - security
      - backend
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    restart: unless-stopped

  grafana:
    image: grafana/grafana:latest
    container_name: trustvault-grafana
    ports:
      - "3001:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/provisioning:/etc/grafana/provisioning
      - ./grafana/dashboards:/var/lib/grafana/dashboards
    networks:
      - security
      - frontend
    environment:
      - GF_SECURITY_ADMIN_USER=${GRAFANA_USER}
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_INSTALL_PLUGINS=grafana-piechart-panel,grafana-worldmap-panel
    restart: unless-stopped

  # AlertManager for notifications
  alertmanager:
    image: prom/alertmanager:latest
    container_name: trustvault-alertmanager
    ports:
      - "9093:9093"
    volumes:
      - ./prometheus/alertmanager.yml:/etc/alertmanager/alertmanager.yml
    networks:
      - security
    command:
      - '--config.file=/etc/alertmanager/alertmanager.yml'
      - '--storage.path=/alertmanager'
      - '--web.external-url=http://localhost:9093'
    restart: unless-stopped

  # Node Exporter for system metrics
  node-exporter:
    image: prom/node-exporter:latest
    container_name: trustvault-node-exporter
    ports:
      - "9100:9100"
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    networks:
      - security
    command:
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    restart: unless-stopped

  # cAdvisor for container metrics
  cadvisor:
    image: gcr.io/cadvisor/cadvisor:latest
    container_name: trustvault-cadvisor
    ports:
      - "8081:8080"
    volumes:
      - /:/rootfs:ro
      - /var/run:/var/run:ro
      - /sys:/sys:ro
      - /var/lib/docker/:/var/lib/docker:ro
      - /dev/disk/:/dev/disk:ro
    networks:
      - security
    privileged: true
    devices:
      - /dev/kmsg
    restart: unless-stopped

  # Blackbox Exporter for endpoint monitoring
  blackbox-exporter:
    image: prom/blackbox-exporter:latest
    container_name: trustvault-blackbox-exporter
    ports:
      - "9115:9115"
    volumes:
      - ./prometheus/blackbox.yml:/etc/blackbox_exporter/config.yml
    networks:
      - security
      - frontend
    restart: unless-stopped

# ============================================================================
# VOLUMES
# ============================================================================

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  elasticsearch_data:
    driver: local
  wazuh_data:
    driver: local
  vault_data:
    driver: local
  vault_logs:
    driver: local
  wazuh_api_configuration:
    driver: local
  wazuh_etc:
    driver: local
  wazuh_logs:
    driver: local
  wazuh_queue:
    driver: local
  wazuh_var_multigroups:
    driver: local
  wazuh_integrations:
    driver: local
  wazuh_active_response:
    driver: local
  wazuh_agentless:
    driver: local
  wazuh_wodles:
    driver: local
  filebeat_etc:
    driver: local
  filebeat_var:
    driver: local
  wazuh_indexer_data:
    driver: local
  wazuh_dashboard_config:
    driver: local
  wazuh_dashboard_custom:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  django_media:
    driver: local
  django_static:
    driver: local

secrets:
  django_secret:
    file: ./secrets/django_secret.txt
  db_password:
    file: ./secrets/db_password.txt

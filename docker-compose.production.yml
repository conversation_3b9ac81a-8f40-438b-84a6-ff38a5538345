version: '3.8'

networks:
  frontend:
    driver: bridge
  backend:
    driver: bridge
  security:
    driver: bridge

volumes:
  postgres_data:
  redis_data:
  prometheus_data:
  grafana_data:
  django_media:
  django_static:

services:
  # Nginx Reverse Proxy with SSL
  nginx:
    image: nginx:alpine
    container_name: trustvault-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./nginx/ssl:/etc/nginx/ssl
      - ./nginx/security-headers.conf:/etc/nginx/conf.d/security-headers.conf
    networks:
      - frontend
      - backend
    depends_on:
      - django
      - react
    restart: unless-stopped

  # Database
  postgres:
    image: postgres:15-alpine
    container_name: trustvault-postgres
    environment:
      - POSTGRES_DB=trustvault
      - POSTGRES_USER=trustvault
      - POSTGRES_PASSWORD=SecureDBPassword123!TrustVault2024
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./postgres/init:/docker-entrypoint-initdb.d
    networks:
      - backend
    ports:
      - "5432:5432"
    restart: unless-stopped

  # Redis
  redis:
    image: redis:7-alpine
    container_name: trustvault-redis
    command: redis-server --requirepass SecureRedisPassword123!TrustVault2024
    volumes:
      - redis_data:/data
    networks:
      - backend
    ports:
      - "6379:6379"
    restart: unless-stopped

  # Django Backend
  django:
    build: ./backend
    container_name: trustvault-django
    environment:
      - DEBUG=False
      - DJANGO_SECRET_KEY=django-secure-production-key-2024-trustvault-change-this
      - DB_PASSWORD=SecureDBPassword123!TrustVault2024
      - REDIS_PASSWORD=SecureRedisPassword123!TrustVault2024
      - POSTGRES_DB=trustvault
      - POSTGRES_USER=trustvault
      - DB_HOST=postgres
      - DB_PORT=5432
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - ALLOWED_HOSTS=trustvault.local,api.trustvault.local,localhost
    volumes:
      - ./backend:/app
      - ./logs/django:/app/logs
      - django_media:/app/media
      - django_static:/app/staticfiles
    networks:
      - backend
      - frontend
    expose:
      - "8000"
    depends_on:
      - postgres
      - redis
    restart: unless-stopped

  # React Frontend
  react:
    build: ./frontend
    container_name: trustvault-react
    networks:
      - frontend
    environment:
      - REACT_APP_API_URL=https://api.trustvault.local
      - REACT_APP_ENVIRONMENT=production
    expose:
      - "80"
    restart: unless-stopped

  # Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: trustvault-prometheus
    ports:
      - "9090:9090"
    volumes:
      - prometheus_data:/prometheus
      - ./prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - ./prometheus/security_rules.yml:/etc/prometheus/security_rules.yml
    networks:
      - backend
      - security
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    restart: unless-stopped

  # Grafana
  grafana:
    image: grafana/grafana:latest
    container_name: trustvault-grafana
    ports:
      - "3001:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/provisioning:/etc/grafana/provisioning
      - ./grafana/dashboards:/var/lib/grafana/dashboards
    networks:
      - backend
      - security
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_SERVER_PROTOCOL=https
      - GF_SERVER_CERT_FILE=/etc/grafana/ssl/server.crt
      - GF_SERVER_CERT_KEY=/etc/grafana/ssl/server.key
    restart: unless-stopped

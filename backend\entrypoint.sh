#!/bin/bash

# TrustVault - Django Entrypoint Script

set -e

# Wait for database to be ready
echo "Waiting for database..."
while ! nc -z postgres 5432; do
  sleep 0.1
done
echo "Database is ready!"

# Wait for <PERSON>is to be ready
echo "Waiting for Redis..."
while ! nc -z redis 6379; do
  sleep 0.1
done
echo "Redis is ready!"

# Run database migrations
echo "Running database migrations..."
python manage.py migrate --noinput

# Collect static files (disabled for now)
# echo "Collecting static files..."
# python manage.py collectstatic --noinput

# Create superuser if it doesn't exist
echo "Creating superuser..."
python manage.py shell << EOF
from django.contrib.auth import get_user_model
User = get_user_model()
if not User.objects.filter(email='<EMAIL>').exists():
    User.objects.create_superuser(
        email='<EMAIL>',
        username='admin',
        password='admin123',
        first_name='Admin',
        last_name='User'
    )
    print('Superuser created successfully')
else:
    print('Superuser already exists')
EOF

# Load initial data if needed
echo "Loading initial data..."
python manage.py loaddata fixtures/initial_data.json || echo "No initial data to load"

# Start the application
echo "Starting Django application..."
exec "$@"

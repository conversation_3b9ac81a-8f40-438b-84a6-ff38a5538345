// TrustVault - Register Page

import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import {
  Box,
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  FormControlLabel,
  Checkbox,
  Alert,
  InputAdornment,
  IconButton,
  Container,
  LinearProgress,
} from '@mui/material';
import {
  Visibility,
  VisibilityOff,
  Security,
  Email,
  Lock,
  Person,
  Phone,
} from '@mui/icons-material';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { toast } from 'react-hot-toast';
import { Helmet } from 'react-helmet-async';

// Store
import { useAuthStore } from '../../store/authStore';

// Types
import { RegisterData } from '../../types';

// Utils
import { validatePasswordStrength } from '../../utils/security';

// Validation schema
const registerSchema = yup.object({
  email: yup
    .string()
    .email('Invalid email format')
    .required('Email is required'),
  username: yup
    .string()
    .min(3, 'Username must be at least 3 characters')
    .max(30, 'Username must be less than 30 characters')
    .matches(/^[a-zA-Z0-9_]+$/, 'Username can only contain letters, numbers, and underscores')
    .required('Username is required'),
  first_name: yup
    .string()
    .min(2, 'First name must be at least 2 characters')
    .required('First name is required'),
  last_name: yup
    .string()
    .min(2, 'Last name must be at least 2 characters')
    .required('Last name is required'),
  phone_number: yup
    .string()
    .matches(/^\+?[1-9]\d{1,14}$/, 'Invalid phone number format')
    .optional(),
  password: yup
    .string()
    .test('password-strength', 'Password does not meet security requirements', (value) => {
      if (!value) return false;
      const { isValid } = validatePasswordStrength(value);
      return isValid;
    })
    .required('Password is required'),
  password_confirm: yup
    .string()
    .oneOf([yup.ref('password')], 'Passwords must match')
    .required('Password confirmation is required'),
  gdpr_consent: yup
    .boolean()
    .oneOf([true], 'You must accept the privacy policy')
    .required('GDPR consent is required'),
  marketing_consent: yup.boolean().optional(),
});

const RegisterPage: React.FC = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [showPasswordConfirm, setShowPasswordConfirm] = useState(false);
  const [passwordStrength, setPasswordStrength] = useState({ score: 0, errors: [] as string[] });
  
  const { register: registerUser, isLoading, error, clearError } = useAuthStore();
  const navigate = useNavigate();

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm<RegisterData>({
    resolver: yupResolver(registerSchema),
    defaultValues: {
      email: '',
      username: '',
      first_name: '',
      last_name: '',
      phone_number: '',
      password: '',
      password_confirm: '',
      gdpr_consent: false,
      marketing_consent: false,
    },
  });

  const watchPassword = watch('password');

  React.useEffect(() => {
    if (watchPassword) {
      const strength = validatePasswordStrength(watchPassword);
      setPasswordStrength(strength);
    } else {
      setPasswordStrength({ score: 0, errors: [] });
    }
  }, [watchPassword]);

  const onSubmit = async (data: RegisterData) => {
    try {
      clearError();
      await registerUser(data);
      toast.success('Registration successful! Please log in.');
      navigate('/login');
    } catch (error: any) {
      // Error is handled by the store and displayed below
    }
  };

  const getPasswordStrengthColor = (score: number) => {
    if (score <= 1) return 'error';
    if (score <= 3) return 'warning';
    return 'success';
  };

  const getPasswordStrengthText = (score: number) => {
    if (score <= 1) return 'Weak';
    if (score <= 3) return 'Medium';
    return 'Strong';
  };

  return (
    <>
      <Helmet>
        <title>Register - TrustVault</title>
        <meta name="description" content="Create your secure TrustVault account" />
      </Helmet>

      <Container maxWidth="sm">
        <Box
          display="flex"
          flexDirection="column"
          alignItems="center"
          justifyContent="center"
          minHeight="100vh"
          py={4}
        >
          {/* Logo and Title */}
          <Box textAlign="center" mb={4}>
            <Security
              sx={{
                fontSize: 64,
                color: 'primary.main',
                mb: 2,
              }}
            />
            <Typography variant="h3" component="h1" gutterBottom>
              TrustVault
            </Typography>
            <Typography variant="h6" color="text.secondary">
              Create Your Secure Account
            </Typography>
          </Box>

          {/* Registration Form */}
          <Card sx={{ width: '100%', maxWidth: 500 }}>
            <CardContent sx={{ p: 4 }}>
              <Typography variant="h4" component="h2" textAlign="center" mb={3}>
                Sign Up
              </Typography>

              {error && (
                <Alert severity="error" sx={{ mb: 3 }}>
                  {error}
                </Alert>
              )}

              <Box component="form" onSubmit={handleSubmit(onSubmit)}>
                {/* Personal Information */}
                <Box display="flex" gap={2} mb={2}>
                  <TextField
                    {...register('first_name')}
                    fullWidth
                    label="First Name"
                    autoComplete="given-name"
                    error={!!errors.first_name}
                    helperText={errors.first_name?.message}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Person color="action" />
                        </InputAdornment>
                      ),
                    }}
                  />

                  <TextField
                    {...register('last_name')}
                    fullWidth
                    label="Last Name"
                    autoComplete="family-name"
                    error={!!errors.last_name}
                    helperText={errors.last_name?.message}
                  />
                </Box>

                <TextField
                  {...register('email')}
                  fullWidth
                  label="Email Address"
                  type="email"
                  autoComplete="email"
                  margin="normal"
                  error={!!errors.email}
                  helperText={errors.email?.message}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <Email color="action" />
                      </InputAdornment>
                    ),
                  }}
                />

                <TextField
                  {...register('username')}
                  fullWidth
                  label="Username"
                  autoComplete="username"
                  margin="normal"
                  error={!!errors.username}
                  helperText={errors.username?.message}
                />

                <TextField
                  {...register('phone_number')}
                  fullWidth
                  label="Phone Number (Optional)"
                  type="tel"
                  autoComplete="tel"
                  margin="normal"
                  error={!!errors.phone_number}
                  helperText={errors.phone_number?.message}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <Phone color="action" />
                      </InputAdornment>
                    ),
                  }}
                />

                {/* Password Fields */}
                <TextField
                  {...register('password')}
                  fullWidth
                  label="Password"
                  type={showPassword ? 'text' : 'password'}
                  autoComplete="new-password"
                  margin="normal"
                  error={!!errors.password}
                  helperText={errors.password?.message}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <Lock color="action" />
                      </InputAdornment>
                    ),
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton
                          onClick={() => setShowPassword(!showPassword)}
                          edge="end"
                        >
                          {showPassword ? <VisibilityOff /> : <Visibility />}
                        </IconButton>
                      </InputAdornment>
                    ),
                  }}
                />

                {/* Password Strength Indicator */}
                {watchPassword && (
                  <Box mt={1} mb={2}>
                    <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                      <Typography variant="body2" color="text.secondary">
                        Password Strength
                      </Typography>
                      <Typography
                        variant="body2"
                        color={`${getPasswordStrengthColor(passwordStrength.score)}.main`}
                      >
                        {getPasswordStrengthText(passwordStrength.score)}
                      </Typography>
                    </Box>
                    <LinearProgress
                      variant="determinate"
                      value={(passwordStrength.score / 5) * 100}
                      color={getPasswordStrengthColor(passwordStrength.score)}
                      sx={{ height: 6, borderRadius: 3 }}
                    />
                    {passwordStrength.errors.length > 0 && (
                      <Box mt={1}>
                        {passwordStrength.errors.map((error, index) => (
                          <Typography key={index} variant="caption" color="error" display="block">
                            • {error}
                          </Typography>
                        ))}
                      </Box>
                    )}
                  </Box>
                )}

                <TextField
                  {...register('password_confirm')}
                  fullWidth
                  label="Confirm Password"
                  type={showPasswordConfirm ? 'text' : 'password'}
                  autoComplete="new-password"
                  margin="normal"
                  error={!!errors.password_confirm}
                  helperText={errors.password_confirm?.message}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <Lock color="action" />
                      </InputAdornment>
                    ),
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton
                          onClick={() => setShowPasswordConfirm(!showPasswordConfirm)}
                          edge="end"
                        >
                          {showPasswordConfirm ? <VisibilityOff /> : <Visibility />}
                        </IconButton>
                      </InputAdornment>
                    ),
                  }}
                />

                {/* Consent Checkboxes */}
                <FormControlLabel
                  control={
                    <Checkbox
                      {...register('gdpr_consent')}
                      color="primary"
                    />
                  }
                  label={
                    <Typography variant="body2">
                      I agree to the{' '}
                      <Link to="/privacy" style={{ color: 'inherit' }}>
                        Privacy Policy
                      </Link>{' '}
                      and{' '}
                      <Link to="/terms" style={{ color: 'inherit' }}>
                        Terms of Service
                      </Link>
                    </Typography>
                  }
                  sx={{ mt: 2, alignItems: 'flex-start' }}
                />
                {errors.gdpr_consent && (
                  <Typography variant="caption" color="error">
                    {errors.gdpr_consent.message}
                  </Typography>
                )}

                <FormControlLabel
                  control={
                    <Checkbox
                      {...register('marketing_consent')}
                      color="primary"
                    />
                  }
                  label="I would like to receive marketing communications"
                  sx={{ mt: 1, mb: 2 }}
                />

                <Button
                  type="submit"
                  fullWidth
                  variant="contained"
                  size="large"
                  disabled={isLoading}
                  sx={{ mt: 2, mb: 2, py: 1.5 }}
                >
                  {isLoading ? 'Creating Account...' : 'Create Account'}
                </Button>

                <Box textAlign="center" mt={2}>
                  <Typography variant="body2" color="text.secondary">
                    Already have an account?{' '}
                    <Link
                      to="/login"
                      style={{
                        color: 'inherit',
                        textDecoration: 'none',
                        fontWeight: 600,
                      }}
                    >
                      Sign in
                    </Link>
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Box>
      </Container>
    </>
  );
};

export default RegisterPage;

# TrustVault - Alerts Services

import logging
from typing import Dict, List, Optional
from django.conf import settings
from django.core.mail import send_mail
from django.template.loader import render_to_string
from django.utils import timezone
from .models import Notification, NotificationPreference, PriceAlert, AlertHistory
from .metrics import (
    record_alert_triggered, record_notification_sent,
    notification_delivery_time, update_active_alerts_gauge
)

logger = logging.getLogger(__name__)


class NotificationService:
    """Service for sending notifications through various channels."""
    
    def __init__(self):
        self.email_enabled = getattr(settings, 'EMAIL_NOTIFICATIONS_ENABLED', True)
        self.sms_enabled = getattr(settings, 'SMS_NOTIFICATIONS_ENABLED', False)
        self.push_enabled = getattr(settings, 'PUSH_NOTIFICATIONS_ENABLED', False)
    
    def send_alert_notification(self, alert: PriceAlert, triggered_value: float) -> Dict[str, bool]:
        """Send notification for a triggered alert."""
        user = alert.user
        preferences = self._get_user_preferences(user)
        
        if not preferences:
            logger.warning(f"No notification preferences found for user {user.id}")
            return {}
        
        # Check if user wants this type of alert
        if not self._should_send_alert(alert, preferences):
            logger.info(f"Alert notifications disabled for user {user.id}")
            return {}
        
        # Check quiet hours
        if preferences.is_quiet_hours():
            logger.info(f"Skipping notification due to quiet hours for user {user.id}")
            return {}
        
        results = {}
        
        # Prepare notification content
        context = {
            'user': user,
            'alert': alert,
            'triggered_value': triggered_value,
            'asset_symbol': alert.asset.symbol if alert.asset else None,
            'portfolio_name': alert.portfolio.name if alert.portfolio else None,
        }
        
        # Send through enabled channels
        for channel in alert.notification_channels:
            if channel == 'EMAIL' and preferences.email_enabled and self.email_enabled:
                results['email'] = self._send_email_notification(user, alert, context, preferences)
            elif channel == 'SMS' and preferences.sms_enabled and self.sms_enabled:
                results['sms'] = self._send_sms_notification(user, alert, context, preferences)
            elif channel == 'PUSH' and preferences.push_enabled and self.push_enabled:
                results['push'] = self._send_push_notification(user, alert, context, preferences)
            elif channel == 'IN_APP' and preferences.in_app_enabled:
                results['in_app'] = self._send_in_app_notification(user, alert, context, preferences)
        
        return results
    
    def _get_user_preferences(self, user) -> Optional[NotificationPreference]:
        """Get user notification preferences."""
        try:
            return NotificationPreference.objects.get(user=user)
        except NotificationPreference.DoesNotExist:
            # Create default preferences
            return NotificationPreference.objects.create(
                user=user,
                email_address=user.email
            )
    
    def _should_send_alert(self, alert: PriceAlert, preferences: NotificationPreference) -> bool:
        """Check if alert should be sent based on user preferences."""
        if alert.alert_type in ['PRICE_ABOVE', 'PRICE_BELOW', 'PRICE_CHANGE', 'VOLUME_SPIKE']:
            return preferences.price_alerts_enabled
        elif alert.alert_type in ['PORTFOLIO_VALUE', 'PORTFOLIO_CHANGE']:
            return preferences.portfolio_alerts_enabled
        elif alert.alert_type in ['NEWS_ALERT', 'EARNINGS_ALERT']:
            return preferences.news_alerts_enabled
        return True
    
    def _send_email_notification(self, user, alert: PriceAlert, context: Dict, preferences: NotificationPreference) -> bool:
        """Send email notification."""
        try:
            recipient = preferences.email_address or user.email
            subject = f"TrustVault Alert: {alert.name}"
            
            # Render email template
            html_message = render_to_string('alerts/email/alert_notification.html', context)
            text_message = render_to_string('alerts/email/alert_notification.txt', context)
            
            # Send email
            send_mail(
                subject=subject,
                message=text_message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[recipient],
                html_message=html_message,
                fail_silently=False
            )
            
            # Log notification
            notification = Notification.objects.create(
                user=user,
                alert=alert,
                channel='EMAIL',
                recipient=recipient,
                subject=subject,
                message=text_message,
                status='SENT',
                sent_at=timezone.now()
            )
            
            logger.info(f"Email notification sent to {recipient} for alert {alert.id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send email notification: {str(e)}")
            
            # Log failed notification
            Notification.objects.create(
                user=user,
                alert=alert,
                channel='EMAIL',
                recipient=preferences.email_address or user.email,
                subject=f"TrustVault Alert: {alert.name}",
                message="Failed to send",
                status='FAILED',
                error_message=str(e)
            )
            
            return False
    
    def _send_sms_notification(self, user, alert: PriceAlert, context: Dict, preferences: NotificationPreference) -> bool:
        """Send SMS notification."""
        if not preferences.phone_number:
            logger.warning(f"No phone number for user {user.id}")
            return False
        
        try:
            # TODO: Implement SMS sending with Twilio or similar service
            message = f"TrustVault Alert: {alert.name} - {alert.asset.symbol if alert.asset else alert.portfolio.name} triggered at {context['triggered_value']}"
            
            # Placeholder for SMS sending
            logger.info(f"SMS would be sent to {preferences.phone_number}: {message}")
            
            # Log notification
            notification = Notification.objects.create(
                user=user,
                alert=alert,
                channel='SMS',
                recipient=preferences.phone_number,
                subject="TrustVault Alert",
                message=message,
                status='SENT',
                sent_at=timezone.now()
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to send SMS notification: {str(e)}")
            
            # Log failed notification
            Notification.objects.create(
                user=user,
                alert=alert,
                channel='SMS',
                recipient=preferences.phone_number,
                subject="TrustVault Alert",
                message="Failed to send",
                status='FAILED',
                error_message=str(e)
            )
            
            return False
    
    def _send_push_notification(self, user, alert: PriceAlert, context: Dict, preferences: NotificationPreference) -> bool:
        """Send push notification."""
        try:
            # TODO: Implement push notifications with Firebase or similar service
            title = f"TrustVault Alert: {alert.name}"
            body = f"{alert.asset.symbol if alert.asset else alert.portfolio.name} triggered at {context['triggered_value']}"
            
            # Placeholder for push notification sending
            logger.info(f"Push notification would be sent to user {user.id}: {title} - {body}")
            
            # Log notification
            notification = Notification.objects.create(
                user=user,
                alert=alert,
                channel='PUSH',
                recipient=f"user_{user.id}",
                subject=title,
                message=body,
                status='SENT',
                sent_at=timezone.now()
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to send push notification: {str(e)}")
            
            # Log failed notification
            Notification.objects.create(
                user=user,
                alert=alert,
                channel='PUSH',
                recipient=f"user_{user.id}",
                subject=f"TrustVault Alert: {alert.name}",
                message="Failed to send",
                status='FAILED',
                error_message=str(e)
            )
            
            return False
    
    def _send_in_app_notification(self, user, alert: PriceAlert, context: Dict, preferences: NotificationPreference) -> bool:
        """Send in-app notification."""
        try:
            title = f"Alert: {alert.name}"
            message = f"{alert.asset.symbol if alert.asset else alert.portfolio.name} triggered at {context['triggered_value']}"
            
            # Log notification (in-app notifications are just database records)
            notification = Notification.objects.create(
                user=user,
                alert=alert,
                channel='IN_APP',
                recipient=f"user_{user.id}",
                subject=title,
                message=message,
                status='DELIVERED',
                sent_at=timezone.now(),
                delivered_at=timezone.now()
            )
            
            logger.info(f"In-app notification created for user {user.id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to create in-app notification: {str(e)}")
            return False


class AlertProcessor:
    """Service for processing and triggering alerts."""
    
    def __init__(self):
        self.notification_service = NotificationService()
    
    def check_price_alerts(self, asset_id: str, current_price: float) -> List[PriceAlert]:
        """Check and trigger price alerts for an asset."""
        from apps.portfolio.models import Asset
        
        try:
            asset = Asset.objects.get(id=asset_id)
        except Asset.DoesNotExist:
            logger.error(f"Asset {asset_id} not found")
            return []
        
        # Get active alerts for this asset
        alerts = PriceAlert.objects.filter(
            asset=asset,
            status='ACTIVE'
        ).select_related('user')
        
        triggered_alerts = []
        
        for alert in alerts:
            if not alert.can_trigger():
                continue
            
            should_trigger = False
            
            # Check trigger conditions
            if alert.alert_type == 'PRICE_ABOVE' and alert.comparison_operator == 'GT':
                should_trigger = current_price > alert.threshold_value
            elif alert.alert_type == 'PRICE_BELOW' and alert.comparison_operator == 'LT':
                should_trigger = current_price < alert.threshold_value
            elif alert.alert_type == 'PRICE_CHANGE':
                # Calculate percentage change from previous price
                previous_price = asset.current_price
                if previous_price > 0:
                    change_percent = ((current_price - previous_price) / previous_price) * 100
                    if alert.comparison_operator == 'GT':
                        should_trigger = change_percent > alert.threshold_value
                    elif alert.comparison_operator == 'LT':
                        should_trigger = change_percent < alert.threshold_value
            
            if should_trigger:
                self._trigger_alert(alert, current_price)
                triggered_alerts.append(alert)
        
        return triggered_alerts
    
    def _trigger_alert(self, alert: PriceAlert, triggered_value: float):
        """Trigger an alert and send notifications."""
        try:
            # Mark alert as triggered
            alert.trigger()
            
            # Create alert history record
            AlertHistory.objects.create(
                alert=alert,
                user=alert.user,
                triggered_value=triggered_value,
                threshold_value=alert.threshold_value,
                message=f"Alert '{alert.name}' triggered at {triggered_value}"
            )
            
            # Send notifications
            notification_results = self.notification_service.send_alert_notification(
                alert, triggered_value
            )

            # Record metrics
            record_alert_triggered(alert.alert_type, str(alert.user.id))
            update_active_alerts_gauge()

            logger.info(f"Alert {alert.id} triggered for user {alert.user.id}. Notifications: {notification_results}")
            
        except Exception as e:
            logger.error(f"Failed to trigger alert {alert.id}: {str(e)}")


# Global instances
notification_service = NotificationService()
alert_processor = AlertProcessor()

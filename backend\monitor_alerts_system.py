#!/usr/bin/env python
"""
TrustVault - Alerts System Monitoring

This script monitors the alerts system health and performance.
"""

import os
import sys
import django
import time
from datetime import datetime, timedelta

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'trustvault.settings')
django.setup()

from django.utils import timezone
from apps.alerts.models import PriceAlert, AlertHistory, Notification, Report, AlertStatus
from apps.alerts.metrics import metrics_collector, update_active_alerts_gauge, update_active_reports_gauge
import logging

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class AlertsSystemMonitor:
    """Monitor for the alerts system."""
    
    def __init__(self):
        self.start_time = timezone.now()
        self.checks_passed = 0
        self.checks_failed = 0
    
    def check_database_connectivity(self):
        """Check database connectivity."""
        try:
            count = PriceAlert.objects.count()
            logger.info(f"✅ Database connectivity OK - {count} alerts in database")
            self.checks_passed += 1
            return True
        except Exception as e:
            logger.error(f"❌ Database connectivity failed: {e}")
            self.checks_failed += 1
            return False
    
    def check_alert_processing(self):
        """Check if alerts are being processed."""
        try:
            # Check for recent alert activity
            recent_triggers = AlertHistory.objects.filter(
                created_at__gte=timezone.now() - timedelta(hours=24)
            ).count()
            
            active_alerts = PriceAlert.objects.filter(status=AlertStatus.ACTIVE).count()
            
            logger.info(f"✅ Alert processing OK - {active_alerts} active alerts, {recent_triggers} triggers in 24h")
            self.checks_passed += 1
            return True
        except Exception as e:
            logger.error(f"❌ Alert processing check failed: {e}")
            self.checks_failed += 1
            return False
    
    def check_notification_system(self):
        """Check notification system health."""
        try:
            # Check recent notifications
            recent_notifications = Notification.objects.filter(
                created_at__gte=timezone.now() - timedelta(hours=24)
            )
            
            total_notifications = recent_notifications.count()
            failed_notifications = recent_notifications.filter(status='FAILED').count()
            
            failure_rate = (failed_notifications / total_notifications * 100) if total_notifications > 0 else 0
            
            if failure_rate > 20:  # More than 20% failure rate is concerning
                logger.warning(f"⚠️ High notification failure rate: {failure_rate:.1f}% ({failed_notifications}/{total_notifications})")
            else:
                logger.info(f"✅ Notification system OK - {failure_rate:.1f}% failure rate ({failed_notifications}/{total_notifications})")
            
            self.checks_passed += 1
            return True
        except Exception as e:
            logger.error(f"❌ Notification system check failed: {e}")
            self.checks_failed += 1
            return False
    
    def check_report_generation(self):
        """Check report generation system."""
        try:
            # Check for stuck reports (generating for more than 1 hour)
            stuck_reports = Report.objects.filter(
                status='GENERATING',
                created_at__lt=timezone.now() - timedelta(hours=1)
            )
            
            stuck_count = stuck_reports.count()
            
            if stuck_count > 0:
                logger.warning(f"⚠️ Found {stuck_count} stuck reports (generating for >1 hour)")
                # Optionally mark them as failed
                # stuck_reports.update(status='FAILED', error_message='Timeout - stuck in generating state')
            else:
                logger.info("✅ Report generation system OK - no stuck reports")
            
            # Check recent report activity
            recent_reports = Report.objects.filter(
                created_at__gte=timezone.now() - timedelta(hours=24)
            ).count()
            
            logger.info(f"📊 {recent_reports} reports created in the last 24 hours")
            
            self.checks_passed += 1
            return True
        except Exception as e:
            logger.error(f"❌ Report generation check failed: {e}")
            self.checks_failed += 1
            return False
    
    def check_system_performance(self):
        """Check system performance metrics."""
        try:
            # Check for performance issues
            metrics = metrics_collector.collect_all_metrics()
            
            alert_counts = metrics.get('alert_counts', {})
            notification_stats = metrics.get('notification_stats', {})
            
            logger.info(f"📈 System metrics:")
            logger.info(f"   - Total alerts: {alert_counts.get('total', 0)}")
            logger.info(f"   - Active alerts: {alert_counts.get('active', 0)}")
            logger.info(f"   - Notifications (24h): {notification_stats.get('total', 0)}")
            
            self.checks_passed += 1
            return True
        except Exception as e:
            logger.error(f"❌ Performance check failed: {e}")
            self.checks_failed += 1
            return False
    
    def update_metrics(self):
        """Update system metrics."""
        try:
            update_active_alerts_gauge()
            update_active_reports_gauge()
            logger.info("✅ Metrics updated successfully")
            self.checks_passed += 1
            return True
        except Exception as e:
            logger.error(f"❌ Metrics update failed: {e}")
            self.checks_failed += 1
            return False
    
    def run_health_check(self):
        """Run complete health check."""
        logger.info("🚀 Starting TrustVault Alerts System Health Check")
        logger.info("=" * 60)
        
        # Run all checks
        checks = [
            ("Database Connectivity", self.check_database_connectivity),
            ("Alert Processing", self.check_alert_processing),
            ("Notification System", self.check_notification_system),
            ("Report Generation", self.check_report_generation),
            ("System Performance", self.check_system_performance),
            ("Metrics Update", self.update_metrics),
        ]
        
        for check_name, check_func in checks:
            logger.info(f"Running {check_name} check...")
            try:
                check_func()
            except Exception as e:
                logger.error(f"Check {check_name} failed with exception: {e}")
                self.checks_failed += 1
        
        # Summary
        total_checks = len(checks)
        success_rate = (self.checks_passed / total_checks * 100) if total_checks > 0 else 0
        
        logger.info("=" * 60)
        logger.info(f"Health Check Summary:")
        logger.info(f"  ✅ Passed: {self.checks_passed}/{total_checks}")
        logger.info(f"  ❌ Failed: {self.checks_failed}/{total_checks}")
        logger.info(f"  📊 Success Rate: {success_rate:.1f}%")
        logger.info(f"  ⏱️ Duration: {timezone.now() - self.start_time}")
        
        if success_rate >= 80:
            logger.info("🎉 System is healthy!")
            return True
        else:
            logger.warning("⚠️ System has issues that need attention!")
            return False


def continuous_monitoring(interval_minutes=5):
    """Run continuous monitoring."""
    logger.info(f"Starting continuous monitoring (interval: {interval_minutes} minutes)")
    
    while True:
        try:
            monitor = AlertsSystemMonitor()
            monitor.run_health_check()
            
            logger.info(f"Next check in {interval_minutes} minutes...")
            time.sleep(interval_minutes * 60)
            
        except KeyboardInterrupt:
            logger.info("Monitoring stopped by user")
            break
        except Exception as e:
            logger.error(f"Monitoring error: {e}")
            time.sleep(60)  # Wait 1 minute before retrying


if __name__ == '__main__':
    if len(sys.argv) > 1 and sys.argv[1] == '--continuous':
        interval = int(sys.argv[2]) if len(sys.argv) > 2 else 5
        continuous_monitoring(interval)
    else:
        monitor = AlertsSystemMonitor()
        success = monitor.run_health_check()
        sys.exit(0 if success else 1)

#!/bin/bash

# TrustVault - SSL Certificate Generation Script
# ============================================================================

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
SSL_DIR="$PROJECT_ROOT/nginx/ssl"

# Certificate configuration
COUNTRY="FR"
STATE="Ile-de-France"
CITY="Paris"
ORGANIZATION="TrustVault"
ORGANIZATIONAL_UNIT="Security Department"
COMMON_NAME="trustvault.local"
EMAIL="<EMAIL>"

# Certificate validity (in days)
VALIDITY_DAYS=365

# Subject Alternative Names
SAN="DNS:trustvault.local,DNS:api.trustvault.local,DNS:*.trustvault.local,DNS:localhost,IP:127.0.0.1,IP:::1"

echo "🔐 TrustVault SSL Certificate Generator"
echo "======================================"

# Create SSL directory if it doesn't exist
mkdir -p "$SSL_DIR"

# Generate CA private key
echo "📋 Generating Certificate Authority (CA) private key..."
openssl genrsa -out "$SSL_DIR/ca.key" 4096

# Generate CA certificate
echo "📋 Generating Certificate Authority (CA) certificate..."
openssl req -new -x509 -days $VALIDITY_DAYS -key "$SSL_DIR/ca.key" -out "$SSL_DIR/ca.crt" -subj "/C=$COUNTRY/ST=$STATE/L=$CITY/O=$ORGANIZATION/OU=$ORGANIZATIONAL_UNIT/CN=TrustVault CA/emailAddress=$EMAIL"

# Generate server private key
echo "📋 Generating server private key..."
openssl genrsa -out "$SSL_DIR/trustvault.key" 4096

# Generate certificate signing request (CSR)
echo "📋 Generating Certificate Signing Request (CSR)..."
openssl req -new -key "$SSL_DIR/trustvault.key" -out "$SSL_DIR/trustvault.csr" -subj "/C=$COUNTRY/ST=$STATE/L=$CITY/O=$ORGANIZATION/OU=$ORGANIZATIONAL_UNIT/CN=$COMMON_NAME/emailAddress=$EMAIL"

# Create extensions file for SAN
echo "📋 Creating certificate extensions..."
cat > "$SSL_DIR/trustvault.ext" << EOF
authorityKeyIdentifier=keyid,issuer
basicConstraints=CA:FALSE
keyUsage = digitalSignature, nonRepudiation, keyEncipherment, dataEncipherment
subjectAltName = $SAN
EOF

# Generate server certificate signed by CA
echo "📋 Generating server certificate..."
openssl x509 -req -in "$SSL_DIR/trustvault.csr" -CA "$SSL_DIR/ca.crt" -CAkey "$SSL_DIR/ca.key" -CAcreateserial -out "$SSL_DIR/trustvault.crt" -days $VALIDITY_DAYS -extensions v3_req -extfile "$SSL_DIR/trustvault.ext"

# Generate DH parameters for perfect forward secrecy
echo "📋 Generating Diffie-Hellman parameters (this may take a while)..."
openssl dhparam -out "$SSL_DIR/dhparam.pem" 2048

# Set proper permissions
chmod 600 "$SSL_DIR"/*.key
chmod 644 "$SSL_DIR"/*.crt "$SSL_DIR"/*.pem

# Clean up temporary files
rm -f "$SSL_DIR/trustvault.csr" "$SSL_DIR/trustvault.ext" "$SSL_DIR/ca.srl"

echo "✅ SSL certificates generated successfully!"
echo ""
echo "📁 Certificate files location: $SSL_DIR"
echo "   - CA Certificate: ca.crt"
echo "   - Server Certificate: trustvault.crt"
echo "   - Server Private Key: trustvault.key"
echo "   - DH Parameters: dhparam.pem"
echo ""
echo "🔧 To trust the CA certificate in your browser:"
echo "   1. Import $SSL_DIR/ca.crt into your browser's certificate store"
echo "   2. Mark it as trusted for identifying websites"
echo ""
echo "🌐 Add these entries to your /etc/hosts file:"
echo "   127.0.0.1 trustvault.local"
echo "   127.0.0.1 api.trustvault.local"
echo ""
echo "🔍 Certificate information:"
openssl x509 -in "$SSL_DIR/trustvault.crt" -text -noout | grep -A 1 "Subject:"
openssl x509 -in "$SSL_DIR/trustvault.crt" -text -noout | grep -A 1 "Subject Alternative Name:"
openssl x509 -in "$SSL_DIR/trustvault.crt" -text -noout | grep "Not After"

# TrustVault XSS Filter

[Definition]

# XSS attack patterns
failregex = ^<HOST> - .* ".*<script.*>.*" .*$
            ^<HOST> - .* ".*javascript:.*" .*$
            ^<HOST> - .* ".*vbscript:.*" .*$
            ^<HOST> - .* ".*(onload|onerror|onclick|onmouseover)=.*" .*$
            ^<HOST> - .* ".*&lt;script&gt;.*" .*$
            ^<HOST> - .* ".*%3Cscript%3E.*" .*$
            ^<HOST> - .* ".*eval\\(.*\\).*" .*$
            ^<HOST> - .* ".*document\\.cookie.*" .*$
            ^<HOST> - .* ".*alert\\(.*\\).*" .*$

# Date pattern
datepattern = ^%%d/%%b/%%Y:%%H:%%M:%%S %%z

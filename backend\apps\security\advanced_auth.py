"""
TrustVault - Advanced Authentication System

This module implements enterprise-grade authentication including:
- Multi-factor authentication (TOTP, SMS, Email, Hardware keys)
- Biometric authentication support
- Risk-based authentication
- Session management with device fingerprinting
- Adaptive authentication based on behavior analysis
"""

import json
import secrets
import hashlib
from typing import Dict, <PERSON>, Optional, Tuple, Any
from datetime import datetime, timedelta
from django.contrib.auth import get_user_model
from django.core.cache import cache
from django.conf import settings
from django.utils import timezone
from django_otp.plugins.otp_totp.models import TOTPDevice
from django_otp.plugins.otp_static.models import StaticDevice, StaticToken
import pyotp
import qrcode
from io import BytesIO
import base64
import geoip2.database
import user_agents
import logging

from .advanced_crypto import crypto
from .models import SecurityEvent, UserSession, DeviceFingerprint, RiskAssessment

logger = logging.getLogger(__name__)
User = get_user_model()

class AdvancedAuthenticationSystem:
    """Advanced authentication system with multiple security layers"""
    
    def __init__(self):
        self.max_failed_attempts = 5
        self.lockout_duration = timedelta(minutes=30)
        self.session_timeout = timedelta(hours=8)
        self.high_risk_threshold = 70
        
    def authenticate_user(self, email: str, password: str, request_data: Dict) -> Dict[str, Any]:
        """
        Advanced user authentication with risk assessment
        """
        try:
            user = User.objects.get(email=email)
        except User.DoesNotExist:
            # Prevent user enumeration by taking same time as valid user
            crypto.secure_hash("dummy_password")
            return {
                'success': False,
                'error': 'Invalid credentials',
                'risk_level': 'HIGH'
            }
        
        # Check if account is locked
        if self._is_account_locked(user):
            self._log_security_event(user, 'LOGIN_ATTEMPT_LOCKED_ACCOUNT', request_data)
            return {
                'success': False,
                'error': 'Account temporarily locked due to security concerns',
                'risk_level': 'CRITICAL'
            }
        
        # Verify password
        if not user.check_password(password):
            self._handle_failed_login(user, request_data)
            return {
                'success': False,
                'error': 'Invalid credentials',
                'risk_level': 'HIGH'
            }
        
        # Perform risk assessment
        risk_assessment = self._assess_login_risk(user, request_data)
        
        # Check if MFA is required
        mfa_required = user.is_mfa_enabled or risk_assessment['risk_score'] > 50
        
        if mfa_required and not request_data.get('mfa_token'):
            return {
                'success': False,
                'mfa_required': True,
                'risk_assessment': risk_assessment,
                'available_methods': self._get_available_mfa_methods(user)
            }
        
        # Verify MFA if provided
        if mfa_required and request_data.get('mfa_token'):
            mfa_result = self._verify_mfa(user, request_data.get('mfa_token'), request_data.get('mfa_method', 'totp'))
            if not mfa_result['success']:
                self._handle_failed_login(user, request_data)
                return mfa_result
        
        # Create secure session
        session_data = self._create_secure_session(user, request_data, risk_assessment)
        
        # Log successful login
        self._log_security_event(user, 'LOGIN_SUCCESS', request_data, {
            'risk_score': risk_assessment['risk_score'],
            'mfa_used': mfa_required
        })
        
        # Reset failed attempts
        self._reset_failed_attempts(user)
        
        return {
            'success': True,
            'session': session_data,
            'risk_assessment': risk_assessment,
            'user_id': str(user.id)
        }
    
    def _assess_login_risk(self, user: User, request_data: Dict) -> Dict[str, Any]:
        """Comprehensive risk assessment for login attempts"""
        risk_factors = []
        risk_score = 0
        
        ip_address = request_data.get('ip_address', '')
        user_agent = request_data.get('user_agent', '')
        
        # Geographic risk assessment
        geo_risk = self._assess_geographic_risk(user, ip_address)
        risk_score += geo_risk['score']
        if geo_risk['is_risky']:
            risk_factors.append(geo_risk['reason'])
        
        # Device fingerprinting
        device_risk = self._assess_device_risk(user, request_data)
        risk_score += device_risk['score']
        if device_risk['is_risky']:
            risk_factors.append(device_risk['reason'])
        
        # Time-based analysis
        time_risk = self._assess_time_based_risk(user)
        risk_score += time_risk['score']
        if time_risk['is_risky']:
            risk_factors.append(time_risk['reason'])
        
        # Behavioral analysis
        behavior_risk = self._assess_behavioral_risk(user, request_data)
        risk_score += behavior_risk['score']
        if behavior_risk['is_risky']:
            risk_factors.append(behavior_risk['reason'])
        
        # Recent security events
        security_risk = self._assess_recent_security_events(user)
        risk_score += security_risk['score']
        if security_risk['is_risky']:
            risk_factors.append(security_risk['reason'])
        
        risk_level = 'LOW'
        if risk_score > 70:
            risk_level = 'CRITICAL'
        elif risk_score > 50:
            risk_level = 'HIGH'
        elif risk_score > 30:
            risk_level = 'MEDIUM'
        
        # Store risk assessment
        RiskAssessment.objects.create(
            user=user,
            risk_score=risk_score,
            risk_level=risk_level,
            risk_factors=risk_factors,
            ip_address=ip_address,
            user_agent=user_agent
        )
        
        return {
            'risk_score': risk_score,
            'risk_level': risk_level,
            'risk_factors': risk_factors,
            'requires_additional_verification': risk_score > 50
        }
    
    def _assess_geographic_risk(self, user: User, ip_address: str) -> Dict[str, Any]:
        """Assess risk based on geographic location"""
        try:
            # Get user's typical locations from recent sessions
            recent_sessions = UserSession.objects.filter(
                user=user,
                created_at__gte=timezone.now() - timedelta(days=30)
            ).values_list('ip_address', flat=True)
            
            # Simple IP-based location check (in production, use proper GeoIP)
            if ip_address in recent_sessions:
                return {'score': 0, 'is_risky': False, 'reason': ''}
            
            # Check if IP is from known safe locations
            if self._is_ip_in_safe_range(ip_address):
                return {'score': 5, 'is_risky': False, 'reason': 'New but safe location'}
            
            return {
                'score': 25,
                'is_risky': True,
                'reason': 'Login from new geographic location'
            }
            
        except Exception as e:
            logger.error(f"Geographic risk assessment failed: {e}")
            return {'score': 10, 'is_risky': False, 'reason': 'Unable to assess location'}
    
    def _assess_device_risk(self, user: User, request_data: Dict) -> Dict[str, Any]:
        """Assess risk based on device fingerprinting"""
        try:
            user_agent = request_data.get('user_agent', '')
            device_fingerprint = self._generate_device_fingerprint(request_data)
            
            # Check if device is known
            known_device = DeviceFingerprint.objects.filter(
                user=user,
                fingerprint_hash=device_fingerprint
            ).first()
            
            if known_device:
                known_device.last_seen = timezone.now()
                known_device.save()
                return {'score': 0, 'is_risky': False, 'reason': ''}
            
            # New device - assess risk
            parsed_ua = user_agents.parse(user_agent)
            
            risk_score = 15  # Base score for new device
            risk_factors = ['New device detected']
            
            # Check for suspicious user agents
            if not parsed_ua.browser.family or not parsed_ua.os.family:
                risk_score += 20
                risk_factors.append('Suspicious or modified user agent')
            
            # Create new device fingerprint
            DeviceFingerprint.objects.create(
                user=user,
                fingerprint_hash=device_fingerprint,
                user_agent=user_agent,
                browser_family=parsed_ua.browser.family,
                os_family=parsed_ua.os.family,
                device_family=parsed_ua.device.family
            )
            
            return {
                'score': risk_score,
                'is_risky': risk_score > 20,
                'reason': '; '.join(risk_factors)
            }
            
        except Exception as e:
            logger.error(f"Device risk assessment failed: {e}")
            return {'score': 15, 'is_risky': True, 'reason': 'Unable to assess device'}
    
    def _assess_time_based_risk(self, user: User) -> Dict[str, Any]:
        """Assess risk based on login time patterns"""
        try:
            current_hour = timezone.now().hour
            
            # Get user's typical login hours
            recent_logins = SecurityEvent.objects.filter(
                user=user,
                action='LOGIN_SUCCESS',
                timestamp__gte=timezone.now() - timedelta(days=30)
            ).values_list('timestamp', flat=True)
            
            if not recent_logins:
                return {'score': 5, 'is_risky': False, 'reason': 'No login history'}
            
            # Calculate typical hours
            login_hours = [login.hour for login in recent_logins]
            typical_hours = set(login_hours)
            
            # Check if current hour is typical
            if current_hour in typical_hours:
                return {'score': 0, 'is_risky': False, 'reason': ''}
            
            # Check if it's during business hours (assuming 9-17)
            if 9 <= current_hour <= 17:
                return {'score': 5, 'is_risky': False, 'reason': 'Business hours login'}
            
            return {
                'score': 15,
                'is_risky': True,
                'reason': 'Login at unusual time'
            }
            
        except Exception as e:
            logger.error(f"Time-based risk assessment failed: {e}")
            return {'score': 5, 'is_risky': False, 'reason': 'Unable to assess time pattern'}
    
    def _assess_behavioral_risk(self, user: User, request_data: Dict) -> Dict[str, Any]:
        """Assess risk based on behavioral patterns"""
        try:
            # Check login frequency
            recent_logins = SecurityEvent.objects.filter(
                user=user,
                action__in=['LOGIN_SUCCESS', 'LOGIN_FAILED'],
                timestamp__gte=timezone.now() - timedelta(hours=1)
            ).count()
            
            if recent_logins > 10:
                return {
                    'score': 30,
                    'is_risky': True,
                    'reason': 'Unusual login frequency detected'
                }
            
            # Check for rapid successive attempts
            last_attempt = SecurityEvent.objects.filter(
                user=user,
                action__in=['LOGIN_SUCCESS', 'LOGIN_FAILED']
            ).first()
            
            if last_attempt and (timezone.now() - last_attempt.timestamp).seconds < 5:
                return {
                    'score': 20,
                    'is_risky': True,
                    'reason': 'Rapid successive login attempts'
                }
            
            return {'score': 0, 'is_risky': False, 'reason': ''}
            
        except Exception as e:
            logger.error(f"Behavioral risk assessment failed: {e}")
            return {'score': 5, 'is_risky': False, 'reason': 'Unable to assess behavior'}
    
    def _assess_recent_security_events(self, user: User) -> Dict[str, Any]:
        """Assess risk based on recent security events"""
        try:
            # Check for recent security incidents
            recent_events = SecurityEvent.objects.filter(
                user=user,
                risk_level__in=['HIGH', 'CRITICAL'],
                timestamp__gte=timezone.now() - timedelta(days=7)
            ).count()
            
            if recent_events > 0:
                return {
                    'score': 25,
                    'is_risky': True,
                    'reason': f'{recent_events} recent security events'
                }
            
            return {'score': 0, 'is_risky': False, 'reason': ''}
            
        except Exception as e:
            logger.error(f"Security events assessment failed: {e}")
            return {'score': 5, 'is_risky': False, 'reason': 'Unable to assess security events'}
    
    def _generate_device_fingerprint(self, request_data: Dict) -> str:
        """Generate unique device fingerprint"""
        fingerprint_data = {
            'user_agent': request_data.get('user_agent', ''),
            'accept_language': request_data.get('accept_language', ''),
            'screen_resolution': request_data.get('screen_resolution', ''),
            'timezone': request_data.get('timezone', ''),
            'platform': request_data.get('platform', '')
        }
        
        fingerprint_string = json.dumps(fingerprint_data, sort_keys=True)
        return hashlib.sha256(fingerprint_string.encode()).hexdigest()
    
    def _verify_mfa(self, user: User, token: str, method: str = 'totp') -> Dict[str, Any]:
        """Verify multi-factor authentication"""
        try:
            if method == 'totp':
                return self._verify_totp(user, token)
            elif method == 'backup':
                return self._verify_backup_code(user, token)
            else:
                return {
                    'success': False,
                    'error': 'Unsupported MFA method'
                }
        except Exception as e:
            logger.error(f"MFA verification failed: {e}")
            return {
                'success': False,
                'error': 'MFA verification failed'
            }
    
    def _verify_totp(self, user: User, token: str) -> Dict[str, Any]:
        """Verify TOTP token"""
        device = TOTPDevice.objects.filter(user=user, confirmed=True).first()
        if not device:
            return {
                'success': False,
                'error': 'TOTP not configured'
            }
        
        if device.verify_token(token):
            return {'success': True}
        
        return {
            'success': False,
            'error': 'Invalid TOTP token'
        }
    
    def _verify_backup_code(self, user: User, code: str) -> Dict[str, Any]:
        """Verify backup recovery code"""
        device = StaticDevice.objects.filter(user=user).first()
        if not device:
            return {
                'success': False,
                'error': 'Backup codes not configured'
            }
        
        token = StaticToken.objects.filter(device=device, token=code).first()
        if token:
            token.delete()  # One-time use
            return {'success': True}
        
        return {
            'success': False,
            'error': 'Invalid backup code'
        }
    
    def _get_available_mfa_methods(self, user: User) -> List[str]:
        """Get available MFA methods for user"""
        methods = []
        
        if TOTPDevice.objects.filter(user=user, confirmed=True).exists():
            methods.append('totp')
        
        if StaticDevice.objects.filter(user=user).exists():
            methods.append('backup')
        
        return methods
    
    def _create_secure_session(self, user: User, request_data: Dict, risk_assessment: Dict) -> Dict[str, Any]:
        """Create secure session with enhanced security"""
        session_token = crypto.generate_secure_token(64)
        
        # Create session record
        session = UserSession.objects.create(
            user=user,
            session_key=session_token,
            ip_address=request_data.get('ip_address', ''),
            user_agent=request_data.get('user_agent', ''),
            risk_score=risk_assessment['risk_score'],
            expires_at=timezone.now() + self.session_timeout
        )
        
        # Store session in cache for fast access
        cache.set(f"session:{session_token}", {
            'user_id': str(user.id),
            'session_id': str(session.id),
            'risk_score': risk_assessment['risk_score'],
            'created_at': session.created_at.isoformat()
        }, timeout=int(self.session_timeout.total_seconds()))
        
        return {
            'token': session_token,
            'expires_at': session.expires_at.isoformat(),
            'risk_score': risk_assessment['risk_score']
        }
    
    def _is_account_locked(self, user: User) -> bool:
        """Check if account is locked due to failed attempts"""
        cache_key = f"failed_attempts:{user.id}"
        attempts_data = cache.get(cache_key, {})
        
        if not attempts_data:
            return False
        
        if attempts_data.get('count', 0) >= self.max_failed_attempts:
            lockout_time = datetime.fromisoformat(attempts_data.get('lockout_until', ''))
            return timezone.now() < lockout_time
        
        return False
    
    def _handle_failed_login(self, user: User, request_data: Dict):
        """Handle failed login attempt"""
        cache_key = f"failed_attempts:{user.id}"
        attempts_data = cache.get(cache_key, {'count': 0})
        
        attempts_data['count'] += 1
        attempts_data['last_attempt'] = timezone.now().isoformat()
        
        if attempts_data['count'] >= self.max_failed_attempts:
            attempts_data['lockout_until'] = (timezone.now() + self.lockout_duration).isoformat()
        
        cache.set(cache_key, attempts_data, timeout=int(self.lockout_duration.total_seconds()))
        
        # Log security event
        self._log_security_event(user, 'LOGIN_FAILED', request_data, {
            'failed_attempts': attempts_data['count']
        })
    
    def _reset_failed_attempts(self, user: User):
        """Reset failed login attempts counter"""
        cache_key = f"failed_attempts:{user.id}"
        cache.delete(cache_key)
    
    def _log_security_event(self, user: User, action: str, request_data: Dict, additional_data: Dict = None):
        """Log security event"""
        SecurityEvent.objects.create(
            user=user,
            action=action,
            ip_address=request_data.get('ip_address', ''),
            user_agent=request_data.get('user_agent', ''),
            details=additional_data or {},
            risk_level=self._determine_event_risk_level(action)
        )
    
    def _determine_event_risk_level(self, action: str) -> str:
        """Determine risk level for security event"""
        high_risk_actions = ['LOGIN_FAILED', 'LOGIN_ATTEMPT_LOCKED_ACCOUNT', 'MFA_FAILED']
        medium_risk_actions = ['PASSWORD_CHANGED', 'MFA_DISABLED']
        
        if action in high_risk_actions:
            return 'HIGH'
        elif action in medium_risk_actions:
            return 'MEDIUM'
        else:
            return 'LOW'
    
    def _is_ip_in_safe_range(self, ip_address: str) -> bool:
        """Check if IP is in known safe ranges (corporate networks, etc.)"""
        # This would contain your organization's IP ranges
        safe_ranges = getattr(settings, 'SAFE_IP_RANGES', [])
        # Implementation would check if IP is in any of the safe ranges
        return False


# Global instance
auth_system = AdvancedAuthenticationSystem()

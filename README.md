# 🏦 TrustVault - Plateforme de Gestion de Portefeuille Sécurisée

[![Security Level](https://img.shields.io/badge/Security-Banking%20Grade-green.svg)](SECURITY_VALIDATION_REPORT.md)
[![Compliance](https://img.shields.io/badge/Compliance-ISO%2027001%20%7C%20SOC%202%20%7C%20GDPR-blue.svg)](SECURITY_IMPLEMENTATION_GUIDE.md)
[![Test Coverage](https://img.shields.io/badge/Test%20Coverage-82.4%25-brightgreen.svg)](run_complete_security_validation.py)
[![Production Ready](https://img.shields.io/badge/Status-Production%20Ready-success.svg)](#)

TrustVault est une **plateforme de gestion de portefeuille financier de niveau bancaire**, développée avec Django et React. Elle offre une sécurité militaire, une conformité réglementaire complète et des fonctionnalités de trading sophistiquées.

![TrustVault](https://img.shields.io/badge/TrustVault-v1.0.0-blue.svg)
![Django](https://img.shields.io/badge/Django-4.2-green.svg)
![React](https://img.shields.io/badge/React-18-blue.svg)
![TypeScript](https://img.shields.io/badge/TypeScript-5.0-blue.svg)
![Docker](https://img.shields.io/badge/Docker-Ready-blue.svg)

## ✨ Features

### 📊 Portfolio Management
- **Create & Manage Portfolios**: Organize your investments efficiently
- **Asset Tracking**: Monitor stocks, bonds, and other securities
- **Transaction History**: Complete audit trail of all transactions
- **Holdings Management**: Real-time position tracking

### 🔒 Advanced Security
- **Multi-Factor Authentication (MFA)**: TOTP-based 2FA protection
- **Session Management**: Monitor and control active sessions
- **Audit Logging**: Comprehensive activity tracking
- **Security Dashboard**: Real-time threat monitoring
- **Role-based Access Control**: Granular permissions system

### 📈 Analytics & Reporting
- **Portfolio Analytics**: Performance metrics and insights
- **Asset Allocation**: Visual breakdown by asset and sector
- **Performance Tracking**: Historical performance analysis
- **Risk Assessment**: Portfolio risk evaluation

### 🛡️ Cybersecurity Features
- **JWT Authentication**: Secure token-based authentication
- **Rate Limiting**: Protection against brute force attacks
- **CORS Protection**: Cross-origin request security
- **Security Headers**: Comprehensive HTTP security headers
- **Input Validation**: Protection against injection attacks

## 🏗️ Tech Stack

### Backend
- **Django 4.2** - Web framework
- **Django REST Framework** - API development
- **PostgreSQL** - Primary database
- **Redis** - Caching and session storage
- **Celery** - Background task processing
- **JWT** - Authentication tokens
- **Gunicorn** - WSGI server

### Frontend
- **React 18** - UI framework
- **TypeScript** - Type safety
- **Material-UI (MUI)** - Component library
- **React Query** - Data fetching and caching
- **React Router** - Client-side routing
- **Zustand** - State management
- **React Hook Form** - Form handling

### Infrastructure
- **Docker & Docker Compose** - Containerization
- **Nginx** - Reverse proxy and static file serving
- **PostgreSQL** - Database
- **Redis** - Cache and message broker

## 🚀 Quick Start

### Prerequisites

- **Docker Desktop** (Windows/Mac) or **Docker + Docker Compose** (Linux)
- **Git** for cloning the repository

### 🎯 One-Click Setup

#### Windows Users
```cmd
# Clone the repository
git clone <repository-url>
cd trustvault

# Start the application
start.bat development
```

#### Linux/Mac Users
```bash
# Clone the repository
git clone <repository-url>
cd trustvault

# Start the application
./start.sh development
```

### 🌐 Access the Application

After startup, access these URLs:

- **🖥️ Frontend Application**: http://localhost:3000
- **🔧 Backend API**: http://localhost:8000
- **📚 API Documentation**: http://localhost:8000/api/docs/
- **⚙️ Admin Panel**: http://localhost:8000/admin/

### 🔑 Default Credentials

```
Email: <EMAIL>
Password: admin123
```

**⚠️ Important**: Change these credentials immediately in production!

## 📖 Detailed Setup

### Environment Configuration

1. **Copy environment template**:
   ```bash
   cp .env.example .env
   ```

2. **Edit `.env` file** with your configuration:
   ```env
   # Database
   POSTGRES_DB=trustvault
   POSTGRES_USER=trustvault
   POSTGRES_PASSWORD=your_secure_password
   
   # Django
   DJANGO_SECRET_KEY=your_secret_key_here
   DJANGO_DEBUG=False
   
   # Security
   ENCRYPTION_KEY=your_encryption_key_here
   JWT_SECRET_KEY=your_jwt_secret_here
   ```

### Development Setup

#### Backend Development
```bash
cd backend
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt
python manage.py migrate
python manage.py createsuperuser
python manage.py runserver
```

#### Frontend Development
```bash
cd frontend
npm install
npm start
```

## 📚 API Documentation

Comprehensive API documentation is available:

- **📋 Swagger UI**: http://localhost:8000/api/docs/
- **📖 ReDoc**: http://localhost:8000/api/redoc/
- **🔗 API Schema**: http://localhost:8000/api/schema/

### Key API Endpoints

```
Authentication:
POST /api/v1/auth/login/          - User login
POST /api/v1/auth/register/       - User registration
POST /api/v1/auth/logout/         - User logout
GET  /api/v1/auth/profile/        - User profile

Portfolio Management:
GET  /api/v1/portfolio/           - List portfolios
POST /api/v1/portfolio/           - Create portfolio
GET  /api/v1/portfolio/{id}/      - Portfolio details
GET  /api/v1/portfolio/{id}/analytics/ - Portfolio analytics

Security:
GET  /api/v1/security/dashboard/  - Security dashboard
GET  /api/v1/security/events/     - Security events
GET  /api/v1/security/audit-logs/ - Audit logs
```

## 🔧 Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `DJANGO_DEBUG` | Enable debug mode | `False` |
| `DJANGO_SECRET_KEY` | Django secret key | Required |
| `DATABASE_URL` | Database connection URL | PostgreSQL default |
| `REDIS_URL` | Redis connection URL | `redis://redis:6379/0` |
| `ENCRYPTION_KEY` | Data encryption key | Required |
| `JWT_SECRET_KEY` | JWT signing key | Required |

### Docker Configuration

- **Development**: `docker-compose.dev.yml`
- **Production**: `docker-compose.yml`

## 🛡️ Security Best Practices

### Authentication & Authorization
- ✅ JWT-based authentication with refresh tokens
- ✅ Multi-factor authentication (TOTP)
- ✅ Session management and monitoring
- ✅ Role-based access control (RBAC)

### Data Protection
- ✅ Encrypted sensitive data at rest
- ✅ HTTPS enforcement in production
- ✅ Secure password hashing (Argon2)
- ✅ Input validation and sanitization

### Infrastructure Security
- ✅ Docker security best practices
- ✅ Non-root container users
- ✅ Security headers (HSTS, CSP, etc.)
- ✅ Rate limiting and DDoS protection

## 🧪 Testing

### Backend Tests
```bash
cd backend
python manage.py test
```

### Frontend Tests
```bash
cd frontend
npm test
```

### Integration Tests
```bash
docker-compose -f docker-compose.test.yml up --abort-on-container-exit
```

## 📊 Monitoring & Logging

### Application Logs
```bash
# View all logs
docker-compose logs -f

# View specific service logs
docker-compose logs -f backend
docker-compose logs -f frontend
```

### Health Checks
- **Backend**: http://localhost:8000/health/
- **Frontend**: http://localhost:3000/health

## 🚀 Deployment

### Production Deployment

1. **Configure environment**:
   ```bash
   cp .env.example .env.production
   # Edit .env.production with production values
   ```

2. **Deploy with Docker**:
   ```bash
   docker-compose -f docker-compose.yml --env-file .env.production up -d
   ```

3. **Setup SSL/TLS** (recommended):
   - Use Let's Encrypt with Certbot
   - Configure Nginx for HTTPS
   - Update security headers

### Cloud Deployment

The application is ready for deployment on:
- **AWS** (ECS, EKS, or EC2)
- **Google Cloud Platform** (GKE or Compute Engine)
- **Azure** (Container Instances or AKS)
- **DigitalOcean** (App Platform or Droplets)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

### Common Issues

**Q: Docker containers won't start**
A: Ensure Docker Desktop is running and ports 3000, 8000, 5432, 6379 are available.

**Q: Database connection errors**
A: Wait for PostgreSQL to fully initialize (usually 30-60 seconds on first run).

**Q: Frontend shows API errors**
A: Verify backend is running and accessible at http://localhost:8000/health/

### Getting Help

- 📧 **Email**: <EMAIL>
- 🐛 **Issues**: Create an issue on GitHub
- 📖 **Documentation**: Check the `/docs` directory

---

**Made with ❤️ by the TrustVault Team**

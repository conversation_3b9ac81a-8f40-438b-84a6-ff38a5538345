# TrustVault - Alerts Serializers

from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import (
    PriceAlert, AlertHistory, NotificationPreference, 
    Notification, Report, AlertType, AlertStatus, NotificationChannel
)
from apps.portfolio.models import Asset, Portfolio

User = get_user_model()


class PriceAlertSerializer(serializers.ModelSerializer):
    """Serializer for PriceAlert model."""
    
    asset_symbol = serializers.CharField(source='asset.symbol', read_only=True)
    asset_name = serializers.CharField(source='asset.name', read_only=True)
    portfolio_name = serializers.CharField(source='portfolio.name', read_only=True)
    can_trigger = serializers.BooleanField(read_only=True)
    is_expired = serializers.BooleanField(read_only=True)
    
    class Meta:
        model = PriceAlert
        fields = [
            'id', 'asset', 'portfolio', 'asset_symbol', 'asset_name', 'portfolio_name',
            'alert_type', 'threshold_value', 'comparison_operator', 'name', 'description',
            'status', 'notification_channels', 'notification_message', 'expires_at',
            'last_checked_at', 'triggered_at', 'max_triggers', 'trigger_count',
            'cooldown_minutes', 'can_trigger', 'is_expired', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'last_checked_at', 'triggered_at', 'trigger_count', 
            'created_at', 'updated_at'
        ]
    
    def validate(self, attrs):
        """Validate alert data."""
        # Ensure either asset or portfolio is specified, but not both
        asset = attrs.get('asset')
        portfolio = attrs.get('portfolio')
        
        if not asset and not portfolio:
            raise serializers.ValidationError("Either asset or portfolio must be specified.")
        
        if asset and portfolio:
            raise serializers.ValidationError("Cannot specify both asset and portfolio.")
        
        # Validate alert type compatibility
        alert_type = attrs.get('alert_type')
        if alert_type in ['PORTFOLIO_VALUE', 'PORTFOLIO_CHANGE'] and not portfolio:
            raise serializers.ValidationError(f"Alert type {alert_type} requires a portfolio.")
        
        if alert_type in ['PRICE_ABOVE', 'PRICE_BELOW', 'PRICE_CHANGE', 'VOLUME_SPIKE'] and not asset:
            raise serializers.ValidationError(f"Alert type {alert_type} requires an asset.")
        
        return attrs
    
    def create(self, validated_data):
        """Create alert with user from request."""
        validated_data['user'] = self.context['request'].user
        return super().create(validated_data)


class AlertHistorySerializer(serializers.ModelSerializer):
    """Serializer for AlertHistory model."""
    
    alert_name = serializers.CharField(source='alert.name', read_only=True)
    alert_type = serializers.CharField(source='alert.alert_type', read_only=True)
    asset_symbol = serializers.CharField(source='alert.asset.symbol', read_only=True)
    
    class Meta:
        model = AlertHistory
        fields = [
            'id', 'alert', 'alert_name', 'alert_type', 'asset_symbol',
            'triggered_value', 'threshold_value', 'message',
            'notifications_sent', 'notification_failures', 'created_at'
        ]
        read_only_fields = ['id', 'created_at']


class NotificationPreferenceSerializer(serializers.ModelSerializer):
    """Serializer for NotificationPreference model."""
    
    class Meta:
        model = NotificationPreference
        fields = [
            'id', 'email_enabled', 'sms_enabled', 'push_enabled', 'in_app_enabled',
            'email_address', 'phone_number', 'quiet_hours_start', 'quiet_hours_end',
            'timezone', 'max_emails_per_day', 'max_sms_per_day', 'max_push_per_hour',
            'price_alerts_enabled', 'portfolio_alerts_enabled', 'news_alerts_enabled',
            'security_alerts_enabled', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def validate_phone_number(self, value):
        """Validate phone number format."""
        if value and not value.startswith('+'):
            raise serializers.ValidationError("Phone number must include country code (e.g., +**********)")
        return value


class NotificationSerializer(serializers.ModelSerializer):
    """Serializer for Notification model."""
    
    alert_name = serializers.CharField(source='alert.name', read_only=True)
    
    class Meta:
        model = Notification
        fields = [
            'id', 'alert', 'alert_name', 'channel', 'recipient', 'subject',
            'message', 'status', 'sent_at', 'delivered_at', 'error_message',
            'external_id', 'provider', 'metadata', 'created_at'
        ]
        read_only_fields = [
            'id', 'sent_at', 'delivered_at', 'error_message', 
            'external_id', 'provider', 'created_at'
        ]


class ReportSerializer(serializers.ModelSerializer):
    """Serializer for Report model."""
    
    portfolio_name = serializers.CharField(source='portfolio.name', read_only=True)
    file_size_mb = serializers.SerializerMethodField()
    download_url = serializers.SerializerMethodField()
    
    class Meta:
        model = Report
        fields = [
            'id', 'portfolio', 'portfolio_name', 'report_type', 'report_format',
            'name', 'description', 'start_date', 'end_date', 'status',
            'parameters', 'file_path', 'file_size', 'file_size_mb', 'file_hash',
            'generated_at', 'expires_at', 'error_message', 'generation_time',
            'download_url', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'status', 'file_path', 'file_size', 'file_hash',
            'generated_at', 'error_message', 'generation_time',
            'created_at', 'updated_at'
        ]
    
    def get_file_size_mb(self, obj):
        """Get file size in MB."""
        if obj.file_size:
            return round(obj.file_size / (1024 * 1024), 2)
        return None
    
    def get_download_url(self, obj):
        """Get download URL for completed reports."""
        if obj.status == 'COMPLETED' and obj.file_path:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(f'/api/v1/alerts/reports/{obj.id}/download/')
        return None
    
    def validate(self, attrs):
        """Validate report data."""
        start_date = attrs.get('start_date')
        end_date = attrs.get('end_date')
        
        if start_date and end_date and start_date > end_date:
            raise serializers.ValidationError("Start date must be before end date.")
        
        return attrs
    
    def create(self, validated_data):
        """Create report with user from request."""
        validated_data['user'] = self.context['request'].user
        return super().create(validated_data)


class ReportCreateSerializer(serializers.ModelSerializer):
    """Simplified serializer for creating reports."""
    
    class Meta:
        model = Report
        fields = [
            'portfolio', 'report_type', 'report_format', 'name', 
            'description', 'start_date', 'end_date', 'parameters'
        ]
    
    def create(self, validated_data):
        """Create report with user from request."""
        validated_data['user'] = self.context['request'].user
        return super().create(validated_data)


# Choice serializers for frontend
class AlertTypeChoicesSerializer(serializers.Serializer):
    """Serializer for alert type choices."""
    value = serializers.CharField()
    label = serializers.CharField()


class AlertStatusChoicesSerializer(serializers.Serializer):
    """Serializer for alert status choices."""
    value = serializers.CharField()
    label = serializers.CharField()


class NotificationChannelChoicesSerializer(serializers.Serializer):
    """Serializer for notification channel choices."""
    value = serializers.CharField()
    label = serializers.CharField()

"""
TrustVault - Compliance and Regulatory Framework

This module implements comprehensive compliance monitoring and reporting for:
- ISO 27001 (Information Security Management)
- SOC 2 (Service Organization Control 2)
- GDPR (General Data Protection Regulation)
- NIST Cybersecurity Framework
- PCI DSS (Payment Card Industry Data Security Standard)
- HIPAA (Health Insurance Portability and Accountability Act)
"""

import json
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from django.utils import timezone
from django.conf import settings
from django.db.models import Count, Q
import logging

from .models import ComplianceAudit, SecurityEvent
# AuditLog is from django-audit-log package
from .advanced_crypto import crypto

logger = logging.getLogger(__name__)

class ComplianceFramework:
    """Comprehensive compliance framework for TrustVault"""
    
    def __init__(self):
        self.frameworks = {
            'ISO_27001': self._get_iso27001_controls(),
            'SOC_2': self._get_soc2_controls(),
            'GDPR': self._get_gdpr_requirements(),
            'NIST': self._get_nist_controls(),
            'PCI_DSS': self._get_pci_dss_requirements(),
            'HIPAA': self._get_hipaa_requirements(),
        }
    
    def assess_compliance(self, framework: str) -> Dict[str, Any]:
        """Perform comprehensive compliance assessment"""
        
        if framework not in self.frameworks:
            raise ValueError(f"Unsupported framework: {framework}")
        
        controls = self.frameworks[framework]
        assessment_results = []
        overall_score = 0
        total_controls = len(controls)
        
        for control in controls:
            result = self._assess_control(framework, control)
            assessment_results.append(result)
            overall_score += result['score']
        
        overall_score = (overall_score / total_controls) if total_controls > 0 else 0
        
        # Determine compliance status
        compliance_status = self._determine_compliance_status(overall_score)
        
        # Generate recommendations
        recommendations = self._generate_recommendations(assessment_results)
        
        # Create audit record
        audit_record = ComplianceAudit.objects.create(
            framework=framework,
            control_id='OVERALL_ASSESSMENT',
            control_name=f'{framework} Overall Assessment',
            description=f'Comprehensive assessment of {framework} compliance',
            status='COMPLETED',
            auditor_id=None,  # System-generated
            audit_date=timezone.now(),
            next_audit_date=timezone.now() + timedelta(days=90),
            compliance_score=int(overall_score),
            findings=[result for result in assessment_results if result['score'] < 80],
            recommendations=recommendations,
            evidence_files=[],
            documentation_links=[]
        )
        
        return {
            'framework': framework,
            'assessment_date': timezone.now().isoformat(),
            'overall_score': overall_score,
            'compliance_status': compliance_status,
            'total_controls': total_controls,
            'passed_controls': len([r for r in assessment_results if r['score'] >= 80]),
            'failed_controls': len([r for r in assessment_results if r['score'] < 80]),
            'control_results': assessment_results,
            'recommendations': recommendations,
            'audit_id': str(audit_record.id),
            'next_assessment_date': (timezone.now() + timedelta(days=90)).isoformat()
        }
    
    def _assess_control(self, framework: str, control: Dict[str, Any]) -> Dict[str, Any]:
        """Assess individual compliance control"""
        
        control_id = control['id']
        control_name = control['name']
        assessment_method = control.get('assessment_method', 'manual')
        
        if assessment_method == 'automated':
            score = self._automated_control_assessment(framework, control)
        else:
            score = self._manual_control_assessment(framework, control)
        
        # Determine status
        status = 'PASSED' if score >= 80 else 'FAILED'
        if 60 <= score < 80:
            status = 'PARTIAL'
        
        return {
            'control_id': control_id,
            'control_name': control_name,
            'framework': framework,
            'score': score,
            'status': status,
            'assessment_date': timezone.now().isoformat(),
            'evidence': control.get('evidence', []),
            'gaps': control.get('gaps', []) if score < 80 else [],
            'recommendations': control.get('recommendations', []) if score < 80 else []
        }
    
    def _automated_control_assessment(self, framework: str, control: Dict[str, Any]) -> int:
        """Perform automated assessment of compliance control"""
        
        control_id = control['id']
        score = 0
        
        # Example automated assessments based on control type
        if 'encryption' in control['name'].lower():
            score = self._assess_encryption_controls()
        elif 'access_control' in control['name'].lower():
            score = self._assess_access_controls()
        elif 'logging' in control['name'].lower():
            score = self._assess_logging_controls()
        elif 'incident' in control['name'].lower():
            score = self._assess_incident_response()
        elif 'backup' in control['name'].lower():
            score = self._assess_backup_controls()
        elif 'vulnerability' in control['name'].lower():
            score = self._assess_vulnerability_management()
        else:
            # Default assessment for unknown controls
            score = 70  # Assume partial compliance
        
        return min(100, max(0, score))
    
    def _assess_encryption_controls(self) -> int:
        """Assess encryption implementation"""
        score = 0
        
        # Check if encryption is properly configured
        if hasattr(settings, 'ENCRYPTION_KEY') and settings.ENCRYPTION_KEY:
            score += 30
        
        # Check database encryption
        if getattr(settings, 'DATABASE_ENCRYPTION_ENABLED', False):
            score += 25
        
        # Check TLS/SSL configuration
        if not settings.DEBUG and getattr(settings, 'SECURE_SSL_REDIRECT', False):
            score += 25
        
        # Check session security
        if getattr(settings, 'SESSION_COOKIE_SECURE', False):
            score += 20
        
        return score
    
    def _assess_access_controls(self) -> int:
        """Assess access control implementation"""
        score = 0
        
        # Check MFA implementation
        from django_otp.plugins.otp_totp.models import TOTPDevice
        mfa_users = TOTPDevice.objects.filter(confirmed=True).count()
        total_users = settings.AUTH_USER_MODEL.objects.count() if hasattr(settings, 'AUTH_USER_MODEL') else 1
        
        if mfa_users > 0:
            mfa_percentage = (mfa_users / max(total_users, 1)) * 100
            score += min(40, int(mfa_percentage * 0.4))
        
        # Check password policies
        if getattr(settings, 'AUTH_PASSWORD_VALIDATORS', []):
            score += 30
        
        # Check session timeout
        if getattr(settings, 'SESSION_COOKIE_AGE', 0) <= 28800:  # 8 hours or less
            score += 30
        
        return score
    
    def _assess_logging_controls(self) -> int:
        """Assess logging and monitoring implementation"""
        score = 0
        
        # Check if audit logging is enabled
        try:
            from audit_log.models import LogEntry
            recent_logs = LogEntry.objects.filter(
                timestamp__gte=timezone.now() - timedelta(days=1)
            ).count()

            if recent_logs > 0:
                score += 40
        except ImportError:
            # If audit_log is not available, assume logging is configured
            score += 40
        
        # Check security event logging
        recent_events = SecurityEvent.objects.filter(
            timestamp__gte=timezone.now() - timedelta(days=1)
        ).count()
        
        if recent_events >= 0:  # Even 0 events is good (means logging is working)
            score += 30
        
        # Check log retention
        try:
            from audit_log.models import LogEntry
            old_logs = LogEntry.objects.filter(
                timestamp__lt=timezone.now() - timedelta(days=90)
            ).count()

            if old_logs > 0:  # Logs are being retained
                score += 30
        except ImportError:
            # Assume log retention is configured
            score += 30
        
        return score
    
    def _assess_incident_response(self) -> int:
        """Assess incident response capabilities"""
        score = 0
        
        # Check if incident response procedures exist
        from .models import IncidentResponse
        
        # Check for recent incident handling
        recent_incidents = IncidentResponse.objects.filter(
            detected_at__gte=timezone.now() - timedelta(days=30)
        ).count()
        
        # Having some incidents is normal and shows the system is working
        if recent_incidents >= 0:
            score += 25
        
        # Check response times
        resolved_incidents = IncidentResponse.objects.filter(
            status='RESOLVED',
            resolved_at__isnull=False
        )
        
        if resolved_incidents.exists():
            score += 25
        
        # Check if automated responses are configured
        automated_responses = IncidentResponse.objects.filter(
            incident_type='AUTOMATED_THREAT_RESPONSE'
        ).count()
        
        if automated_responses > 0:
            score += 25
        
        # Check documentation
        score += 25  # Assume documentation exists
        
        return score
    
    def _assess_backup_controls(self) -> int:
        """Assess backup and recovery controls"""
        score = 0
        
        # This would typically check backup systems
        # For now, assume basic backup procedures are in place
        score += 50
        
        # Check if database backups are configured
        if getattr(settings, 'BACKUP_ENABLED', False):
            score += 50
        
        return score
    
    def _assess_vulnerability_management(self) -> int:
        """Assess vulnerability management processes"""
        score = 0
        
        # Check for recent security assessments
        recent_assessments = SecurityEvent.objects.filter(
            action='SECURITY_ASSESSMENT',
            timestamp__gte=timezone.now() - timedelta(days=30)
        ).count()
        
        if recent_assessments > 0:
            score += 50
        
        # Check for patch management (assume it's in place)
        score += 50
        
        return score
    
    def _manual_control_assessment(self, framework: str, control: Dict[str, Any]) -> int:
        """Perform manual assessment (placeholder for human review)"""
        # This would typically require human review
        # For automated assessment, we'll return a default score
        return 75  # Assume partial compliance pending manual review
    
    def _determine_compliance_status(self, score: float) -> str:
        """Determine overall compliance status"""
        if score >= 90:
            return 'FULLY_COMPLIANT'
        elif score >= 80:
            return 'SUBSTANTIALLY_COMPLIANT'
        elif score >= 60:
            return 'PARTIALLY_COMPLIANT'
        else:
            return 'NON_COMPLIANT'
    
    def _generate_recommendations(self, assessment_results: List[Dict]) -> List[str]:
        """Generate compliance recommendations"""
        recommendations = []
        
        failed_controls = [r for r in assessment_results if r['score'] < 80]
        
        for control in failed_controls:
            if 'encryption' in control['control_name'].lower():
                recommendations.append("Implement comprehensive encryption for data at rest and in transit")
            elif 'access' in control['control_name'].lower():
                recommendations.append("Strengthen access controls and implement multi-factor authentication")
            elif 'logging' in control['control_name'].lower():
                recommendations.append("Enhance logging and monitoring capabilities")
            elif 'incident' in control['control_name'].lower():
                recommendations.append("Develop and test incident response procedures")
            elif 'backup' in control['control_name'].lower():
                recommendations.append("Implement regular backup and recovery testing")
            elif 'vulnerability' in control['control_name'].lower():
                recommendations.append("Establish vulnerability management and patch management processes")
        
        # Add general recommendations
        if len(failed_controls) > 0:
            recommendations.extend([
                "Conduct regular compliance assessments",
                "Provide security awareness training to all users",
                "Implement continuous monitoring and improvement processes",
                "Document all security policies and procedures"
            ])
        
        return list(set(recommendations))  # Remove duplicates
    
    def _get_iso27001_controls(self) -> List[Dict[str, Any]]:
        """Get ISO 27001 control requirements"""
        return [
            {
                'id': 'A.5.1.1',
                'name': 'Information Security Policy',
                'assessment_method': 'manual',
                'description': 'Information security policy shall be defined, approved by management, published and communicated to employees and relevant external parties.'
            },
            {
                'id': 'A.6.1.1',
                'name': 'Information Security Roles and Responsibilities',
                'assessment_method': 'manual',
                'description': 'All information security responsibilities shall be defined and allocated.'
            },
            {
                'id': 'A.8.1.1',
                'name': 'Inventory of Assets',
                'assessment_method': 'automated',
                'description': 'Assets associated with information and information processing facilities shall be identified and an inventory of these assets shall be drawn up and maintained.'
            },
            {
                'id': 'A.9.1.1',
                'name': 'Access Control Policy',
                'assessment_method': 'automated',
                'description': 'An access control policy shall be established, documented and reviewed based on business and information security requirements.'
            },
            {
                'id': 'A.10.1.1',
                'name': 'Cryptographic Controls',
                'assessment_method': 'automated',
                'description': 'A policy on the use of cryptographic controls for protection of information shall be developed and implemented.'
            },
            {
                'id': 'A.12.1.1',
                'name': 'Operating Procedures',
                'assessment_method': 'manual',
                'description': 'Operating procedures shall be documented and made available to all users who need them.'
            },
            {
                'id': 'A.12.4.1',
                'name': 'Event Logging',
                'assessment_method': 'automated',
                'description': 'Event logs recording user activities, exceptions, faults and information security events shall be produced, kept and regularly reviewed.'
            },
            {
                'id': 'A.16.1.1',
                'name': 'Incident Management Responsibilities',
                'assessment_method': 'automated',
                'description': 'Management responsibilities and procedures shall be established to ensure a quick, effective and orderly response to information security incidents.'
            }
        ]
    
    def _get_soc2_controls(self) -> List[Dict[str, Any]]:
        """Get SOC 2 control requirements"""
        return [
            {
                'id': 'CC1.1',
                'name': 'Control Environment',
                'assessment_method': 'manual',
                'description': 'The entity demonstrates a commitment to integrity and ethical values.'
            },
            {
                'id': 'CC2.1',
                'name': 'Communication and Information',
                'assessment_method': 'manual',
                'description': 'The entity obtains or generates and uses relevant, quality information to support the functioning of internal control.'
            },
            {
                'id': 'CC6.1',
                'name': 'Logical and Physical Access Controls',
                'assessment_method': 'automated',
                'description': 'The entity implements logical access security software, infrastructure, and architectures over protected information assets.'
            },
            {
                'id': 'CC6.7',
                'name': 'Data Transmission',
                'assessment_method': 'automated',
                'description': 'The entity restricts the transmission, movement, and removal of information to authorized internal and external users and processes.'
            },
            {
                'id': 'CC7.1',
                'name': 'System Operations',
                'assessment_method': 'automated',
                'description': 'To meet its objectives, the entity uses detection and monitoring procedures to identify anomalies, errors, and suspicious activities.'
            }
        ]
    
    def _get_gdpr_requirements(self) -> List[Dict[str, Any]]:
        """Get GDPR compliance requirements"""
        return [
            {
                'id': 'Art.25',
                'name': 'Data Protection by Design and by Default',
                'assessment_method': 'manual',
                'description': 'The controller shall implement appropriate technical and organisational measures to ensure data protection principles are integrated into processing activities.'
            },
            {
                'id': 'Art.32',
                'name': 'Security of Processing',
                'assessment_method': 'automated',
                'description': 'The controller and processor shall implement appropriate technical and organisational measures to ensure a level of security appropriate to the risk.'
            },
            {
                'id': 'Art.33',
                'name': 'Notification of Personal Data Breach',
                'assessment_method': 'automated',
                'description': 'In the case of a personal data breach, the controller shall without undue delay notify the supervisory authority.'
            },
            {
                'id': 'Art.35',
                'name': 'Data Protection Impact Assessment',
                'assessment_method': 'manual',
                'description': 'Where processing is likely to result in a high risk to rights and freedoms, the controller shall carry out an assessment of the impact.'
            }
        ]
    
    def _get_nist_controls(self) -> List[Dict[str, Any]]:
        """Get NIST Cybersecurity Framework controls"""
        return [
            {
                'id': 'ID.AM-1',
                'name': 'Asset Management',
                'assessment_method': 'automated',
                'description': 'Physical devices and systems within the organization are inventoried.'
            },
            {
                'id': 'PR.AC-1',
                'name': 'Access Control',
                'assessment_method': 'automated',
                'description': 'Identities and credentials are issued, managed, verified, revoked, and audited for authorized devices, users and processes.'
            },
            {
                'id': 'PR.DS-1',
                'name': 'Data Security',
                'assessment_method': 'automated',
                'description': 'Data-at-rest is protected.'
            },
            {
                'id': 'DE.AE-1',
                'name': 'Anomalies and Events',
                'assessment_method': 'automated',
                'description': 'A baseline of network operations and expected data flows for users and systems is established and managed.'
            },
            {
                'id': 'RS.RP-1',
                'name': 'Response Planning',
                'assessment_method': 'automated',
                'description': 'Response plan is executed during or after an incident.'
            }
        ]
    
    def _get_pci_dss_requirements(self) -> List[Dict[str, Any]]:
        """Get PCI DSS requirements"""
        return [
            {
                'id': 'Req.1',
                'name': 'Firewall Configuration',
                'assessment_method': 'manual',
                'description': 'Install and maintain a firewall configuration to protect cardholder data.'
            },
            {
                'id': 'Req.2',
                'name': 'Default Passwords',
                'assessment_method': 'automated',
                'description': 'Do not use vendor-supplied defaults for system passwords and other security parameters.'
            },
            {
                'id': 'Req.3',
                'name': 'Cardholder Data Protection',
                'assessment_method': 'automated',
                'description': 'Protect stored cardholder data.'
            },
            {
                'id': 'Req.4',
                'name': 'Data Transmission Encryption',
                'assessment_method': 'automated',
                'description': 'Encrypt transmission of cardholder data across open, public networks.'
            }
        ]
    
    def _get_hipaa_requirements(self) -> List[Dict[str, Any]]:
        """Get HIPAA compliance requirements"""
        return [
            {
                'id': '164.308(a)(1)',
                'name': 'Security Officer',
                'assessment_method': 'manual',
                'description': 'Assign security responsibility to an individual.'
            },
            {
                'id': '164.308(a)(3)',
                'name': 'Workforce Training',
                'assessment_method': 'manual',
                'description': 'Implement procedures to authorize access to electronic protected health information.'
            },
            {
                'id': '164.312(a)(1)',
                'name': 'Access Control',
                'assessment_method': 'automated',
                'description': 'Implement technical policies and procedures for electronic information systems.'
            },
            {
                'id': '164.312(b)',
                'name': 'Audit Controls',
                'assessment_method': 'automated',
                'description': 'Implement hardware, software, and/or procedural mechanisms that record and examine access.'
            }
        ]


# Global instance
compliance_framework = ComplianceFramework()

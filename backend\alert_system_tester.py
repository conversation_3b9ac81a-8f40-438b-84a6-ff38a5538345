#!/usr/bin/env python
"""
TrustVault - Alert System Tester
Test Prometheus/Alertmanager integration and alert rules
"""

import os
import sys
import django
import requests
import time
import json
from datetime import datetime
import threading
from concurrent.futures import ThreadPoolExecutor

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'trustvault.settings')
django.setup()

from django.contrib.auth import get_user_model
from apps.security.models import SecurityEvent

User = get_user_model()

class AlertSystemTester:
    def __init__(self):
        self.prometheus_url = "http://localhost:9090"
        self.alertmanager_url = "http://localhost:9093"
        self.app_url = "http://localhost:8000"
        self.results = []
        
    def log_result(self, test_name, status, details):
        """Log test result."""
        result = {
            'test': test_name,
            'status': status,
            'details': details,
            'timestamp': datetime.now().isoformat()
        }
        self.results.append(result)
        status_icon = "✅" if status == "PASS" else "❌" if status == "FAIL" else "⚠️"
        print(f"{status_icon} {test_name}: {details}")

    def check_prometheus_connection(self):
        """Check if Prometheus is accessible."""
        print("\n🔍 Checking Prometheus Connection")
        print("=" * 40)
        
        try:
            response = requests.get(f"{self.prometheus_url}/api/v1/query", 
                params={'query': 'up'}, timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('status') == 'success':
                    targets = len(data.get('data', {}).get('result', []))
                    self.log_result("Prometheus Connection", "PASS", 
                        f"Connected successfully, {targets} targets monitored")
                    return True
                else:
                    self.log_result("Prometheus Connection", "FAIL", 
                        "Connected but query failed")
                    return False
            else:
                self.log_result("Prometheus Connection", "FAIL", 
                    f"HTTP {response.status_code}")
                return False
                
        except requests.exceptions.RequestException as e:
            self.log_result("Prometheus Connection", "FAIL", 
                f"Connection error: {e}")
            return False

    def check_alertmanager_connection(self):
        """Check if Alertmanager is accessible."""
        print("\n📢 Checking Alertmanager Connection")
        print("=" * 40)
        
        try:
            response = requests.get(f"{self.alertmanager_url}/api/v1/status", timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('status') == 'success':
                    config_hash = data.get('data', {}).get('configHash', 'unknown')
                    self.log_result("Alertmanager Connection", "PASS", 
                        f"Connected successfully, config hash: {config_hash[:8]}")
                    return True
                else:
                    self.log_result("Alertmanager Connection", "FAIL", 
                        "Connected but status check failed")
                    return False
            else:
                self.log_result("Alertmanager Connection", "FAIL", 
                    f"HTTP {response.status_code}")
                return False
                
        except requests.exceptions.RequestException as e:
            self.log_result("Alertmanager Connection", "FAIL", 
                f"Connection error: {e}")
            return False

    def check_alert_rules(self):
        """Check if alert rules are loaded."""
        print("\n📋 Checking Alert Rules")
        print("=" * 30)
        
        try:
            response = requests.get(f"{self.prometheus_url}/api/v1/rules", timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('status') == 'success':
                    groups = data.get('data', {}).get('groups', [])
                    total_rules = sum(len(group.get('rules', [])) for group in groups)
                    
                    security_rules = 0
                    for group in groups:
                        if 'security' in group.get('name', '').lower():
                            security_rules += len(group.get('rules', []))
                    
                    self.log_result("Alert Rules", "PASS", 
                        f"{total_rules} total rules, {security_rules} security rules")
                    return True
                else:
                    self.log_result("Alert Rules", "FAIL", "Rules query failed")
                    return False
            else:
                self.log_result("Alert Rules", "FAIL", f"HTTP {response.status_code}")
                return False
                
        except requests.exceptions.RequestException as e:
            self.log_result("Alert Rules", "FAIL", f"Connection error: {e}")
            return False

    def check_active_alerts(self):
        """Check currently active alerts."""
        print("\n🚨 Checking Active Alerts")
        print("=" * 30)
        
        try:
            response = requests.get(f"{self.prometheus_url}/api/v1/alerts", timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('status') == 'success':
                    alerts = data.get('data', {}).get('alerts', [])
                    
                    active_alerts = [a for a in alerts if a.get('state') == 'firing']
                    pending_alerts = [a for a in alerts if a.get('state') == 'pending']
                    
                    self.log_result("Active Alerts", "PASS", 
                        f"{len(active_alerts)} firing, {len(pending_alerts)} pending")
                    
                    # Show critical alerts
                    critical_alerts = [a for a in active_alerts 
                                     if a.get('labels', {}).get('severity') == 'critical']
                    
                    if critical_alerts:
                        print("   🔴 Critical Alerts:")
                        for alert in critical_alerts[:3]:  # Show first 3
                            name = alert.get('labels', {}).get('alertname', 'Unknown')
                            print(f"      - {name}")
                    
                    return True
                else:
                    self.log_result("Active Alerts", "FAIL", "Alerts query failed")
                    return False
            else:
                self.log_result("Active Alerts", "FAIL", f"HTTP {response.status_code}")
                return False
                
        except requests.exceptions.RequestException as e:
            self.log_result("Active Alerts", "FAIL", f"Connection error: {e}")
            return False

    def trigger_test_alert(self):
        """Trigger a test alert by simulating high load."""
        print("\n⚡ Triggering Test Alert")
        print("=" * 30)
        
        def make_requests():
            """Make rapid requests to trigger rate limiting alert."""
            session = requests.Session()
            for i in range(50):
                try:
                    session.get(f"{self.app_url}/api/v1/alerts/alerts/choices/", timeout=1)
                except:
                    pass
                time.sleep(0.01)  # 100 requests per second
        
        # Start multiple threads to generate load
        threads = []
        for _ in range(5):
            thread = threading.Thread(target=make_requests)
            threads.append(thread)
            thread.start()
        
        # Wait for threads to complete
        for thread in threads:
            thread.join()
        
        # Wait for alert to trigger
        time.sleep(30)
        
        # Check if alert was triggered
        try:
            response = requests.get(f"{self.prometheus_url}/api/v1/alerts", timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                alerts = data.get('data', {}).get('alerts', [])
                
                # Look for DDoS or high request rate alerts
                test_alerts = [a for a in alerts 
                             if 'ddos' in a.get('labels', {}).get('alertname', '').lower() or
                                'rate' in a.get('labels', {}).get('alertname', '').lower()]
                
                if test_alerts:
                    self.log_result("Test Alert Trigger", "PASS", 
                        f"Successfully triggered {len(test_alerts)} alerts")
                    return True
                else:
                    self.log_result("Test Alert Trigger", "WARNING", 
                        "No rate-limiting alerts triggered (may need more time)")
                    return False
            else:
                self.log_result("Test Alert Trigger", "FAIL", 
                    "Could not check alerts after trigger")
                return False
                
        except requests.exceptions.RequestException as e:
            self.log_result("Test Alert Trigger", "FAIL", 
                f"Error checking triggered alerts: {e}")
            return False

    def test_alertmanager_routing(self):
        """Test Alertmanager routing configuration."""
        print("\n🎯 Testing Alertmanager Routing")
        print("=" * 40)
        
        try:
            # Get Alertmanager configuration
            response = requests.get(f"{self.alertmanager_url}/api/v1/status", timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                
                # Check if we can get receivers
                receivers_response = requests.get(f"{self.alertmanager_url}/api/v1/receivers", timeout=5)
                
                if receivers_response.status_code == 200:
                    receivers_data = receivers_response.json()
                    receivers = receivers_data.get('data', [])
                    
                    # Look for our configured receivers
                    expected_receivers = ['default-receiver', 'security-team', 'ops-team']
                    found_receivers = [r for r in receivers if r in expected_receivers]
                    
                    self.log_result("Alertmanager Routing", "PASS", 
                        f"Found {len(found_receivers)}/{len(expected_receivers)} configured receivers")
                    return True
                else:
                    self.log_result("Alertmanager Routing", "WARNING", 
                        "Could not verify receivers configuration")
                    return False
            else:
                self.log_result("Alertmanager Routing", "FAIL", 
                    "Could not access Alertmanager status")
                return False
                
        except requests.exceptions.RequestException as e:
            self.log_result("Alertmanager Routing", "FAIL", 
                f"Connection error: {e}")
            return False

    def test_notification_channels(self):
        """Test notification channels."""
        print("\n📧 Testing Notification Channels")
        print("=" * 40)
        
        # This is a basic test - in production you'd want to test actual email delivery
        try:
            # Check if we can access the alertmanager API
            response = requests.get(f"{self.alertmanager_url}/api/v1/alerts", timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                alerts = data.get('data', [])
                
                # Count alerts by receiver
                receivers_used = set()
                for alert in alerts:
                    receiver = alert.get('receiver', 'unknown')
                    receivers_used.add(receiver)
                
                self.log_result("Notification Channels", "PASS", 
                    f"Alertmanager accessible, {len(receivers_used)} receivers in use")
                return True
            else:
                self.log_result("Notification Channels", "WARNING", 
                    "Could not verify notification channels")
                return False
                
        except requests.exceptions.RequestException as e:
            self.log_result("Notification Channels", "FAIL", 
                f"Connection error: {e}")
            return False

    def run_all_tests(self):
        """Run all alert system tests."""
        print("🚀 TrustVault Alert System Test Suite")
        print("=" * 50)
        print(f"Prometheus: {self.prometheus_url}")
        print(f"Alertmanager: {self.alertmanager_url}")
        print(f"Application: {self.app_url}")
        print(f"Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 50)
        
        # Run all tests
        test_results = []
        test_results.append(self.check_prometheus_connection())
        test_results.append(self.check_alertmanager_connection())
        test_results.append(self.check_alert_rules())
        test_results.append(self.check_active_alerts())
        test_results.append(self.test_alertmanager_routing())
        test_results.append(self.test_notification_channels())
        
        # Optional: Trigger test alert (commented out to avoid spam)
        # test_results.append(self.trigger_test_alert())
        
        # Summary
        passed = sum(test_results)
        total = len(test_results)
        
        print("\n" + "=" * 50)
        print("📊 ALERT SYSTEM TEST SUMMARY")
        print("=" * 50)
        print(f"Tests Passed: {passed}/{total}")
        print(f"Success Rate: {(passed/total)*100:.1f}%")
        
        if passed == total:
            print("🎉 ALL ALERT SYSTEM TESTS PASSED!")
        elif passed >= total * 0.8:
            print("✅ ALERT SYSTEM FUNCTIONING WELL")
        elif passed >= total * 0.6:
            print("⚠️ ALERT SYSTEM PARTIALLY FUNCTIONAL")
        else:
            print("❌ ALERT SYSTEM ISSUES - IMMEDIATE ACTION REQUIRED")
        
        print("\n📝 Detailed Results:")
        for result in self.results:
            status_icon = "✅" if result['status'] == "PASS" else "❌" if result['status'] == "FAIL" else "⚠️"
            print(f"   {status_icon} {result['test']}: {result['details']}")
        
        return passed >= total * 0.8


if __name__ == '__main__':
    tester = AlertSystemTester()
    success = tester.run_all_tests()
    
    if success:
        print("\n🎯 Alert system is functioning correctly!")
    else:
        print("\n⚠️ Alert system needs attention!")
    
    sys.exit(0 if success else 1)

# TrustVault - Portfolio Tests

from django.test import TestCase
from django.contrib.auth import get_user_model
from django.urls import reverse
from rest_framework.test import APITestCase
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken
from decimal import Decimal

from .models import Portfolio, Asset, Holding, Transaction

User = get_user_model()


class PortfolioModelTest(TestCase):
    """Test Portfolio model functionality."""
    
    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            username='testuser',
            password='testpass123'
        )
        
        self.portfolio_data = {
            'name': 'Test Portfolio',
            'description': 'A test portfolio',
            'currency': 'USD'
        }
    
    def test_create_portfolio(self):
        """Test creating a portfolio."""
        portfolio = Portfolio.objects.create(
            user=self.user,
            **self.portfolio_data
        )
        
        self.assertEqual(portfolio.user, self.user)
        self.assertEqual(portfolio.name, self.portfolio_data['name'])
        self.assertEqual(portfolio.description, self.portfolio_data['description'])
        self.assertEqual(portfolio.currency, self.portfolio_data['currency'])
        self.assertEqual(portfolio.total_value, Decimal('0.00'))
    
    def test_portfolio_string_representation(self):
        """Test portfolio string representation."""
        portfolio = Portfolio.objects.create(
            user=self.user,
            **self.portfolio_data
        )
        
        expected_str = f"{self.portfolio_data['name']} - {self.user.email}"
        self.assertEqual(str(portfolio), expected_str)


class AssetModelTest(TestCase):
    """Test Asset model functionality."""
    
    def setUp(self):
        self.asset_data = {
            'symbol': 'AAPL',
            'name': 'Apple Inc.',
            'asset_type': 'STOCK',
            'sector': 'Technology',
            'current_price': Decimal('150.00')
        }
    
    def test_create_asset(self):
        """Test creating an asset."""
        asset = Asset.objects.create(**self.asset_data)
        
        self.assertEqual(asset.symbol, self.asset_data['symbol'])
        self.assertEqual(asset.name, self.asset_data['name'])
        self.assertEqual(asset.asset_type, self.asset_data['asset_type'])
        self.assertEqual(asset.sector, self.asset_data['sector'])
        self.assertEqual(asset.current_price, self.asset_data['current_price'])
    
    def test_asset_string_representation(self):
        """Test asset string representation."""
        asset = Asset.objects.create(**self.asset_data)
        
        expected_str = f"{self.asset_data['symbol']} - {self.asset_data['name']}"
        self.assertEqual(str(asset), expected_str)


class HoldingModelTest(TestCase):
    """Test Holding model functionality."""
    
    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            username='testuser',
            password='testpass123'
        )
        
        self.portfolio = Portfolio.objects.create(
            user=self.user,
            name='Test Portfolio',
            currency='USD'
        )
        
        self.asset = Asset.objects.create(
            symbol='AAPL',
            name='Apple Inc.',
            asset_type='STOCK',
            current_price=Decimal('150.00')
        )
    
    def test_create_holding(self):
        """Test creating a holding."""
        holding = Holding.objects.create(
            portfolio=self.portfolio,
            asset=self.asset,
            quantity=Decimal('10.00'),
            average_cost=Decimal('140.00')
        )
        
        self.assertEqual(holding.portfolio, self.portfolio)
        self.assertEqual(holding.asset, self.asset)
        self.assertEqual(holding.quantity, Decimal('10.00'))
        self.assertEqual(holding.average_cost, Decimal('140.00'))
        
        # Test calculated properties
        expected_total_cost = Decimal('10.00') * Decimal('140.00')
        expected_current_value = Decimal('10.00') * Decimal('150.00')
        
        self.assertEqual(holding.total_cost, expected_total_cost)
        self.assertEqual(holding.current_value, expected_current_value)
    
    def test_holding_string_representation(self):
        """Test holding string representation."""
        holding = Holding.objects.create(
            portfolio=self.portfolio,
            asset=self.asset,
            quantity=Decimal('10.00'),
            average_cost=Decimal('140.00')
        )
        
        expected_str = f"{self.portfolio.name} - {self.asset.symbol} (10.00)"
        self.assertEqual(str(holding), expected_str)


class PortfolioAPITest(APITestCase):
    """Test Portfolio API endpoints."""
    
    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            username='testuser',
            password='testpass123'
        )
        
        # Authenticate user
        refresh = RefreshToken.for_user(self.user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')
        
        self.portfolio_list_url = reverse('portfolio:portfolio-list')
        
        self.portfolio_data = {
            'name': 'Test Portfolio',
            'description': 'A test portfolio',
            'currency': 'USD'
        }
    
    def test_create_portfolio(self):
        """Test creating a portfolio via API."""
        response = self.client.post(self.portfolio_list_url, self.portfolio_data)
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['name'], self.portfolio_data['name'])
        self.assertEqual(response.data['description'], self.portfolio_data['description'])
        self.assertEqual(response.data['currency'], self.portfolio_data['currency'])
        
        # Verify portfolio was created in database
        portfolio = Portfolio.objects.get(id=response.data['id'])
        self.assertEqual(portfolio.user, self.user)
        self.assertEqual(portfolio.name, self.portfolio_data['name'])
    
    def test_list_portfolios(self):
        """Test listing user's portfolios."""
        # Create test portfolios
        Portfolio.objects.create(user=self.user, name='Portfolio 1', currency='USD')
        Portfolio.objects.create(user=self.user, name='Portfolio 2', currency='EUR')
        
        # Create portfolio for another user (should not be included)
        other_user = User.objects.create_user(
            email='<EMAIL>',
            username='otheruser',
            password='testpass123'
        )
        Portfolio.objects.create(user=other_user, name='Other Portfolio', currency='USD')
        
        response = self.client.get(self.portfolio_list_url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 2)  # Only user's portfolios
        
        portfolio_names = [p['name'] for p in response.data]
        self.assertIn('Portfolio 1', portfolio_names)
        self.assertIn('Portfolio 2', portfolio_names)
        self.assertNotIn('Other Portfolio', portfolio_names)
    
    def test_portfolio_detail(self):
        """Test retrieving portfolio details."""
        portfolio = Portfolio.objects.create(
            user=self.user,
            **self.portfolio_data
        )
        
        detail_url = reverse('portfolio:portfolio-detail', kwargs={'pk': portfolio.id})
        response = self.client.get(detail_url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['id'], str(portfolio.id))
        self.assertEqual(response.data['name'], portfolio.name)
    
    def test_portfolio_detail_unauthorized(self):
        """Test accessing another user's portfolio."""
        other_user = User.objects.create_user(
            email='<EMAIL>',
            username='otheruser',
            password='testpass123'
        )
        
        portfolio = Portfolio.objects.create(
            user=other_user,
            name='Other Portfolio',
            currency='USD'
        )
        
        detail_url = reverse('portfolio:portfolio-detail', kwargs={'pk': portfolio.id})
        response = self.client.get(detail_url)
        
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)


class AssetAPITest(APITestCase):
    """Test Asset API endpoints."""
    
    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            username='testuser',
            password='testpass123'
        )
        
        # Authenticate user
        refresh = RefreshToken.for_user(self.user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')
        
        self.asset_list_url = reverse('portfolio:asset-list')
        
        # Create test assets
        Asset.objects.create(
            symbol='AAPL',
            name='Apple Inc.',
            asset_type='STOCK',
            sector='Technology',
            current_price=Decimal('150.00')
        )
        Asset.objects.create(
            symbol='GOOGL',
            name='Alphabet Inc.',
            asset_type='STOCK',
            sector='Technology',
            current_price=Decimal('2500.00')
        )
        Asset.objects.create(
            symbol='BTC',
            name='Bitcoin',
            asset_type='CRYPTO',
            current_price=Decimal('45000.00')
        )
    
    def test_list_assets(self):
        """Test listing all assets."""
        response = self.client.get(self.asset_list_url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 3)
        
        symbols = [asset['symbol'] for asset in response.data]
        self.assertIn('AAPL', symbols)
        self.assertIn('GOOGL', symbols)
        self.assertIn('BTC', symbols)
    
    def test_filter_assets_by_type(self):
        """Test filtering assets by type."""
        response = self.client.get(self.asset_list_url, {'type': 'STOCK'})
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 2)
        
        for asset in response.data:
            self.assertEqual(asset['asset_type'], 'STOCK')
    
    def test_search_assets(self):
        """Test searching assets by name or symbol."""
        response = self.client.get(self.asset_list_url, {'search': 'Apple'})
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['symbol'], 'AAPL')


class TransactionModelTest(TestCase):
    """Test Transaction model functionality."""
    
    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            username='testuser',
            password='testpass123'
        )
        
        self.portfolio = Portfolio.objects.create(
            user=self.user,
            name='Test Portfolio',
            currency='USD'
        )
        
        self.asset = Asset.objects.create(
            symbol='AAPL',
            name='Apple Inc.',
            asset_type='STOCK',
            current_price=Decimal('150.00')
        )
    
    def test_create_transaction(self):
        """Test creating a transaction."""
        transaction = Transaction.objects.create(
            portfolio=self.portfolio,
            asset=self.asset,
            transaction_type='BUY',
            quantity=Decimal('10.00'),
            price=Decimal('145.00'),
            fees=Decimal('5.00'),
            notes='Test purchase'
        )
        
        self.assertEqual(transaction.portfolio, self.portfolio)
        self.assertEqual(transaction.asset, self.asset)
        self.assertEqual(transaction.transaction_type, 'BUY')
        self.assertEqual(transaction.quantity, Decimal('10.00'))
        self.assertEqual(transaction.price, Decimal('145.00'))
        self.assertEqual(transaction.fees, Decimal('5.00'))
        
        # Test calculated total
        expected_total = (Decimal('10.00') * Decimal('145.00')) + Decimal('5.00')
        self.assertEqual(transaction.total_amount, expected_total)
    
    def test_transaction_string_representation(self):
        """Test transaction string representation."""
        transaction = Transaction.objects.create(
            portfolio=self.portfolio,
            asset=self.asset,
            transaction_type='BUY',
            quantity=Decimal('10.00'),
            price=Decimal('145.00')
        )
        
        expected_str = f"BUY 10.00 {self.asset.symbol} @ $145.00"
        self.assertEqual(str(transaction), expected_str)

// TrustVault - Settings Page

import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  Button,
  Alert,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  CircularProgress,
  Chip,
  Divider,
} from '@mui/material';
import {
  Settings,
  Notifications,
  Security,
  Delete,
  Save,
  Download,
  Devices,
  Key,
  Brightness4,
  Brightness7,
  BrightnessAuto,
} from '@mui/icons-material';
import { Helmet } from 'react-helmet-async';
import { toast } from 'react-hot-toast';
import { useMutation, useQueryClient, useQuery } from 'react-query';

// Store
import { useAuthStore } from '../../store/authStore';

// Contexts
import { useTheme } from '../../contexts/ThemeContext';

// Services
import apiService from '../../services/api';

interface UserSettings {
  timezone: string;
  language: string;
  theme: 'light' | 'dark' | 'auto';
  notifications: {
    email: boolean;
    push: boolean;
    security: boolean;
    portfolio: boolean;
    marketing: boolean;
    news: boolean;
    email_frequency: 'immediate' | 'daily' | 'weekly' | 'never';
    push_enabled: boolean;
  };
  privacy: {
    profile_visibility: 'public' | 'private';
    portfolio_visibility: 'public' | 'private';
    data_sharing: boolean;
    analytics: boolean;
  };
}

const SettingsPage: React.FC = () => {
  const { user } = useAuthStore();
  const queryClient = useQueryClient();
  const { themeMode, setThemeMode, isDarkMode } = useTheme();

  const [settings, setSettings] = useState<UserSettings>({
    timezone: user?.timezone || 'UTC',
    language: user?.language || 'en',
    theme: themeMode,
    notifications: {
      email: true,
      push: true,
      security: true,
      portfolio: true,
      marketing: false,
      news: true,
      email_frequency: 'daily',
      push_enabled: true,
    },
    privacy: {
      profile_visibility: 'private',
      portfolio_visibility: 'private',
      data_sharing: false,
      analytics: true,
    },
  });

  const [deleteAccountDialog, setDeleteAccountDialog] = useState(false);
  const [deleteConfirmation, setDeleteConfirmation] = useState('');
  const [sessionsDialog, setSessionsDialog] = useState(false);
  const [exportingData, setExportingData] = useState(false);
  const [apiKeysDialog, setApiKeysDialog] = useState(false);
  const [createApiKeyDialog, setCreateApiKeyDialog] = useState(false);
  const [newApiKeyName, setNewApiKeyName] = useState('');
  const [newApiKeyPermissions, setNewApiKeyPermissions] = useState<string[]>([]);

  // Load user settings
  const { data: userSettings, isLoading: settingsLoading } = useQuery(
    'user-settings',
    apiService.getUserSettings,
    {
      onSuccess: (data) => {
        setSettings(prev => ({ ...prev, ...data }));
      },
      onError: () => {
        // Use default settings if API fails
      },
    }
  );

  // Load active sessions
  const { data: activeSessions, refetch: refetchSessions } = useQuery(
    'active-sessions',
    apiService.getActiveSessions,
    {
      enabled: sessionsDialog,
      onError: () => {
        toast.error('Failed to load active sessions');
      },
    }
  );

  // Load API keys
  const { data: apiKeys, refetch: refetchApiKeys } = useQuery(
    'api-keys',
    apiService.getApiKeys,
    {
      enabled: apiKeysDialog,
      onError: () => {
        toast.error('Failed to load API keys');
      },
    }
  );

  const updateSettingsMutation = useMutation(
    (data: Partial<UserSettings>) => apiService.updateUserSettings(data),
    {
      onSuccess: () => {
        toast.success('Settings updated successfully');
        queryClient.invalidateQueries(['user-settings', 'user-profile']);
      },
      onError: () => {
        toast.error('Failed to update settings');
      },
    }
  );

  const deleteAccountMutation = useMutation(
    (confirmation: string) => apiService.deleteAccount(confirmation),
    {
      onSuccess: () => {
        toast.success('Account deleted successfully');
        // Redirect to login or home page
        window.location.href = '/';
      },
      onError: (error: any) => {
        const message = error.response?.data?.message || 'Failed to delete account';
        toast.error(message);
      },
    }
  );

  const revokeSessionMutation = useMutation(
    (sessionId: string) => apiService.revokeSession(sessionId),
    {
      onSuccess: () => {
        toast.success('Session revoked successfully');
        refetchSessions();
      },
      onError: () => {
        toast.error('Failed to revoke session');
      },
    }
  );

  const revokeAllSessionsMutation = useMutation(
    () => apiService.revokeAllSessions(),
    {
      onSuccess: () => {
        toast.success('All sessions revoked successfully');
        refetchSessions();
      },
      onError: () => {
        toast.error('Failed to revoke all sessions');
      },
    }
  );

  const createApiKeyMutation = useMutation(
    ({ name, permissions }: { name: string; permissions: string[] }) =>
      apiService.createApiKey(name, permissions),
    {
      onSuccess: () => {
        toast.success('API key created successfully');
        refetchApiKeys();
        setCreateApiKeyDialog(false);
        setNewApiKeyName('');
        setNewApiKeyPermissions([]);
      },
      onError: () => {
        toast.error('Failed to create API key');
      },
    }
  );

  const revokeApiKeyMutation = useMutation(
    (keyId: string) => apiService.revokeApiKey(keyId),
    {
      onSuccess: () => {
        toast.success('API key revoked successfully');
        refetchApiKeys();
      },
      onError: () => {
        toast.error('Failed to revoke API key');
      },
    }
  );

  const handleSettingChange = (category: keyof UserSettings, key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [category]: typeof prev[category] === 'object' && prev[category] !== null
        ? { ...(prev[category] as Record<string, any>), [key]: value }
        : value
    }));
  };

  const handleThemeChange = (newTheme: 'light' | 'dark' | 'auto') => {
    setThemeMode(newTheme);
    handleSettingChange('theme', '', newTheme);
  };

  const handleSaveSettings = () => {
    updateSettingsMutation.mutate(settings);
  };

  const handleExportData = async () => {
    setExportingData(true);
    try {
      const blob = await apiService.exportUserData();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `trustvault-data-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      toast.success('Data exported successfully');
    } catch (error) {
      toast.error('Failed to export data');
    } finally {
      setExportingData(false);
    }
  };

  const handleDeleteAccount = () => {
    if (deleteConfirmation === 'DELETE') {
      deleteAccountMutation.mutate(deleteConfirmation);
    }
  };

  const timezones = [
    { value: 'UTC', label: 'UTC' },
    { value: 'America/New_York', label: 'Eastern Time' },
    { value: 'America/Chicago', label: 'Central Time' },
    { value: 'America/Denver', label: 'Mountain Time' },
    { value: 'America/Los_Angeles', label: 'Pacific Time' },
    { value: 'Europe/London', label: 'London' },
    { value: 'Europe/Paris', label: 'Paris' },
    { value: 'Asia/Tokyo', label: 'Tokyo' },
  ];

  const languages = [
    { value: 'en', label: 'English' },
    { value: 'fr', label: 'Français' },
    { value: 'es', label: 'Español' },
    { value: 'de', label: 'Deutsch' },
  ];

  return (
    <>
      <Helmet>
        <title>Settings - TrustVault</title>
        <meta name="description" content="Manage your account settings and preferences" />
      </Helmet>

      <Box>
        {/* Header */}
        <Box mb={4}>
          <Typography variant="h4" component="h1" gutterBottom>
            Settings
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Manage your account preferences and security settings
          </Typography>
        </Box>

        <Grid container spacing={3}>
          {/* General Settings */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" gap={2} mb={3}>
                  <Settings color="primary" />
                  <Typography variant="h6">General</Typography>
                </Box>

                <Box display="flex" flexDirection="column" gap={3}>
                  <FormControl fullWidth>
                    <InputLabel>Timezone</InputLabel>
                    <Select
                      value={settings.timezone}
                      onChange={(e) => handleSettingChange('timezone', '', e.target.value)}
                      label="Timezone"
                    >
                      {timezones.map((tz) => (
                        <MenuItem key={tz.value} value={tz.value}>
                          {tz.label}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>

                  <FormControl fullWidth>
                    <InputLabel>Language</InputLabel>
                    <Select
                      value={settings.language}
                      onChange={(e) => handleSettingChange('language', '', e.target.value)}
                      label="Language"
                    >
                      {languages.map((lang) => (
                        <MenuItem key={lang.value} value={lang.value}>
                          {lang.label}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>

                  <FormControl fullWidth>
                    <InputLabel>Theme</InputLabel>
                    <Select
                      value={themeMode}
                      onChange={(e) => handleThemeChange(e.target.value as 'light' | 'dark' | 'auto')}
                      label="Theme"
                    >
                      <MenuItem value="light">
                        <Box display="flex" alignItems="center" gap={1}>
                          <Brightness7 fontSize="small" />
                          Light
                        </Box>
                      </MenuItem>
                      <MenuItem value="dark">
                        <Box display="flex" alignItems="center" gap={1}>
                          <Brightness4 fontSize="small" />
                          Dark
                        </Box>
                      </MenuItem>
                      <MenuItem value="auto">
                        <Box display="flex" alignItems="center" gap={1}>
                          <BrightnessAuto fontSize="small" />
                          Auto (System)
                        </Box>
                      </MenuItem>
                    </Select>
                  </FormControl>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* Notification Settings */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" gap={2} mb={3}>
                  <Notifications color="primary" />
                  <Typography variant="h6">Notifications</Typography>
                </Box>

                <Box display="flex" flexDirection="column" gap={2} mb={3}>
                  <FormControl fullWidth>
                    <InputLabel>Email Frequency</InputLabel>
                    <Select
                      value={settings.notifications.email_frequency}
                      onChange={(e) => handleSettingChange('notifications', 'email_frequency', e.target.value)}
                      label="Email Frequency"
                    >
                      <MenuItem value="immediate">Immediate</MenuItem>
                      <MenuItem value="daily">Daily Digest</MenuItem>
                      <MenuItem value="weekly">Weekly Summary</MenuItem>
                      <MenuItem value="never">Never</MenuItem>
                    </Select>
                  </FormControl>
                </Box>

                <List>
                  <ListItem>
                    <ListItemText
                      primary="Email Notifications"
                      secondary="Receive updates via email"
                    />
                    <ListItemSecondaryAction>
                      <Switch
                        checked={settings.notifications.email}
                        onChange={(e) => handleSettingChange('notifications', 'email', e.target.checked)}
                      />
                    </ListItemSecondaryAction>
                  </ListItem>

                  <ListItem>
                    <ListItemText
                      primary="Push Notifications"
                      secondary="Browser push notifications"
                    />
                    <ListItemSecondaryAction>
                      <Switch
                        checked={settings.notifications.push}
                        onChange={(e) => handleSettingChange('notifications', 'push', e.target.checked)}
                      />
                    </ListItemSecondaryAction>
                  </ListItem>

                  <ListItem>
                    <ListItemText
                      primary="Security Alerts"
                      secondary="Important security notifications (always enabled)"
                    />
                    <ListItemSecondaryAction>
                      <Switch
                        checked={true}
                        disabled={true}
                      />
                    </ListItemSecondaryAction>
                  </ListItem>

                  <ListItem>
                    <ListItemText
                      primary="Portfolio Updates"
                      secondary="Portfolio performance notifications"
                    />
                    <ListItemSecondaryAction>
                      <Switch
                        checked={settings.notifications.portfolio}
                        onChange={(e) => handleSettingChange('notifications', 'portfolio', e.target.checked)}
                      />
                    </ListItemSecondaryAction>
                  </ListItem>

                  <ListItem>
                    <ListItemText
                      primary="Marketing Communications"
                      secondary="Product updates and promotional content"
                    />
                    <ListItemSecondaryAction>
                      <Switch
                        checked={settings.notifications.marketing}
                        onChange={(e) => handleSettingChange('notifications', 'marketing', e.target.checked)}
                      />
                    </ListItemSecondaryAction>
                  </ListItem>

                  <ListItem>
                    <ListItemText
                      primary="Market News"
                      secondary="Financial market news and insights"
                    />
                    <ListItemSecondaryAction>
                      <Switch
                        checked={settings.notifications.news}
                        onChange={(e) => handleSettingChange('notifications', 'news', e.target.checked)}
                      />
                    </ListItemSecondaryAction>
                  </ListItem>
                </List>
              </CardContent>
            </Card>
          </Grid>

          {/* Privacy Settings */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" gap={2} mb={3}>
                  <Security color="primary" />
                  <Typography variant="h6">Privacy</Typography>
                </Box>

                <Box display="flex" flexDirection="column" gap={3}>
                  <FormControl fullWidth>
                    <InputLabel>Profile Visibility</InputLabel>
                    <Select
                      value={settings.privacy.profile_visibility}
                      onChange={(e) => handleSettingChange('privacy', 'profile_visibility', e.target.value)}
                      label="Profile Visibility"
                    >
                      <MenuItem value="public">Public</MenuItem>
                      <MenuItem value="private">Private</MenuItem>
                    </Select>
                  </FormControl>

                  <FormControl fullWidth>
                    <InputLabel>Portfolio Visibility</InputLabel>
                    <Select
                      value={settings.privacy.portfolio_visibility}
                      onChange={(e) => handleSettingChange('privacy', 'portfolio_visibility', e.target.value)}
                      label="Portfolio Visibility"
                    >
                      <MenuItem value="public">Public</MenuItem>
                      <MenuItem value="private">Private</MenuItem>
                    </Select>
                  </FormControl>
                </Box>

                <Divider sx={{ my: 2 }} />

                <List>
                  <ListItem>
                    <ListItemText
                      primary="Data Sharing"
                      secondary="Allow anonymized data sharing for research"
                    />
                    <ListItemSecondaryAction>
                      <Switch
                        checked={settings.privacy.data_sharing}
                        onChange={(e) => handleSettingChange('privacy', 'data_sharing', e.target.checked)}
                      />
                    </ListItemSecondaryAction>
                  </ListItem>

                  <ListItem>
                    <ListItemText
                      primary="Analytics"
                      secondary="Help improve our service with usage analytics"
                    />
                    <ListItemSecondaryAction>
                      <Switch
                        checked={settings.privacy.analytics}
                        onChange={(e) => handleSettingChange('privacy', 'analytics', e.target.checked)}
                      />
                    </ListItemSecondaryAction>
                  </ListItem>
                </List>
              </CardContent>
            </Card>
          </Grid>

          {/* Session Management */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" gap={2} mb={3}>
                  <Devices color="primary" />
                  <Typography variant="h6">Session Management</Typography>
                </Box>

                <Typography variant="body2" color="text.secondary" mb={2}>
                  Manage your active sessions and devices
                </Typography>

                <Box display="flex" flexDirection="column" gap={2}>
                  <Button
                    variant="outlined"
                    startIcon={<Devices />}
                    onClick={() => setSessionsDialog(true)}
                    fullWidth
                  >
                    View Active Sessions
                  </Button>

                  <Button
                    variant="outlined"
                    color="warning"
                    onClick={() => revokeAllSessionsMutation.mutate()}
                    disabled={revokeAllSessionsMutation.isLoading}
                    fullWidth
                  >
                    {revokeAllSessionsMutation.isLoading ? 'Revoking...' : 'Logout All Devices'}
                  </Button>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* Data Management */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" gap={2} mb={3}>
                  <Download color="primary" />
                  <Typography variant="h6">Data Management</Typography>
                </Box>

                <Typography variant="body2" color="text.secondary" mb={2}>
                  Export your data or manage your account
                </Typography>

                <Box display="flex" flexDirection="column" gap={2}>
                  <Button
                    variant="outlined"
                    startIcon={<Download />}
                    onClick={handleExportData}
                    disabled={exportingData}
                    fullWidth
                  >
                    {exportingData ? 'Exporting...' : 'Export My Data'}
                  </Button>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* API Key Management */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" gap={2} mb={3}>
                  <Key color="primary" />
                  <Typography variant="h6">API Keys</Typography>
                </Box>

                <Typography variant="body2" color="text.secondary" mb={2}>
                  Manage API keys for third-party integrations
                </Typography>

                <Box display="flex" flexDirection="column" gap={2}>
                  <Button
                    variant="outlined"
                    startIcon={<Key />}
                    onClick={() => setApiKeysDialog(true)}
                    fullWidth
                  >
                    Manage API Keys
                  </Button>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* Danger Zone */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" gap={2} mb={3}>
                  <Delete color="error" />
                  <Typography variant="h6" color="error">Danger Zone</Typography>
                </Box>

                <Alert severity="warning" sx={{ mb: 2 }}>
                  These actions cannot be undone. Please be careful.
                </Alert>

                <Button
                  variant="outlined"
                  color="error"
                  startIcon={<Delete />}
                  onClick={() => setDeleteAccountDialog(true)}
                  fullWidth
                >
                  Delete Account
                </Button>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Save Button */}
        <Box mt={4} display="flex" justifyContent="flex-end">
          <Button
            variant="contained"
            startIcon={<Save />}
            onClick={handleSaveSettings}
            disabled={updateSettingsMutation.isLoading}
          >
            {updateSettingsMutation.isLoading ? 'Saving...' : 'Save Settings'}
          </Button>
        </Box>

        {/* Delete Account Dialog */}
        <Dialog
          open={deleteAccountDialog}
          onClose={() => setDeleteAccountDialog(false)}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle color="error">Delete Account</DialogTitle>
          <DialogContent>
            <Alert severity="error" sx={{ mb: 2 }}>
              This action will permanently delete your account and all associated data.
              This cannot be undone.
            </Alert>
            
            <Typography variant="body2" gutterBottom>
              To confirm, please type "DELETE" in the field below:
            </Typography>
            
            <TextField
              fullWidth
              value={deleteConfirmation}
              onChange={(e) => setDeleteConfirmation(e.target.value)}
              placeholder="Type DELETE to confirm"
              sx={{ mt: 2 }}
            />
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setDeleteAccountDialog(false)}>
              Cancel
            </Button>
            <Button
              color="error"
              variant="contained"
              disabled={deleteConfirmation !== 'DELETE' || deleteAccountMutation.isLoading}
              onClick={handleDeleteAccount}
              startIcon={deleteAccountMutation.isLoading ? <CircularProgress size={16} /> : <Delete />}
            >
              {deleteAccountMutation.isLoading ? 'Deleting...' : 'Delete Account'}
            </Button>
          </DialogActions>
        </Dialog>

        {/* Active Sessions Dialog */}
        <Dialog
          open={sessionsDialog}
          onClose={() => setSessionsDialog(false)}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle>Active Sessions</DialogTitle>
          <DialogContent>
            {activeSessions && activeSessions.length > 0 ? (
              <List>
                {activeSessions.map((session: any) => (
                  <ListItem key={session.id} divider>
                    <ListItemText
                      primary={
                        <Box display="flex" alignItems="center" gap={1}>
                          <Typography variant="body1">
                            {session.device_name || 'Unknown Device'}
                          </Typography>
                          {session.is_current && (
                            <Chip label="Current" color="primary" size="small" />
                          )}
                        </Box>
                      }
                      secondary={
                        <Box>
                          <Typography variant="body2" color="text.secondary">
                            IP: {session.ip_address}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            Last active: {new Date(session.last_activity).toLocaleString()}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            Location: {session.location || 'Unknown'}
                          </Typography>
                        </Box>
                      }
                    />
                    <ListItemSecondaryAction>
                      {!session.is_current && (
                        <Button
                          color="error"
                          size="small"
                          onClick={() => revokeSessionMutation.mutate(session.id)}
                          disabled={revokeSessionMutation.isLoading}
                        >
                          Revoke
                        </Button>
                      )}
                    </ListItemSecondaryAction>
                  </ListItem>
                ))}
              </List>
            ) : (
              <Typography>No active sessions found.</Typography>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setSessionsDialog(false)}>
              Close
            </Button>
          </DialogActions>
        </Dialog>

        {/* API Keys Dialog */}
        <Dialog
          open={apiKeysDialog}
          onClose={() => setApiKeysDialog(false)}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle>
            <Box display="flex" justifyContent="space-between" alignItems="center">
              API Keys
              <Button
                variant="contained"
                size="small"
                startIcon={<Key />}
                onClick={() => setCreateApiKeyDialog(true)}
              >
                Create New Key
              </Button>
            </Box>
          </DialogTitle>
          <DialogContent>
            {apiKeys && apiKeys.length > 0 ? (
              <List>
                {apiKeys.map((key: any) => (
                  <ListItem key={key.id} divider>
                    <ListItemText
                      primary={key.name}
                      secondary={
                        <Box>
                          <Typography variant="body2" color="text.secondary">
                            Created: {new Date(key.created_at).toLocaleDateString()}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            Last used: {key.last_used ? new Date(key.last_used).toLocaleDateString() : 'Never'}
                          </Typography>
                          <Box display="flex" gap={1} mt={1}>
                            {key.permissions.map((permission: string) => (
                              <Chip key={permission} label={permission} size="small" />
                            ))}
                          </Box>
                        </Box>
                      }
                    />
                    <ListItemSecondaryAction>
                      <Button
                        color="error"
                        size="small"
                        onClick={() => revokeApiKeyMutation.mutate(key.id)}
                        disabled={revokeApiKeyMutation.isLoading}
                      >
                        Revoke
                      </Button>
                    </ListItemSecondaryAction>
                  </ListItem>
                ))}
              </List>
            ) : (
              <Typography>No API keys found.</Typography>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setApiKeysDialog(false)}>
              Close
            </Button>
          </DialogActions>
        </Dialog>

        {/* Create API Key Dialog */}
        <Dialog
          open={createApiKeyDialog}
          onClose={() => setCreateApiKeyDialog(false)}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>Create New API Key</DialogTitle>
          <DialogContent>
            <Box display="flex" flexDirection="column" gap={3} mt={1}>
              <TextField
                label="Key Name"
                value={newApiKeyName}
                onChange={(e) => setNewApiKeyName(e.target.value)}
                fullWidth
                placeholder="e.g., Mobile App Integration"
              />

              <FormControl fullWidth>
                <InputLabel>Permissions</InputLabel>
                <Select
                  multiple
                  value={newApiKeyPermissions}
                  onChange={(e) => setNewApiKeyPermissions(e.target.value as string[])}
                  label="Permissions"
                  renderValue={(selected) => (
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                      {(selected as string[]).map((value) => (
                        <Chip key={value} label={value} size="small" />
                      ))}
                    </Box>
                  )}
                >
                  <MenuItem value="read:portfolio">Read Portfolio</MenuItem>
                  <MenuItem value="write:portfolio">Write Portfolio</MenuItem>
                  <MenuItem value="read:transactions">Read Transactions</MenuItem>
                  <MenuItem value="write:transactions">Write Transactions</MenuItem>
                  <MenuItem value="read:analytics">Read Analytics</MenuItem>
                </Select>
              </FormControl>
            </Box>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setCreateApiKeyDialog(false)}>
              Cancel
            </Button>
            <Button
              variant="contained"
              onClick={() => createApiKeyMutation.mutate({
                name: newApiKeyName,
                permissions: newApiKeyPermissions
              })}
              disabled={!newApiKeyName || newApiKeyPermissions.length === 0 || createApiKeyMutation.isLoading}
            >
              {createApiKeyMutation.isLoading ? 'Creating...' : 'Create Key'}
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </>
  );
};

export default SettingsPage;

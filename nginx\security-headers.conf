# TrustVault - Security Headers Configuration

# ============================================================================
# SECURITY HEADERS - OWASP Recommendations
# ============================================================================

# Prevent clickjacking attacks
add_header X-Frame-Options "SAMEORIGIN" always;

# Prevent MIME type sniffing
add_header X-Content-Type-Options "nosniff" always;

# Enable XSS protection
add_header X-XSS-Protection "1; mode=block" always;

# Control referrer information
add_header Referrer-Policy "strict-origin-when-cross-origin" always;

# Content Security Policy (CSP)
add_header Content-Security-Policy "
    default-src 'self';
    script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdnjs.cloudflare.com;
    style-src 'self' 'unsafe-inline' https://fonts.googleapis.com;
    img-src 'self' data: https: blob:;
    font-src 'self' https://fonts.gstatic.com data:;
    connect-src 'self' https://api.trustvault.local wss://api.trustvault.local;
    media-src 'self';
    object-src 'none';
    child-src 'self';
    frame-src 'self';
    worker-src 'self';
    manifest-src 'self';
    base-uri 'self';
    form-action 'self';
    frame-ancestors 'self';
    upgrade-insecure-requests;
" always;

# HTTP Strict Transport Security (HSTS)
add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;

# Permissions Policy (formerly Feature Policy)
add_header Permissions-Policy "
    geolocation=(),
    microphone=(),
    camera=(),
    magnetometer=(),
    gyroscope=(),
    speaker=(),
    vibrate=(),
    fullscreen=(self),
    payment=()
" always;

# Cross-Origin Embedder Policy
add_header Cross-Origin-Embedder-Policy "require-corp" always;

# Cross-Origin Opener Policy
add_header Cross-Origin-Opener-Policy "same-origin" always;

# Cross-Origin Resource Policy
add_header Cross-Origin-Resource-Policy "same-origin" always;

# Remove server information
more_clear_headers "Server";
more_clear_headers "X-Powered-By";

# Cache control for security-sensitive pages
add_header Cache-Control "no-cache, no-store, must-revalidate" always;
add_header Pragma "no-cache" always;
add_header Expires "0" always;

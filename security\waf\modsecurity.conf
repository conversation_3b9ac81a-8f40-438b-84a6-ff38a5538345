# TrustVault - ModSecurity Advanced Configuration
# This configuration provides comprehensive web application firewall protection

# Basic ModSecurity configuration
SecRuleEngine On
SecRequestBodyAccess On
SecResponseBodyAccess On
SecResponseBodyMimeType text/plain text/html text/xml application/json
SecDefaultAction "phase:1,log,auditlog,pass"

# Request body handling
SecRequestBodyLimit 13107200
SecRequestBodyNoFilesLimit 131072
SecRequestBodyInMemoryLimit 131072
SecRequestBodyLimitAction Reject

# Response body handling
SecResponseBodyLimit 524288
SecResponseBodyLimitAction ProcessPartial

# File upload handling
SecTmpDir /tmp/
SecDataDir /tmp/
SecUploadDir /tmp/
SecUploadFileLimit 100
SecUploadFileMode 0600
SecUploadKeepFiles RelevantOnly

# Debug and audit logging
SecDebugLog /var/log/nginx/modsec_debug.log
SecDebugLogLevel 0
SecAuditEngine RelevantOnly
SecAuditLogRelevantStatus "^(?:5|4(?!04))"
SecAuditLogParts ABDEFHIJZ
SecAuditLogType Serial
SecAuditLog /var/log/nginx/modsec_audit.log

# Argument separator
SecArgumentSeparator &

# Cookie format
SecCookieFormat 0

# Unicode mapping
SecUnicodeMapFile unicode.mapping 20127

# Status engine
SecStatusEngine On

# Collection timeout
SecCollectionTimeout 600

# Rule inclusion
Include /etc/nginx/modsecurity/crs/crs-setup.conf
Include /etc/nginx/modsecurity/crs/rules/*.conf

# Custom TrustVault security rules
SecRule REQUEST_HEADERS:User-Agent "@detectSQLi" \
    "id:1000001,\
    phase:1,\
    block,\
    msg:'SQL Injection Attack Detected in User-Agent',\
    logdata:'Matched Data: %{MATCHED_VAR} found within %{MATCHED_VAR_NAME}',\
    tag:'attack-sqli',\
    tag:'OWASP_CRS',\
    severity:'CRITICAL',\
    setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}',\
    setvar:'tx.sql_injection_score=+1'"

SecRule REQUEST_HEADERS:User-Agent "@detectXSS" \
    "id:1000002,\
    phase:1,\
    block,\
    msg:'XSS Attack Detected in User-Agent',\
    logdata:'Matched Data: %{MATCHED_VAR} found within %{MATCHED_VAR_NAME}',\
    tag:'attack-xss',\
    tag:'OWASP_CRS',\
    severity:'CRITICAL',\
    setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}',\
    setvar:'tx.xss_score=+1'"

# Block common attack tools
SecRule REQUEST_HEADERS:User-Agent "@pmFromFile /etc/nginx/modsecurity/attack-tools.data" \
    "id:1000003,\
    phase:1,\
    block,\
    msg:'Attack Tool Detected',\
    logdata:'Matched Data: %{MATCHED_VAR} found within %{MATCHED_VAR_NAME}',\
    tag:'attack-tool',\
    severity:'WARNING'"

# Rate limiting rules
SecAction \
    "id:1000004,\
    phase:1,\
    nolog,\
    pass,\
    initcol:ip=%{REMOTE_ADDR},\
    setvar:ip.requests_per_minute=+1,\
    expirevar:ip.requests_per_minute=60"

SecRule IP:REQUESTS_PER_MINUTE "@gt 100" \
    "id:1000005,\
    phase:1,\
    block,\
    msg:'Rate Limiting: Too many requests per minute',\
    logdata:'Requests per minute: %{ip.requests_per_minute}',\
    tag:'rate-limiting',\
    severity:'WARNING'"

# Authentication endpoint protection
SecRule REQUEST_URI "@beginsWith /api/v1/auth/" \
    "id:1000006,\
    phase:1,\
    pass,\
    nolog,\
    initcol:ip=%{REMOTE_ADDR},\
    setvar:ip.auth_attempts=+1,\
    expirevar:ip.auth_attempts=300"

SecRule REQUEST_URI "@beginsWith /api/v1/auth/" \
    "chain,\
    id:1000007,\
    phase:1,\
    block,\
    msg:'Too many authentication attempts',\
    logdata:'Auth attempts: %{ip.auth_attempts}',\
    tag:'brute-force',\
    severity:'WARNING'"
    SecRule IP:AUTH_ATTEMPTS "@gt 10"

# SQL Injection protection for JSON payloads
SecRule REQUEST_HEADERS:Content-Type "@beginsWith application/json" \
    "id:1000008,\
    phase:2,\
    pass,\
    nolog,\
    ctl:requestBodyProcessor=JSON"

SecRule REQUEST_BODY "@detectSQLi" \
    "id:1000009,\
    phase:2,\
    block,\
    msg:'SQL Injection Attack Detected in Request Body',\
    logdata:'Matched Data: %{MATCHED_VAR} found within %{MATCHED_VAR_NAME}',\
    tag:'attack-sqli',\
    severity:'CRITICAL'"

# XSS protection for JSON payloads
SecRule REQUEST_BODY "@detectXSS" \
    "id:1000010,\
    phase:2,\
    block,\
    msg:'XSS Attack Detected in Request Body',\
    logdata:'Matched Data: %{MATCHED_VAR} found within %{MATCHED_VAR_NAME}',\
    tag:'attack-xss',\
    severity:'CRITICAL'"

# Command injection protection
SecRule ARGS "@detectCmdLine" \
    "id:1000011,\
    phase:2,\
    block,\
    msg:'Command Injection Attack Detected',\
    logdata:'Matched Data: %{MATCHED_VAR} found within %{MATCHED_VAR_NAME}',\
    tag:'attack-injection-cmd',\
    severity:'CRITICAL'"

# Path traversal protection
SecRule ARGS "@detectPathTraversal" \
    "id:1000012,\
    phase:2,\
    block,\
    msg:'Path Traversal Attack Detected',\
    logdata:'Matched Data: %{MATCHED_VAR} found within %{MATCHED_VAR_NAME}',\
    tag:'attack-path-traversal',\
    severity:'HIGH'"

# File upload restrictions
SecRule FILES_TMPNAMES "@inspectFile /etc/nginx/modsecurity/file-inspector.lua" \
    "id:1000013,\
    phase:2,\
    block,\
    msg:'Malicious File Upload Detected',\
    tag:'attack-file-upload',\
    severity:'HIGH'"

# Block suspicious file extensions
SecRule FILES "@rx \.(php|asp|aspx|jsp|exe|bat|cmd|com|scr|vbs|js|jar|war)$" \
    "id:1000014,\
    phase:2,\
    block,\
    msg:'Suspicious File Extension Detected',\
    logdata:'File: %{MATCHED_VAR}',\
    tag:'attack-file-upload',\
    severity:'HIGH'"

# Geolocation-based blocking (example)
SecGeoLookupDB /etc/nginx/modsecurity/GeoLite2-Country.mmdb
SecRule GEO:COUNTRY_CODE "@pmFromFile /etc/nginx/modsecurity/blocked-countries.data" \
    "id:1000015,\
    phase:1,\
    block,\
    msg:'Request from blocked country',\
    logdata:'Country: %{GEO:COUNTRY_CODE}',\
    tag:'geo-blocking',\
    severity:'WARNING'"

# Session security
SecRule REQUEST_COOKIES:sessionid "!@rx ^[a-zA-Z0-9]{32,}$" \
    "id:1000016,\
    phase:1,\
    block,\
    msg:'Invalid Session ID Format',\
    tag:'session-security',\
    severity:'MEDIUM'"

# CSRF protection
SecRule REQUEST_METHOD "@rx ^(POST|PUT|DELETE|PATCH)$" \
    "chain,\
    id:1000017,\
    phase:2,\
    block,\
    msg:'Missing CSRF Token',\
    tag:'csrf-protection',\
    severity:'HIGH'"
    SecRule &REQUEST_HEADERS:X-CSRFToken "@eq 0"

# API key validation
SecRule REQUEST_URI "@beginsWith /api/" \
    "chain,\
    id:1000018,\
    phase:1,\
    block,\
    msg:'Missing or Invalid API Key',\
    tag:'api-security',\
    severity:'HIGH'"
    SecRule &REQUEST_HEADERS:Authorization "@eq 0"

# Content-Type validation for API endpoints
SecRule REQUEST_URI "@beginsWith /api/" \
    "chain,\
    id:1000019,\
    phase:1,\
    block,\
    msg:'Invalid Content-Type for API Request',\
    tag:'api-security',\
    severity:'MEDIUM'"
    SecRule REQUEST_METHOD "@rx ^(POST|PUT|PATCH)$"
    SecRule REQUEST_HEADERS:Content-Type "!@beginsWith application/json"

# Response data leakage prevention
SecRule RESPONSE_BODY "@rx (?i)(password|secret|key|token|credential)" \
    "id:1000020,\
    phase:4,\
    block,\
    msg:'Sensitive Data Leakage Detected',\
    tag:'data-leakage',\
    severity:'HIGH'"

# Database error message blocking
SecRule RESPONSE_BODY "@rx (?i)(mysql|postgresql|oracle|sql server|sqlite)" \
    "id:1000021,\
    phase:4,\
    block,\
    msg:'Database Error Message Detected',\
    tag:'information-disclosure',\
    severity:'MEDIUM'"

# Stack trace blocking
SecRule RESPONSE_BODY "@rx (?i)(traceback|stack trace|exception|error)" \
    "id:1000022,\
    phase:4,\
    block,\
    msg:'Stack Trace Information Disclosure',\
    tag:'information-disclosure',\
    severity:'MEDIUM'"

# Anomaly scoring
SecRule TX:ANOMALY_SCORE "@ge 5" \
    "id:1000023,\
    phase:5,\
    block,\
    msg:'Inbound Anomaly Score Exceeded (Total Score: %{TX.ANOMALY_SCORE})',\
    tag:'anomaly-evaluation'"

# Custom logging for security events
SecRule TX:ANOMALY_SCORE "@ge 1" \
    "id:1000024,\
    phase:5,\
    pass,\
    log,\
    msg:'Security Event Detected (Score: %{TX.ANOMALY_SCORE})',\
    tag:'security-monitoring'"

# IP reputation checking (placeholder - would integrate with threat intelligence)
SecRule REMOTE_ADDR "@ipMatchFromFile /etc/nginx/modsecurity/malicious-ips.data" \
    "id:1000025,\
    phase:1,\
    block,\
    msg:'Request from Known Malicious IP',\
    logdata:'IP: %{REMOTE_ADDR}',\
    tag:'ip-reputation',\
    severity:'CRITICAL'"

# Bot detection
SecRule REQUEST_HEADERS:User-Agent "@rx (?i)(bot|crawler|spider|scraper)" \
    "id:1000026,\
    phase:1,\
    pass,\
    log,\
    msg:'Bot/Crawler Detected',\
    tag:'bot-detection',\
    setvar:'tx.bot_score=+1'"

# Honeypot trap
SecRule REQUEST_URI "@rx /admin/config\.php" \
    "id:1000027,\
    phase:1,\
    block,\
    msg:'Honeypot Triggered',\
    logdata:'Honeypot URL accessed: %{REQUEST_URI}',\
    tag:'honeypot',\
    severity:'WARNING'"

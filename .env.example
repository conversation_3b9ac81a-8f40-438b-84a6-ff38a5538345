# ============================================================================
# TrustVault - Configuration Sécurisée
# ============================================================================

# Database Configuration
DB_PASSWORD=your_super_secure_db_password_here
POSTGRES_DB=trustvault
POSTGRES_USER=trustvault

# Redis Configuration
REDIS_PASSWORD=your_redis_password_here

# Django Configuration
DJANGO_SECRET_KEY=your_django_secret_key_here
DEBUG=False
ALLOWED_HOSTS=localhost,127.0.0.1,trustvault.local

# HashiCorp Vault
VAULT_ROOT_TOKEN=your_vault_root_token_here
VAULT_UNSEAL_KEY=your_vault_unseal_key_here

# Wazuh SIEM Configuration
WAZUH_PASSWORD=your_wazuh_password_here
WAZUH_API_PASSWORD=your_wazuh_api_password_here

# Monitoring
GRAFANA_USER=admin
GRAFANA_PASSWORD=your_grafana_password_here

# SSL/TLS Configuration
SSL_CERT_PATH=/etc/nginx/ssl/trustvault.crt
SSL_KEY_PATH=/etc/nginx/ssl/trustvault.key

# Security Settings
FAIL2BAN_BANTIME=3600
FAIL2BAN_MAXRETRY=3
MODSECURITY_PARANOIA_LEVEL=2

# JWT Configuration
JWT_SECRET_KEY=your_jwt_secret_key_here
JWT_EXPIRATION_HOURS=1
JWT_REFRESH_EXPIRATION_DAYS=7

# MFA Configuration
TOTP_ISSUER=TrustVault
SMS_API_KEY=your_sms_api_key_here

# Email Configuration (for alerts)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your_email_password_here
EMAIL_USE_TLS=True

# Backup Configuration
RESTIC_PASSWORD=your_restic_backup_password_here
BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM

# Threat Intelligence
MISP_URL=https://misp.trustvault.local
MISP_API_KEY=your_misp_api_key_here

# Compliance
GDPR_ENABLED=True
AUDIT_LOG_RETENTION_DAYS=2555  # 7 years
DATA_ENCRYPTION_ALGORITHM=AES-256-GCM

# Network Security
ALLOWED_IPS=127.0.0.1,10.0.0.0/8,**********/12,***********/16
BLOCKED_COUNTRIES=CN,RU,KP  # Example blocked countries
RATE_LIMIT_PER_MINUTE=60

# Development/Testing (set to False in production)
ENABLE_DEBUG_TOOLBAR=False
ENABLE_SILK_PROFILER=False
SKIP_SSL_VERIFICATION=False

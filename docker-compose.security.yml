# TrustVault - Advanced Security Infrastructure
# This docker-compose file deploys a comprehensive security infrastructure including:
# - VPN Server (OpenVPN)
# - LDAP Directory Service (OpenLDAP)
# - Web Application Firewall (ModSecurity)
# - SIEM System (Elastic Stack)
# - Network Security Monitoring
# - Intrusion Detection System

version: '3.8'

services:
  # OpenVPN Server for secure remote access
  openvpn:
    image: kylemanna/openvpn:latest
    container_name: trustvault-vpn
    cap_add:
      - NET_ADMIN
    ports:
      - "1194:1194/udp"
    volumes:
      - openvpn-data:/etc/openvpn
      - ./security/vpn/config:/etc/openvpn/config
    environment:
      - OVPN_DATA=/etc/openvpn
    networks:
      - security-network
    restart: unless-stopped
    labels:
      - "traefik.enable=false"

  # OpenLDAP Directory Service
  openldap:
    image: osixia/openldap:1.5.0
    container_name: trustvault-ldap
    environment:
      - LDAP_LOG_LEVEL=256
      - LDAP_ORGANISATION=TrustVault
      - LDAP_DOMAIN=trustvault.local
      - LDAP_BASE_DN=dc=trustvault,dc=local
      - LDAP_ADMIN_PASSWORD=SecureAdminPassword123!
      - LDAP_CONFIG_PASSWORD=SecureConfigPassword123!
      - LDAP_READONLY_USER=false
      - LDAP_RFC2307BIS_SCHEMA=false
      - LDAP_BACKEND=mdb
      - LDAP_TLS=true
      - LDAP_TLS_CRT_FILENAME=ldap.crt
      - LDAP_TLS_KEY_FILENAME=ldap.key
      - LDAP_TLS_DH_PARAM_FILENAME=dhparam.pem
      - LDAP_TLS_CA_CRT_FILENAME=ca.crt
      - LDAP_TLS_ENFORCE=false
      - LDAP_TLS_CIPHER_SUITE=SECURE256:-VERS-SSL3.0
      - LDAP_TLS_VERIFY_CLIENT=demand
      - LDAP_REPLICATION=false
    tty: true
    stdin_open: true
    volumes:
      - ldap-data:/var/lib/ldap
      - ldap-config:/etc/ldap/slapd.d
      - ./security/ldap/certs:/container/service/slapd/assets/certs/
      - ./security/ldap/schema:/container/service/slapd/assets/config/bootstrap/schema/custom
    ports:
      - "389:389"
      - "636:636"
    networks:
      - security-network
    restart: unless-stopped

  # LDAP Admin Interface
  phpldapadmin:
    image: osixia/phpldapadmin:latest
    container_name: trustvault-ldap-admin
    environment:
      - PHPLDAPADMIN_LDAP_HOSTS=openldap
      - PHPLDAPADMIN_HTTPS=false
    ports:
      - "8080:80"
    depends_on:
      - openldap
    networks:
      - security-network
    restart: unless-stopped

  # Web Application Firewall (ModSecurity with Nginx)
  waf:
    image: owasp/modsecurity-nginx:latest
    container_name: trustvault-waf
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./security/waf/nginx.conf:/etc/nginx/nginx.conf
      - ./security/waf/modsecurity.conf:/etc/nginx/modsecurity/modsecurity.conf
      - ./security/waf/crs:/etc/nginx/modsecurity/crs
      - ./security/ssl:/etc/nginx/ssl
    environment:
      - BACKEND_HOST=backend
      - BACKEND_PORT=8000
      - FRONTEND_HOST=frontend
      - FRONTEND_PORT=3000
    depends_on:
      - backend
      - frontend
    networks:
      - security-network
      - app-network
    restart: unless-stopped

  # Elasticsearch for SIEM
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: trustvault-elasticsearch
    environment:
      - node.name=elasticsearch
      - cluster.name=trustvault-siem
      - discovery.type=single-node
      - bootstrap.memory_lock=true
      - "ES_JAVA_OPTS=-Xms2g -Xmx2g"
      - xpack.security.enabled=true
      - xpack.security.authc.api_key.enabled=true
      - ELASTIC_PASSWORD=SecureElasticPassword123!
    ulimits:
      memlock:
        soft: -1
        hard: -1
    volumes:
      - elasticsearch-data:/usr/share/elasticsearch/data
      - ./security/elasticsearch/config:/usr/share/elasticsearch/config
    ports:
      - "9200:9200"
    networks:
      - security-network
    restart: unless-stopped

  # Kibana for SIEM Dashboard
  kibana:
    image: docker.elastic.co/kibana/kibana:8.11.0
    container_name: trustvault-kibana
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
      - ELASTICSEARCH_USERNAME=elastic
      - ELASTICSEARCH_PASSWORD=SecureElasticPassword123!
      - SERVER_NAME=trustvault-kibana
      - SERVER_HOST=0.0.0.0
    ports:
      - "5601:5601"
    depends_on:
      - elasticsearch
    networks:
      - security-network
    restart: unless-stopped

  # Logstash for log processing
  logstash:
    image: docker.elastic.co/logstash/logstash:8.11.0
    container_name: trustvault-logstash
    environment:
      - "LS_JAVA_OPTS=-Xmx1g -Xms1g"
    volumes:
      - ./security/logstash/config:/usr/share/logstash/config
      - ./security/logstash/pipeline:/usr/share/logstash/pipeline
      - /var/log:/var/log:ro
    ports:
      - "5044:5044"
      - "9600:9600"
    depends_on:
      - elasticsearch
    networks:
      - security-network
    restart: unless-stopped

  # Filebeat for log shipping
  filebeat:
    image: docker.elastic.co/beats/filebeat:8.11.0
    container_name: trustvault-filebeat
    user: root
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
      - ELASTICSEARCH_USERNAME=elastic
      - ELASTICSEARCH_PASSWORD=SecureElasticPassword123!
    volumes:
      - ./security/filebeat/filebeat.yml:/usr/share/filebeat/filebeat.yml:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - /var/log:/var/log:ro
    depends_on:
      - elasticsearch
      - logstash
    networks:
      - security-network
    restart: unless-stopped

  # Suricata IDS/IPS
  suricata:
    image: jasonish/suricata:latest
    container_name: trustvault-suricata
    cap_add:
      - NET_ADMIN
      - SYS_NICE
    network_mode: host
    volumes:
      - ./security/suricata/suricata.yaml:/etc/suricata/suricata.yaml
      - ./security/suricata/rules:/var/lib/suricata/rules
      - suricata-logs:/var/log/suricata
    environment:
      - SURICATA_OPTIONS=-i any
    restart: unless-stopped

  # Security Monitoring Dashboard
  security-dashboard:
    build:
      context: ./security/dashboard
      dockerfile: Dockerfile
    container_name: trustvault-security-dashboard
    environment:
      - ELASTICSEARCH_URL=http://elasticsearch:9200
      - ELASTICSEARCH_USERNAME=elastic
      - ELASTICSEARCH_PASSWORD=SecureElasticPassword123!
    ports:
      - "8090:8080"
    depends_on:
      - elasticsearch
      - kibana
    networks:
      - security-network
    restart: unless-stopped

  # Threat Intelligence Feed Processor
  threat-intel:
    build:
      context: ./security/threat-intel
      dockerfile: Dockerfile
    container_name: trustvault-threat-intel
    environment:
      - DATABASE_URL=************************************************/trustvault
      - ELASTICSEARCH_URL=http://elasticsearch:9200
      - ELASTICSEARCH_USERNAME=elastic
      - ELASTICSEARCH_PASSWORD=SecureElasticPassword123!
    depends_on:
      - postgres
      - elasticsearch
    networks:
      - security-network
      - app-network
    restart: unless-stopped

  # Network Security Scanner
  nmap-scanner:
    image: instrumentisto/nmap:latest
    container_name: trustvault-scanner
    volumes:
      - ./security/scanner/scripts:/scripts
      - scanner-results:/results
    networks:
      - security-network
    restart: "no"
    profiles:
      - scanning

  # Vulnerability Scanner
  openvas:
    image: mikesplain/openvas:latest
    container_name: trustvault-openvas
    ports:
      - "9390:9390"
      - "9391:9391"
    volumes:
      - openvas-data:/var/lib/openvas
    environment:
      - OV_PASSWORD=SecureOpenVASPassword123!
    networks:
      - security-network
    restart: unless-stopped
    profiles:
      - vulnerability-scanning

  # Security Metrics Collector
  security-metrics:
    build:
      context: ./security/metrics
      dockerfile: Dockerfile
    container_name: trustvault-security-metrics
    environment:
      - DATABASE_URL=************************************************/trustvault
      - REDIS_URL=redis://redis:6379/0
      - ELASTICSEARCH_URL=http://elasticsearch:9200
    depends_on:
      - postgres
      - redis
      - elasticsearch
    networks:
      - security-network
      - app-network
    restart: unless-stopped

networks:
  security-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
  app-network:
    external: true

volumes:
  openvpn-data:
    driver: local
  ldap-data:
    driver: local
  ldap-config:
    driver: local
  elasticsearch-data:
    driver: local
  suricata-logs:
    driver: local
  scanner-results:
    driver: local
  openvas-data:
    driver: local

"""
Django management command to populate sample assets for TrustVault.
"""

from django.core.management.base import BaseCommand
from decimal import Decimal
from apps.portfolio.models import Asset


class Command(BaseCommand):
    help = 'Populate sample assets for TrustVault'

    def add_arguments(self, parser):
        parser.add_argument(
            '--clear',
            action='store_true',
            help='Clear existing assets before adding new ones',
        )

    def handle(self, *args, **options):
        if options['clear']:
            self.stdout.write('Clearing existing assets...')
            Asset.objects.all().delete()
            self.stdout.write(self.style.SUCCESS('Existing assets cleared.'))

        # Sample assets data
        sample_assets = [
            # Stocks
            {
                'symbol': 'AAPL',
                'name': 'Apple Inc.',
                'asset_type': 'STOCK',
                'sector': 'Technology',
                'current_price': Decimal('175.50'),
                'currency': 'USD',
                'description': 'Apple Inc. designs, manufactures, and markets smartphones, personal computers, tablets, wearables, and accessories worldwide.'
            },
            {
                'symbol': 'GOOGL',
                'name': 'Alphabet Inc.',
                'asset_type': 'STOCK',
                'sector': 'Technology',
                'current_price': Decimal('142.80'),
                'currency': 'USD',
                'description': 'Alphabet Inc. provides various products and platforms in the United States, Europe, the Middle East, Africa, the Asia-Pacific, Canada, and Latin America.'
            },
            {
                'symbol': 'MSFT',
                'name': 'Microsoft Corporation',
                'asset_type': 'STOCK',
                'sector': 'Technology',
                'current_price': Decimal('420.15'),
                'currency': 'USD',
                'description': 'Microsoft Corporation develops, licenses, and supports software, services, devices, and solutions worldwide.'
            },
            {
                'symbol': 'AMZN',
                'name': 'Amazon.com Inc.',
                'asset_type': 'STOCK',
                'sector': 'Consumer Discretionary',
                'current_price': Decimal('185.25'),
                'currency': 'USD',
                'description': 'Amazon.com, Inc. engages in the retail sale of consumer products and subscriptions in North America and internationally.'
            },
            {
                'symbol': 'TSLA',
                'name': 'Tesla Inc.',
                'asset_type': 'STOCK',
                'sector': 'Consumer Discretionary',
                'current_price': Decimal('248.50'),
                'currency': 'USD',
                'description': 'Tesla, Inc. designs, develops, manufactures, leases, and sells electric vehicles, and energy generation and storage systems.'
            },
            {
                'symbol': 'NVDA',
                'name': 'NVIDIA Corporation',
                'asset_type': 'STOCK',
                'sector': 'Technology',
                'current_price': Decimal('875.30'),
                'currency': 'USD',
                'description': 'NVIDIA Corporation provides graphics, and compute and networking solutions in the United States, Taiwan, China, and internationally.'
            },
            {
                'symbol': 'JPM',
                'name': 'JPMorgan Chase & Co.',
                'asset_type': 'STOCK',
                'sector': 'Financial Services',
                'current_price': Decimal('195.75'),
                'currency': 'USD',
                'description': 'JPMorgan Chase & Co. operates as a financial services company worldwide.'
            },
            {
                'symbol': 'JNJ',
                'name': 'Johnson & Johnson',
                'asset_type': 'STOCK',
                'sector': 'Healthcare',
                'current_price': Decimal('162.40'),
                'currency': 'USD',
                'description': 'Johnson & Johnson researches, develops, manufactures, and sells various products in the healthcare field worldwide.'
            },
            {
                'symbol': 'V',
                'name': 'Visa Inc.',
                'asset_type': 'STOCK',
                'sector': 'Financial Services',
                'current_price': Decimal('285.90'),
                'currency': 'USD',
                'description': 'Visa Inc. operates as a payments technology company worldwide.'
            },
            {
                'symbol': 'PG',
                'name': 'Procter & Gamble Co.',
                'asset_type': 'STOCK',
                'sector': 'Consumer Staples',
                'current_price': Decimal('165.20'),
                'currency': 'USD',
                'description': 'The Procter & Gamble Company provides branded consumer packaged goods to consumers in North and Latin America, Europe, the Asia Pacific, Greater China, India, the Middle East, and Africa.'
            },
            
            # ETFs
            {
                'symbol': 'SPY',
                'name': 'SPDR S&P 500 ETF Trust',
                'asset_type': 'ETF',
                'sector': 'Diversified',
                'current_price': Decimal('545.80'),
                'currency': 'USD',
                'description': 'The SPDR S&P 500 ETF Trust seeks to provide investment results that correspond generally to the price and yield performance of the S&P 500 Index.'
            },
            {
                'symbol': 'QQQ',
                'name': 'Invesco QQQ Trust',
                'asset_type': 'ETF',
                'sector': 'Technology',
                'current_price': Decimal('485.25'),
                'currency': 'USD',
                'description': 'The Invesco QQQ Trust tracks the Nasdaq-100 Index, which includes 100 of the largest domestic and international non-financial companies listed on the Nasdaq Stock Market.'
            },
            {
                'symbol': 'VTI',
                'name': 'Vanguard Total Stock Market ETF',
                'asset_type': 'ETF',
                'sector': 'Diversified',
                'current_price': Decimal('275.40'),
                'currency': 'USD',
                'description': 'The Vanguard Total Stock Market ETF seeks to track the performance of the CRSP US Total Market Index.'
            },
            
            # Bonds
            {
                'symbol': 'TLT',
                'name': 'iShares 20+ Year Treasury Bond ETF',
                'asset_type': 'BOND',
                'sector': 'Government Bonds',
                'current_price': Decimal('92.15'),
                'currency': 'USD',
                'description': 'The iShares 20+ Year Treasury Bond ETF seeks to track the investment results of an index composed of U.S. Treasury bonds with remaining maturities greater than twenty years.'
            },
            {
                'symbol': 'AGG',
                'name': 'iShares Core U.S. Aggregate Bond ETF',
                'asset_type': 'BOND',
                'sector': 'Diversified Bonds',
                'current_price': Decimal('102.85'),
                'currency': 'USD',
                'description': 'The iShares Core U.S. Aggregate Bond ETF seeks to track the investment results of an index composed of the total U.S. investment-grade bond market.'
            },
            
            # Cryptocurrencies
            {
                'symbol': 'BTC',
                'name': 'Bitcoin',
                'asset_type': 'CRYPTO',
                'sector': 'Cryptocurrency',
                'current_price': Decimal('67500.00'),
                'currency': 'USD',
                'description': 'Bitcoin is a decentralized digital currency that can be transferred on the peer-to-peer bitcoin network.'
            },
            {
                'symbol': 'ETH',
                'name': 'Ethereum',
                'asset_type': 'CRYPTO',
                'sector': 'Cryptocurrency',
                'current_price': Decimal('3450.00'),
                'currency': 'USD',
                'description': 'Ethereum is a decentralized, open-source blockchain with smart contract functionality.'
            },
            
            # Commodities
            {
                'symbol': 'GLD',
                'name': 'SPDR Gold Shares',
                'asset_type': 'COMMODITY',
                'sector': 'Precious Metals',
                'current_price': Decimal('195.30'),
                'currency': 'USD',
                'description': 'The SPDR Gold Shares seeks to reflect the performance of the price of gold bullion, less the expenses of the Trust\'s operations.'
            },
            {
                'symbol': 'SLV',
                'name': 'iShares Silver Trust',
                'asset_type': 'COMMODITY',
                'sector': 'Precious Metals',
                'current_price': Decimal('22.85'),
                'currency': 'USD',
                'description': 'The iShares Silver Trust seeks to reflect generally the performance of the price of silver.'
            },
        ]

        created_count = 0
        updated_count = 0

        for asset_data in sample_assets:
            asset, created = Asset.objects.get_or_create(
                symbol=asset_data['symbol'],
                defaults=asset_data
            )
            
            if created:
                created_count += 1
                self.stdout.write(f'Created asset: {asset.symbol} - {asset.name}')
            else:
                # Update existing asset with new data
                for key, value in asset_data.items():
                    if key != 'symbol':  # Don't update the symbol
                        setattr(asset, key, value)
                asset.save()
                updated_count += 1
                self.stdout.write(f'Updated asset: {asset.symbol} - {asset.name}')

        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully processed {len(sample_assets)} assets: '
                f'{created_count} created, {updated_count} updated.'
            )
        )

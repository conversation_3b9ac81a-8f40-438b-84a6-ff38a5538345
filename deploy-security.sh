#!/bin/bash

# TrustVault - Advanced Security Infrastructure Deployment Script
# This script deploys a comprehensive security infrastructure including:
# - VPN Server (OpenVPN)
# - LDAP Directory Service
# - Web Application Firewall (ModSecurity)
# - SIEM System (Elastic Stack)
# - Intrusion Detection System (Suricata)
# - Security Monitoring and Alerting

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DOMAIN="trustvault.local"
ADMIN_EMAIL="<EMAIL>"
SECURITY_DIR="./security"
BACKUP_DIR="./backups/security"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_requirements() {
    log_info "Checking system requirements..."
    
    # Check if Docker is installed
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    # Check if Docker Compose is installed
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    # Check available disk space (minimum 20GB)
    available_space=$(df / | awk 'NR==2 {print $4}')
    if [ "$available_space" -lt 20971520 ]; then
        log_warning "Less than 20GB disk space available. Security infrastructure may require more space."
    fi
    
    # Check available memory (minimum 8GB)
    available_memory=$(free -m | awk 'NR==2{printf "%.0f", $7}')
    if [ "$available_memory" -lt 8192 ]; then
        log_warning "Less than 8GB RAM available. Performance may be impacted."
    fi
    
    log_success "System requirements check completed"
}

create_directories() {
    log_info "Creating security directories..."
    
    mkdir -p "$SECURITY_DIR"/{waf,vpn,ldap,elasticsearch,kibana,logstash,filebeat,suricata,ssl,threat-intel,scanner,metrics,dashboard}
    mkdir -p "$SECURITY_DIR"/waf/{config,logs}
    mkdir -p "$SECURITY_DIR"/vpn/{config,certs}
    mkdir -p "$SECURITY_DIR"/ldap/{data,config,certs,schema}
    mkdir -p "$SECURITY_DIR"/elasticsearch/{config,data}
    mkdir -p "$SECURITY_DIR"/logstash/{config,pipeline,templates}
    mkdir -p "$SECURITY_DIR"/suricata/{config,rules,logs}
    mkdir -p "$SECURITY_DIR"/ssl/{certs,private}
    mkdir -p "$BACKUP_DIR"
    
    log_success "Security directories created"
}

generate_ssl_certificates() {
    log_info "Generating SSL certificates..."
    
    # Create CA private key
    openssl genrsa -out "$SECURITY_DIR/ssl/private/ca-key.pem" 4096
    
    # Create CA certificate
    openssl req -new -x509 -days 3650 -key "$SECURITY_DIR/ssl/private/ca-key.pem" \
        -out "$SECURITY_DIR/ssl/certs/ca.pem" \
        -subj "/C=US/ST=CA/L=San Francisco/O=TrustVault/OU=Security/CN=TrustVault CA"
    
    # Create server private key
    openssl genrsa -out "$SECURITY_DIR/ssl/private/trustvault.key" 4096
    
    # Create server certificate signing request
    openssl req -new -key "$SECURITY_DIR/ssl/private/trustvault.key" \
        -out "$SECURITY_DIR/ssl/trustvault.csr" \
        -subj "/C=US/ST=CA/L=San Francisco/O=TrustVault/OU=Security/CN=$DOMAIN"
    
    # Create server certificate
    openssl x509 -req -days 365 -in "$SECURITY_DIR/ssl/trustvault.csr" \
        -CA "$SECURITY_DIR/ssl/certs/ca.pem" \
        -CAkey "$SECURITY_DIR/ssl/private/ca-key.pem" \
        -CAcreateserial \
        -out "$SECURITY_DIR/ssl/certs/trustvault.crt" \
        -extensions v3_req \
        -extfile <(cat <<EOF
[v3_req]
basicConstraints = CA:FALSE
keyUsage = nonRepudiation, digitalSignature, keyEncipherment
subjectAltName = @alt_names

[alt_names]
DNS.1 = $DOMAIN
DNS.2 = localhost
IP.1 = 127.0.0.1
EOF
)
    
    # Set proper permissions
    chmod 600 "$SECURITY_DIR/ssl/private/"*
    chmod 644 "$SECURITY_DIR/ssl/certs/"*
    
    log_success "SSL certificates generated"
}

setup_vpn() {
    log_info "Setting up VPN server..."
    
    # Initialize OpenVPN configuration
    docker run -v "$PWD/$SECURITY_DIR/vpn/config:/etc/openvpn" --rm kylemanna/openvpn ovpn_genconfig -u udp://$DOMAIN
    
    # Generate CA and server certificates
    docker run -v "$PWD/$SECURITY_DIR/vpn/config:/etc/openvpn" --rm -it kylemanna/openvpn ovpn_initpki nopass
    
    log_success "VPN server configured"
}

setup_ldap() {
    log_info "Setting up LDAP directory service..."
    
    # Copy SSL certificates for LDAP
    cp "$SECURITY_DIR/ssl/certs/ca.pem" "$SECURITY_DIR/ldap/certs/ca.crt"
    cp "$SECURITY_DIR/ssl/certs/trustvault.crt" "$SECURITY_DIR/ldap/certs/ldap.crt"
    cp "$SECURITY_DIR/ssl/private/trustvault.key" "$SECURITY_DIR/ldap/certs/ldap.key"
    
    # Generate DH parameters
    openssl dhparam -out "$SECURITY_DIR/ldap/certs/dhparam.pem" 2048
    
    # Create LDAP schema files
    cat > "$SECURITY_DIR/ldap/schema/trustvault.ldif" <<EOF
dn: cn=trustvault,cn=schema,cn=config
objectClass: olcSchemaConfig
cn: trustvault
olcAttributeTypes: ( 1.3.6.1.4.1.12345.1.1.1 NAME 'trustVaultRole'
  DESC 'TrustVault user role'
  EQUALITY caseIgnoreMatch
  SUBSTR caseIgnoreSubstringsMatch
  SYNTAX 1.3.6.1.4.1.1466.115.121.1.15 )
olcObjectClasses: ( 1.3.6.1.4.1.12345.1.2.1 NAME 'trustVaultUser'
  DESC 'TrustVault user account'
  SUP inetOrgPerson
  STRUCTURAL
  MAY ( trustVaultRole ) )
EOF
    
    log_success "LDAP directory service configured"
}

setup_waf() {
    log_info "Setting up Web Application Firewall..."
    
    # Download OWASP Core Rule Set
    if [ ! -d "$SECURITY_DIR/waf/crs" ]; then
        git clone https://github.com/coreruleset/coreruleset.git "$SECURITY_DIR/waf/crs"
        cd "$SECURITY_DIR/waf/crs"
        git checkout v3.3/master
        cd - > /dev/null
    fi
    
    # Create attack tools detection file
    cat > "$SECURITY_DIR/waf/attack-tools.data" <<EOF
sqlmap
nikto
nmap
masscan
burp
w3af
havij
pangolin
acunetix
netsparker
appscan
websecurify
EOF
    
    # Create blocked countries file (example)
    cat > "$SECURITY_DIR/waf/blocked-countries.data" <<EOF
CN
RU
KP
IR
EOF
    
    # Create malicious IPs file (placeholder)
    touch "$SECURITY_DIR/waf/malicious-ips.data"
    
    log_success "Web Application Firewall configured"
}

setup_siem() {
    log_info "Setting up SIEM system..."
    
    # Create Elasticsearch configuration
    cat > "$SECURITY_DIR/elasticsearch/config/elasticsearch.yml" <<EOF
cluster.name: trustvault-siem
node.name: trustvault-es-node
network.host: 0.0.0.0
http.port: 9200
discovery.type: single-node
xpack.security.enabled: true
xpack.security.authc.api_key.enabled: true
xpack.monitoring.collection.enabled: true
EOF
    
    # Create Kibana configuration
    cat > "$SECURITY_DIR/kibana/config/kibana.yml" <<EOF
server.name: trustvault-kibana
server.host: 0.0.0.0
elasticsearch.hosts: ["http://elasticsearch:9200"]
elasticsearch.username: elastic
elasticsearch.password: SecureElasticPassword123!
xpack.security.enabled: true
xpack.monitoring.ui.container.elasticsearch.enabled: true
EOF
    
    # Create Logstash configuration
    cat > "$SECURITY_DIR/logstash/config/logstash.yml" <<EOF
http.host: "0.0.0.0"
xpack.monitoring.elasticsearch.hosts: ["http://elasticsearch:9200"]
xpack.monitoring.elasticsearch.username: elastic
xpack.monitoring.elasticsearch.password: SecureElasticPassword123!
EOF
    
    # Create Filebeat configuration
    cat > "$SECURITY_DIR/filebeat/filebeat.yml" <<EOF
filebeat.inputs:
- type: log
  enabled: true
  paths:
    - /var/log/nginx/*.log
  fields:
    logtype: nginx
  fields_under_root: true

- type: log
  enabled: true
  paths:
    - /var/log/trustvault/*.log
  fields:
    logtype: application
  fields_under_root: true

output.logstash:
  hosts: ["logstash:5044"]

processors:
- add_host_metadata:
    when.not.contains.tags: forwarded
EOF
    
    # Create Elasticsearch index template
    cat > "$SECURITY_DIR/logstash/templates/trustvault-security-template.json" <<EOF
{
  "index_patterns": ["trustvault-security-*"],
  "settings": {
    "number_of_shards": 1,
    "number_of_replicas": 0,
    "index.refresh_interval": "5s"
  },
  "mappings": {
    "properties": {
      "@timestamp": { "type": "date" },
      "remote_addr": { "type": "ip" },
      "status": { "type": "integer" },
      "method": { "type": "keyword" },
      "url": { "type": "text" },
      "http_user_agent": { "type": "text" },
      "risk_score": { "type": "integer" },
      "threat_level": { "type": "keyword" },
      "geoip": {
        "properties": {
          "location": { "type": "geo_point" },
          "country_name": { "type": "keyword" },
          "city_name": { "type": "keyword" }
        }
      }
    }
  }
}
EOF
    
    log_success "SIEM system configured"
}

setup_ids() {
    log_info "Setting up Intrusion Detection System..."
    
    # Create Suricata configuration
    cat > "$SECURITY_DIR/suricata/config/suricata.yaml" <<EOF
vars:
  address-groups:
    HOME_NET: "[***********/16,10.0.0.0/8,**********/12]"
    EXTERNAL_NET: "!$HOME_NET"
  port-groups:
    HTTP_PORTS: "80"
    SHELLCODE_PORTS: "!80"
    ORACLE_PORTS: 1521
    SSH_PORTS: 22
    DNP3_PORTS: 20000
    MODBUS_PORTS: 502
    FILE_DATA_PORTS: "[$HTTP_PORTS,110,143]"
    FTP_PORTS: 21

default-log-dir: /var/log/suricata/

stats:
  enabled: yes
  interval: 8

outputs:
  - eve-log:
      enabled: yes
      filetype: regular
      filename: eve.json
      types:
        - alert
        - http
        - dns
        - tls
        - files
        - smtp

app-layer:
  protocols:
    http:
      enabled: yes
    tls:
      enabled: yes
    dns:
      enabled: yes

rule-files:
  - suricata.rules
  - /var/lib/suricata/rules/emerging-threats.rules

classification-file: /etc/suricata/classification.config
reference-config-file: /etc/suricata/reference.config
EOF
    
    # Download Emerging Threats rules
    mkdir -p "$SECURITY_DIR/suricata/rules"
    wget -O "$SECURITY_DIR/suricata/rules/emerging-threats.rules" \
        "https://rules.emergingthreats.net/open/suricata/emerging.rules.tar.gz" || true
    
    log_success "Intrusion Detection System configured"
}

deploy_security_infrastructure() {
    log_info "Deploying security infrastructure..."
    
    # Start security services
    docker-compose -f docker-compose.security.yml up -d
    
    # Wait for services to start
    log_info "Waiting for services to start..."
    sleep 30
    
    # Check service health
    check_service_health
    
    log_success "Security infrastructure deployed"
}

check_service_health() {
    log_info "Checking service health..."
    
    services=("elasticsearch" "kibana" "logstash" "openldap" "waf")
    
    for service in "${services[@]}"; do
        if docker-compose -f docker-compose.security.yml ps | grep -q "$service.*Up"; then
            log_success "$service is running"
        else
            log_error "$service is not running"
        fi
    done
}

setup_monitoring() {
    log_info "Setting up security monitoring..."
    
    # Create monitoring script
    cat > "$SECURITY_DIR/monitor.sh" <<'EOF'
#!/bin/bash

# TrustVault Security Monitoring Script

ELASTICSEARCH_URL="http://localhost:9200"
ALERT_EMAIL="<EMAIL>"

# Check for high-risk events in the last hour
high_risk_events=$(curl -s "$ELASTICSEARCH_URL/trustvault-security-*/_search" \
    -H "Content-Type: application/json" \
    -d '{
        "query": {
            "bool": {
                "must": [
                    {"range": {"@timestamp": {"gte": "now-1h"}}},
                    {"term": {"threat_level": "HIGH"}}
                ]
            }
        },
        "size": 0
    }' | jq '.hits.total.value')

if [ "$high_risk_events" -gt 10 ]; then
    echo "ALERT: $high_risk_events high-risk security events detected in the last hour"
    # Send alert email (configure mail server)
    # echo "High-risk security events detected" | mail -s "TrustVault Security Alert" $ALERT_EMAIL
fi

# Check service health
services=("elasticsearch" "kibana" "logstash" "openldap" "waf")
for service in "${services[@]}"; do
    if ! docker ps | grep -q "$service"; then
        echo "ALERT: $service is not running"
    fi
done
EOF
    
    chmod +x "$SECURITY_DIR/monitor.sh"
    
    # Add to crontab (run every 5 minutes)
    (crontab -l 2>/dev/null; echo "*/5 * * * * $PWD/$SECURITY_DIR/monitor.sh") | crontab -
    
    log_success "Security monitoring configured"
}

create_security_documentation() {
    log_info "Creating security documentation..."
    
    cat > "$SECURITY_DIR/README.md" <<EOF
# TrustVault Security Infrastructure

## Services Overview

- **Web Application Firewall (WAF)**: ModSecurity with OWASP Core Rule Set
- **VPN Server**: OpenVPN for secure remote access
- **LDAP Directory**: OpenLDAP for centralized authentication
- **SIEM System**: Elastic Stack (Elasticsearch, Logstash, Kibana)
- **IDS/IPS**: Suricata for network intrusion detection
- **Security Monitoring**: Custom monitoring and alerting

## Access Information

- **Kibana Dashboard**: https://$DOMAIN:5601
- **LDAP Admin**: http://$DOMAIN:8080
- **Security Dashboard**: http://$DOMAIN:8090

## Default Credentials

- **Elasticsearch**: elastic / SecureElasticPassword123!
- **LDAP Admin**: cn=admin,dc=trustvault,dc=local / SecureAdminPassword123!

## Maintenance

- Monitor logs: \`docker-compose -f docker-compose.security.yml logs -f\`
- Update rules: Run \`$SECURITY_DIR/update-rules.sh\`
- Backup: Run \`$SECURITY_DIR/backup.sh\`

## Security Contacts

- Security Team: <EMAIL>
- Incident Response: <EMAIL>
EOF
    
    log_success "Security documentation created"
}

main() {
    echo "=================================================="
    echo "TrustVault - Advanced Security Infrastructure"
    echo "=================================================="
    echo
    
    check_requirements
    create_directories
    generate_ssl_certificates
    setup_waf
    setup_siem
    setup_ids
    setup_ldap
    setup_vpn
    deploy_security_infrastructure
    setup_monitoring
    create_security_documentation
    
    echo
    log_success "Security infrastructure deployment completed!"
    echo
    echo "Next steps:"
    echo "1. Access Kibana at https://$DOMAIN:5601"
    echo "2. Configure LDAP users at http://$DOMAIN:8080"
    echo "3. Review security monitoring at $SECURITY_DIR/monitor.sh"
    echo "4. Update firewall rules and threat intelligence feeds"
    echo "5. Test VPN connectivity"
    echo
    echo "For detailed information, see $SECURITY_DIR/README.md"
}

# Run main function
main "$@"

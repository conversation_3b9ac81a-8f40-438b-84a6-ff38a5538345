# TrustVault - Test Configuration

import pytest
from django.test import Client
from django.contrib.auth import get_user_model
from rest_framework.test import APIClient
from rest_framework_simplejwt.tokens import RefreshToken

User = get_user_model()


@pytest.fixture
def client():
    """Django test client."""
    return Client()


@pytest.fixture
def api_client():
    """DRF API client."""
    return APIClient()


@pytest.fixture
def user_data():
    """Sample user data for testing."""
    return {
        'email': '<EMAIL>',
        'username': 'testuser',
        'first_name': 'Test',
        'last_name': 'User',
        'password': 'TestPassword123!',
        'gdpr_consent': True,
    }


@pytest.fixture
def user(user_data):
    """Create a test user."""
    user = User.objects.create_user(
        email=user_data['email'],
        username=user_data['username'],
        first_name=user_data['first_name'],
        last_name=user_data['last_name'],
        password=user_data['password'],
        gdpr_consent=user_data['gdpr_consent'],
    )
    return user


@pytest.fixture
def admin_user():
    """Create an admin user."""
    return User.objects.create_superuser(
        email='<EMAIL>',
        username='admin',
        first_name='Admin',
        last_name='User',
        password='AdminPassword123!',
    )


@pytest.fixture
def authenticated_client(api_client, user):
    """API client with authenticated user."""
    refresh = RefreshToken.for_user(user)
    api_client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')
    return api_client


@pytest.fixture
def admin_client(api_client, admin_user):
    """API client with authenticated admin user."""
    refresh = RefreshToken.for_user(admin_user)
    api_client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')
    return api_client


@pytest.fixture
def portfolio_data():
    """Sample portfolio data for testing."""
    return {
        'name': 'Test Portfolio',
        'description': 'A test portfolio',
        'portfolio_type': 'MODERATE',
        'currency': 'USD',
        'is_public': False,
    }


@pytest.fixture
def asset_data():
    """Sample asset data for testing."""
    return {
        'symbol': 'AAPL',
        'name': 'Apple Inc.',
        'asset_type': 'STOCK',
        'current_price': '150.00',
        'currency': 'USD',
        'sector': 'Technology',
        'country': 'US',
    }

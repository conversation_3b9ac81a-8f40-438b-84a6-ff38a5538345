# TrustVault - Alerts URLs

from django.urls import path
from . import views

app_name = 'alerts'

urlpatterns = [
    # Price Alerts
    path('alerts/', views.PriceAlertListCreateView.as_view(), name='alert-list'),
    path('alerts/<uuid:pk>/', views.PriceAlertDetailView.as_view(), name='alert-detail'),
    path('alerts/choices/', views.AlertChoicesView.as_view(), name='alert-choices'),
    path('alerts/stats/', views.AlertStatsView.as_view(), name='alert-stats'),
    path('alerts/metrics/', views.AlertsMetricsView.as_view(), name='alerts-metrics'),
    
    # Alert History
    path('alerts/history/', views.AlertHistoryListView.as_view(), name='alert-history'),
    
    # Notification Preferences
    path('notifications/preferences/', views.NotificationPreferenceView.as_view(), name='notification-preferences'),
    path('notifications/', views.NotificationListView.as_view(), name='notification-list'),
    path('notifications/test/', views.CreateTestNotificationsView.as_view(), name='create-test-notifications'),
    path('alerts/test/', views.AlertCreationTestView.as_view(), name='alert-creation-test'),
    
    # Reports
    path('reports/', views.ReportListCreateView.as_view(), name='report-list'),
    path('reports/<uuid:pk>/', views.ReportDetailView.as_view(), name='report-detail'),
    path('reports/<uuid:pk>/download/', views.ReportDownloadView.as_view(), name='report-download'),
]

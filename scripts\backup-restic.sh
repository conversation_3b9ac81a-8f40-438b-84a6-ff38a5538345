#!/bin/bash

# TrustVault - Restic Backup Script with Encryption
# ============================================================================

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
BACKUP_CONFIG="$PROJECT_ROOT/.env"

# Load environment variables
if [[ -f "$BACKUP_CONFIG" ]]; then
    source "$BACKUP_CONFIG"
fi

# Backup configuration
RESTIC_REPOSITORY="${RESTIC_REPOSITORY:-/backup/trustvault}"
RESTIC_PASSWORD="${RESTIC_PASSWORD:-trustvault_backup_password}"
BACKUP_RETENTION_DAYS="${BACKUP_RETENTION_DAYS:-30}"
BACKUP_LOG_FILE="${BACKUP_LOG_FILE:-/var/log/trustvault/backup.log}"

# Database configuration
DB_HOST="${DB_HOST:-postgres}"
DB_PORT="${DB_PORT:-5432}"
DB_NAME="${DB_NAME:-trustvault}"
DB_USER="${DB_USER:-trustvault}"
DB_PASSWORD="${DB_PASSWORD:-}"

# Notification configuration
WEBHOOK_URL="${BACKUP_WEBHOOK_URL:-}"
SLACK_WEBHOOK="${SLACK_WEBHOOK_URL:-}"

# ============================================================================
# LOGGING FUNCTIONS
# ============================================================================

log() {
    local level="$1"
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] [$level] $message" | tee -a "$BACKUP_LOG_FILE"
}

log_info() {
    log "INFO" "$@"
}

log_warn() {
    log "WARN" "$@"
}

log_error() {
    log "ERROR" "$@"
}

log_success() {
    log "SUCCESS" "$@"
}

# ============================================================================
# NOTIFICATION FUNCTIONS
# ============================================================================

send_notification() {
    local status="$1"
    local message="$2"
    local color="good"
    
    if [[ "$status" == "ERROR" ]]; then
        color="danger"
    elif [[ "$status" == "WARN" ]]; then
        color="warning"
    fi
    
    # Send to webhook if configured
    if [[ -n "$WEBHOOK_URL" ]]; then
        curl -s -X POST "$WEBHOOK_URL" \
            -H "Content-Type: application/json" \
            -d "{\"status\":\"$status\",\"message\":\"$message\",\"timestamp\":\"$(date -Iseconds)\"}" \
            || log_warn "Failed to send webhook notification"
    fi
    
    # Send to Slack if configured
    if [[ -n "$SLACK_WEBHOOK" ]]; then
        curl -s -X POST "$SLACK_WEBHOOK" \
            -H "Content-Type: application/json" \
            -d "{\"text\":\"TrustVault Backup $status: $message\",\"color\":\"$color\"}" \
            || log_warn "Failed to send Slack notification"
    fi
}

# ============================================================================
# BACKUP FUNCTIONS
# ============================================================================

init_repository() {
    log_info "Initializing Restic repository..."
    
    if ! restic -r "$RESTIC_REPOSITORY" snapshots >/dev/null 2>&1; then
        log_info "Repository does not exist, creating..."
        restic -r "$RESTIC_REPOSITORY" init
        log_success "Repository initialized successfully"
    else
        log_info "Repository already exists"
    fi
}

backup_database() {
    log_info "Starting database backup..."
    
    local backup_file="/tmp/trustvault_db_$(date +%Y%m%d_%H%M%S).sql"
    local backup_file_encrypted="${backup_file}.gpg"
    
    # Create database dump
    PGPASSWORD="$DB_PASSWORD" pg_dump \
        -h "$DB_HOST" \
        -p "$DB_PORT" \
        -U "$DB_USER" \
        -d "$DB_NAME" \
        --verbose \
        --no-password \
        --format=custom \
        --compress=9 \
        --file="$backup_file" \
        || {
            log_error "Database backup failed"
            return 1
        }
    
    # Encrypt the backup
    gpg --symmetric --cipher-algo AES256 --compress-algo 2 --s2k-mode 3 \
        --s2k-digest-algo SHA512 --s2k-count 65011712 \
        --passphrase "$RESTIC_PASSWORD" \
        --batch --yes \
        --output "$backup_file_encrypted" \
        "$backup_file"
    
    # Remove unencrypted backup
    rm -f "$backup_file"
    
    # Backup encrypted file with Restic
    restic -r "$RESTIC_REPOSITORY" backup "$backup_file_encrypted" \
        --tag database \
        --tag "$(date +%Y-%m-%d)" \
        || {
            log_error "Restic database backup failed"
            rm -f "$backup_file_encrypted"
            return 1
        }
    
    # Clean up encrypted file
    rm -f "$backup_file_encrypted"
    
    log_success "Database backup completed successfully"
}

backup_application_data() {
    log_info "Starting application data backup..."
    
    local app_dirs=(
        "$PROJECT_ROOT/backend"
        "$PROJECT_ROOT/frontend"
        "$PROJECT_ROOT/nginx"
        "$PROJECT_ROOT/docker-compose.yml"
        "$PROJECT_ROOT/.env"
        "$PROJECT_ROOT/secrets"
    )
    
    # Create temporary directory for application backup
    local temp_dir="/tmp/trustvault_app_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$temp_dir"
    
    # Copy application files
    for dir in "${app_dirs[@]}"; do
        if [[ -e "$dir" ]]; then
            cp -r "$dir" "$temp_dir/"
        fi
    done
    
    # Backup with Restic
    restic -r "$RESTIC_REPOSITORY" backup "$temp_dir" \
        --tag application \
        --tag "$(date +%Y-%m-%d)" \
        --exclude="*.pyc" \
        --exclude="__pycache__" \
        --exclude="node_modules" \
        --exclude=".git" \
        || {
            log_error "Restic application backup failed"
            rm -rf "$temp_dir"
            return 1
        }
    
    # Clean up
    rm -rf "$temp_dir"
    
    log_success "Application data backup completed successfully"
}

backup_logs() {
    log_info "Starting logs backup..."
    
    local logs_dir="$PROJECT_ROOT/logs"
    
    if [[ -d "$logs_dir" ]]; then
        restic -r "$RESTIC_REPOSITORY" backup "$logs_dir" \
            --tag logs \
            --tag "$(date +%Y-%m-%d)" \
            || {
                log_error "Logs backup failed"
                return 1
            }
        
        log_success "Logs backup completed successfully"
    else
        log_warn "Logs directory not found, skipping logs backup"
    fi
}

backup_secrets() {
    log_info "Starting secrets backup..."
    
    local secrets_dir="$PROJECT_ROOT/secrets"
    
    if [[ -d "$secrets_dir" ]]; then
        # Create encrypted archive of secrets
        local secrets_archive="/tmp/trustvault_secrets_$(date +%Y%m%d_%H%M%S).tar.gz"
        local secrets_encrypted="${secrets_archive}.gpg"
        
        tar -czf "$secrets_archive" -C "$PROJECT_ROOT" secrets/
        
        # Encrypt the secrets archive
        gpg --symmetric --cipher-algo AES256 --compress-algo 2 --s2k-mode 3 \
            --s2k-digest-algo SHA512 --s2k-count 65011712 \
            --passphrase "$RESTIC_PASSWORD" \
            --batch --yes \
            --output "$secrets_encrypted" \
            "$secrets_archive"
        
        # Remove unencrypted archive
        rm -f "$secrets_archive"
        
        # Backup encrypted secrets
        restic -r "$RESTIC_REPOSITORY" backup "$secrets_encrypted" \
            --tag secrets \
            --tag "$(date +%Y-%m-%d)" \
            || {
                log_error "Secrets backup failed"
                rm -f "$secrets_encrypted"
                return 1
            }
        
        # Clean up
        rm -f "$secrets_encrypted"
        
        log_success "Secrets backup completed successfully"
    else
        log_warn "Secrets directory not found, skipping secrets backup"
    fi
}

cleanup_old_backups() {
    log_info "Cleaning up old backups..."
    
    # Remove snapshots older than retention period
    restic -r "$RESTIC_REPOSITORY" forget \
        --keep-daily "$BACKUP_RETENTION_DAYS" \
        --keep-weekly 4 \
        --keep-monthly 12 \
        --prune \
        || {
            log_error "Backup cleanup failed"
            return 1
        }
    
    log_success "Old backups cleaned up successfully"
}

verify_backup() {
    log_info "Verifying backup integrity..."
    
    # Check repository integrity
    restic -r "$RESTIC_REPOSITORY" check \
        || {
            log_error "Backup verification failed"
            return 1
        }
    
    log_success "Backup verification completed successfully"
}

# ============================================================================
# MAIN BACKUP FUNCTION
# ============================================================================

run_backup() {
    local start_time=$(date +%s)
    log_info "Starting TrustVault backup process..."
    
    # Export Restic password
    export RESTIC_PASSWORD
    
    # Initialize repository if needed
    init_repository || {
        send_notification "ERROR" "Failed to initialize backup repository"
        return 1
    }
    
    # Perform backups
    local backup_success=true
    
    backup_database || backup_success=false
    backup_application_data || backup_success=false
    backup_logs || backup_success=false
    backup_secrets || backup_success=false
    
    if [[ "$backup_success" == "true" ]]; then
        cleanup_old_backups || log_warn "Cleanup failed but backup succeeded"
        verify_backup || log_warn "Verification failed but backup succeeded"
        
        local end_time=$(date +%s)
        local duration=$((end_time - start_time))
        
        log_success "Backup process completed successfully in ${duration} seconds"
        send_notification "SUCCESS" "Backup completed successfully in ${duration} seconds"
    else
        log_error "Backup process failed"
        send_notification "ERROR" "Backup process failed - check logs for details"
        return 1
    fi
}

# ============================================================================
# RESTORE FUNCTIONS
# ============================================================================

list_snapshots() {
    log_info "Listing available snapshots..."
    restic -r "$RESTIC_REPOSITORY" snapshots
}

restore_database() {
    local snapshot_id="$1"
    local restore_path="${2:-/tmp/restore}"
    
    log_info "Restoring database from snapshot $snapshot_id..."
    
    mkdir -p "$restore_path"
    
    restic -r "$RESTIC_REPOSITORY" restore "$snapshot_id" \
        --target "$restore_path" \
        --include "*.sql.gpg" \
        || {
            log_error "Database restore failed"
            return 1
        }
    
    log_success "Database restored to $restore_path"
}

# ============================================================================
# MAIN SCRIPT
# ============================================================================

main() {
    case "${1:-backup}" in
        "backup")
            run_backup
            ;;
        "list")
            list_snapshots
            ;;
        "restore-db")
            if [[ -z "${2:-}" ]]; then
                log_error "Snapshot ID required for restore"
                exit 1
            fi
            restore_database "$2" "${3:-}"
            ;;
        "verify")
            export RESTIC_PASSWORD
            verify_backup
            ;;
        *)
            echo "Usage: $0 {backup|list|restore-db <snapshot-id> [restore-path]|verify}"
            exit 1
            ;;
    esac
}

# Create log directory if it doesn't exist
mkdir -p "$(dirname "$BACKUP_LOG_FILE")"

# Run main function
main "$@"

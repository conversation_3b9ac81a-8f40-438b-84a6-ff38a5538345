#!/usr/bin/env python3
"""
TrustVault - Comprehensive Security Validation Suite
This script performs a complete security assessment of the TrustVault infrastructure.
"""

import requests
import json
import time
import subprocess
import sys
import os
from datetime import datetime
from typing import Dict, List, Any

class SecurityValidator:
    def __init__(self):
        self.base_url = "http://localhost:8000"
        self.frontend_url = "http://localhost:3000"
        self.prometheus_url = "http://localhost:9090"
        self.grafana_url = "http://localhost:3001"
        self.results = []
        
    def log_result(self, test_name: str, status: str, details: str = ""):
        """Log test results"""
        result = {
            "timestamp": datetime.now().isoformat(),
            "test": test_name,
            "status": status,
            "details": details
        }
        self.results.append(result)
        status_icon = "✅" if status == "PASS" else "❌" if status == "FAIL" else "⚠️"
        print(f"{status_icon} {test_name}: {status}")
        if details:
            print(f"   Details: {details}")
    
    def test_ssl_certificates(self):
        """Test SSL certificate configuration"""
        print("\n🔐 Testing SSL Certificates...")
        
        ssl_dir = "security/ssl"
        required_files = ["trustvault.crt", "trustvault.key", "ca.crt", "ca.key"]
        
        for file in required_files:
            file_path = os.path.join(ssl_dir, file)
            if os.path.exists(file_path):
                self.log_result(f"SSL Certificate - {file}", "PASS", f"File exists: {file_path}")
            else:
                self.log_result(f"SSL Certificate - {file}", "FAIL", f"Missing file: {file_path}")
    
    def test_authentication_security(self):
        """Test authentication security features"""
        print("\n🔑 Testing Authentication Security...")
        
        # Test login endpoint
        try:
            login_data = {
                "email": "<EMAIL>",
                "password": "admin123"
            }
            response = requests.post(f"{self.base_url}/api/v1/auth/login/", json=login_data, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if "access_token" in data:
                    self.log_result("Authentication - Login", "PASS", "Login successful with JWT token")
                    return data["access_token"]
                else:
                    self.log_result("Authentication - Login", "FAIL", "No access token in response")
            else:
                self.log_result("Authentication - Login", "FAIL", f"HTTP {response.status_code}")
        except Exception as e:
            self.log_result("Authentication - Login", "FAIL", str(e))
        
        return None
    
    def test_api_security_headers(self):
        """Test security headers on API endpoints"""
        print("\n🛡️ Testing API Security Headers...")
        
        try:
            response = requests.get(f"{self.base_url}/health/", timeout=10)
            headers = response.headers
            
            security_headers = {
                "X-Content-Type-Options": "nosniff",
                "X-Frame-Options": ["DENY", "SAMEORIGIN"],
                "Content-Security-Policy": None,  # Should exist
                "X-XSS-Protection": "1; mode=block"
            }
            
            for header, expected in security_headers.items():
                if header in headers:
                    if expected is None:  # Just check existence
                        self.log_result(f"Security Header - {header}", "PASS", f"Present: {headers[header]}")
                    elif isinstance(expected, list):
                        if headers[header] in expected:
                            self.log_result(f"Security Header - {header}", "PASS", f"Value: {headers[header]}")
                        else:
                            self.log_result(f"Security Header - {header}", "WARN", f"Unexpected value: {headers[header]}")
                    elif headers[header] == expected:
                        self.log_result(f"Security Header - {header}", "PASS", f"Value: {headers[header]}")
                    else:
                        self.log_result(f"Security Header - {header}", "WARN", f"Unexpected value: {headers[header]}")
                else:
                    self.log_result(f"Security Header - {header}", "FAIL", "Header missing")
                    
        except Exception as e:
            self.log_result("API Security Headers", "FAIL", str(e))
    
    def test_rate_limiting(self):
        """Test rate limiting protection"""
        print("\n🚦 Testing Rate Limiting...")
        
        try:
            # Make multiple rapid requests to test rate limiting
            rapid_requests = 0
            for i in range(10):
                response = requests.get(f"{self.base_url}/health/", timeout=5)
                if response.status_code == 200:
                    rapid_requests += 1
                elif response.status_code == 429:  # Too Many Requests
                    self.log_result("Rate Limiting", "PASS", f"Rate limit triggered after {rapid_requests} requests")
                    return
                time.sleep(0.1)
            
            self.log_result("Rate Limiting", "WARN", "No rate limiting detected in 10 requests")
            
        except Exception as e:
            self.log_result("Rate Limiting", "FAIL", str(e))
    
    def test_monitoring_services(self):
        """Test monitoring and alerting services"""
        print("\n📊 Testing Monitoring Services...")
        
        # Test Prometheus
        try:
            response = requests.get(f"{self.prometheus_url}/api/v1/query?query=up", timeout=10)
            if response.status_code == 200:
                data = response.json()
                if data.get("status") == "success":
                    self.log_result("Monitoring - Prometheus", "PASS", "Prometheus API responding")
                else:
                    self.log_result("Monitoring - Prometheus", "FAIL", "Prometheus API error")
            else:
                self.log_result("Monitoring - Prometheus", "FAIL", f"HTTP {response.status_code}")
        except Exception as e:
            self.log_result("Monitoring - Prometheus", "FAIL", str(e))
        
        # Test Grafana
        try:
            response = requests.get(f"{self.grafana_url}/api/health", timeout=10)
            if response.status_code == 200:
                self.log_result("Monitoring - Grafana", "PASS", "Grafana health check passed")
            else:
                self.log_result("Monitoring - Grafana", "FAIL", f"HTTP {response.status_code}")
        except Exception as e:
            self.log_result("Monitoring - Grafana", "FAIL", str(e))
    
    def test_database_security(self):
        """Test database security configuration"""
        print("\n🗄️ Testing Database Security...")
        
        try:
            # Test if database is accessible only from allowed hosts
            response = requests.get(f"{self.base_url}/health/", timeout=10)
            if response.status_code == 200:
                data = response.json()
                if "database" in data.get("components", {}):
                    db_status = data["components"]["database"]["status"]
                    if db_status == "healthy":
                        self.log_result("Database Security", "PASS", "Database connection secure")
                    else:
                        self.log_result("Database Security", "FAIL", f"Database status: {db_status}")
                else:
                    self.log_result("Database Security", "WARN", "Database status not in health check")
            else:
                self.log_result("Database Security", "FAIL", f"Health check failed: HTTP {response.status_code}")
        except Exception as e:
            self.log_result("Database Security", "FAIL", str(e))
    
    def test_container_security(self):
        """Test Docker container security"""
        print("\n🐳 Testing Container Security...")
        
        try:
            # Check if containers are running as non-root
            result = subprocess.run(["docker", "ps", "--format", "table {{.Names}}\t{{.Status}}"], 
                                  capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                containers = result.stdout.strip().split('\n')[1:]  # Skip header
                running_containers = len([c for c in containers if "Up" in c])
                self.log_result("Container Security", "PASS", f"{running_containers} containers running")
            else:
                self.log_result("Container Security", "FAIL", "Could not check container status")
                
        except Exception as e:
            self.log_result("Container Security", "FAIL", str(e))
    
    def generate_report(self):
        """Generate comprehensive security report"""
        print("\n" + "="*60)
        print("🔒 TRUSTVAULT SECURITY VALIDATION REPORT")
        print("="*60)
        
        total_tests = len(self.results)
        passed_tests = len([r for r in self.results if r["status"] == "PASS"])
        failed_tests = len([r for r in self.results if r["status"] == "FAIL"])
        warning_tests = len([r for r in self.results if r["status"] == "WARN"])
        
        print(f"\n📊 SUMMARY:")
        print(f"   Total Tests: {total_tests}")
        print(f"   ✅ Passed: {passed_tests}")
        print(f"   ❌ Failed: {failed_tests}")
        print(f"   ⚠️  Warnings: {warning_tests}")
        
        security_score = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
        print(f"\n🎯 Security Score: {security_score:.1f}%")
        
        if security_score >= 90:
            print("🟢 EXCELLENT - Security posture is strong")
        elif security_score >= 75:
            print("🟡 GOOD - Minor security improvements needed")
        elif security_score >= 60:
            print("🟠 MODERATE - Several security issues need attention")
        else:
            print("🔴 CRITICAL - Immediate security improvements required")
        
        # Save detailed report
        report_file = f"security_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump(self.results, f, indent=2)
        
        print(f"\n📄 Detailed report saved to: {report_file}")
        
        return security_score
    
    def run_all_tests(self):
        """Run all security validation tests"""
        print("🚀 Starting TrustVault Security Validation...")
        print(f"⏰ Timestamp: {datetime.now().isoformat()}")
        
        self.test_ssl_certificates()
        self.test_authentication_security()
        self.test_api_security_headers()
        self.test_rate_limiting()
        self.test_monitoring_services()
        self.test_database_security()
        self.test_container_security()
        
        return self.generate_report()

if __name__ == "__main__":
    validator = SecurityValidator()
    score = validator.run_all_tests()
    
    # Exit with appropriate code
    sys.exit(0 if score >= 75 else 1)

# TrustVault - Alerts Views

from rest_framework import generics, permissions, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.views import APIView
from django.db.models import Q, Count
from django.utils import timezone
from django.http import HttpResponse, Http404
from django_filters.rest_framework import DjangoFilterBackend
from drf_spectacular.utils import extend_schema, OpenApiParameter
from drf_spectacular.types import OpenApiTypes

from .models import (
    PriceAlert, AlertHistory, NotificationPreference,
    Notification, Report, AlertType, AlertStatus, NotificationChannel
)
from .serializers import (
    PriceAlertSerializer, AlertHistorySerializer, NotificationPreferenceSerializer,
    NotificationSerializer, ReportSerializer, ReportCreateSerializer,
    AlertTypeChoicesSerializer, AlertStatusChoicesSerializer, NotificationChannelChoicesSerializer
)
from apps.core.models import AuditLog
from .metrics import (
    record_alert_created, record_report_generated, update_active_alerts_gauge,
    metrics_collector
)
import logging

logger = logging.getLogger(__name__)


class PriceAlertListCreateView(generics.ListCreateAPIView):
    """List and create price alerts."""

    serializer_class = PriceAlertSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['alert_type', 'status', 'asset', 'portfolio']
    search_fields = ['name', 'description']
    ordering_fields = ['created_at', 'threshold_value', 'triggered_at']
    ordering = ['-created_at']

    def get_queryset(self):
        """Get alerts for the current user."""
        return PriceAlert.objects.filter(user=self.request.user).select_related(
            'asset', 'portfolio'
        )

    @extend_schema(
        summary="List Price Alerts",
        description="Get paginated list of user's price alerts with filtering and search",
        responses={200: PriceAlertSerializer(many=True)},
        tags=["Alerts"]
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)

    @extend_schema(
        summary="Create Price Alert",
        description="Create a new price alert",
        request=PriceAlertSerializer,
        responses={201: PriceAlertSerializer},
        tags=["Alerts"]
    )
    def post(self, request, *args, **kwargs):
        # Detailed logging for debugging
        logger.info("=" * 50)
        logger.info("🚨 ALERT CREATION REQUEST")
        logger.info(f"📝 User: {request.user}")
        logger.info(f"📝 Raw data: {request.data}")
        logger.info(f"📝 Content-Type: {request.content_type}")
        logger.info(f"📝 Method: {request.method}")

        # Test serializer directly
        serializer = self.get_serializer(data=request.data)
        logger.info(f"📝 Serializer class: {serializer.__class__.__name__}")

        if serializer.is_valid():
            logger.info("✅ Serializer validation PASSED")
            logger.info(f"📝 Validated data: {serializer.validated_data}")
        else:
            logger.error("❌ Serializer validation FAILED")
            logger.error(f"Validation errors: {serializer.errors}")

            # Get non-field errors safely
            non_field_errors = []
            if hasattr(serializer, 'non_field_errors'):
                non_field_errors = serializer.non_field_errors()

            logger.error(f"Non-field errors: {non_field_errors}")

            # Return detailed error response
            return Response({
                'error': 'Validation failed',
                'details': serializer.errors,
                'non_field_errors': non_field_errors,
                'received_data': request.data
            }, status=status.HTTP_400_BAD_REQUEST)

        response = super().post(request, *args, **kwargs)

        # Log validation errors if any
        if response.status_code == status.HTTP_400_BAD_REQUEST:
            logger.warning(f"❌ Alert creation failed with status 400")
            logger.warning(f"📝 Response data: {response.data}")
            return response

        if response.status_code == status.HTTP_201_CREATED:
            # Log alert creation
            AuditLog.objects.create(
                user=request.user,
                action='CREATE',
                resource_type='PriceAlert',
                resource_id=str(response.data['id']),
                ip_address=self._get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                details={
                    'alert_type': response.data['alert_type'],
                    'threshold_value': str(response.data['threshold_value']),
                    'asset': response.data.get('asset_symbol'),
                    'portfolio': response.data.get('portfolio_name')
                }
            )

            # Record metrics
            record_alert_created(response.data['alert_type'], str(request.user.id))
            update_active_alerts_gauge()

        return response

    def _get_client_ip(self, request):
        """Get client IP address."""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class PriceAlertDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Retrieve, update, or delete a price alert."""

    serializer_class = PriceAlertSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        """Get alerts for the current user."""
        return PriceAlert.objects.filter(user=self.request.user).select_related(
            'asset', 'portfolio'
        )

    @extend_schema(
        summary="Get Price Alert",
        description="Retrieve a specific price alert",
        responses={200: PriceAlertSerializer},
        tags=["Alerts"]
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)

    @extend_schema(
        summary="Update Price Alert",
        description="Update a price alert",
        request=PriceAlertSerializer,
        responses={200: PriceAlertSerializer},
        tags=["Alerts"]
    )
    def put(self, request, *args, **kwargs):
        return super().put(request, *args, **kwargs)

    @extend_schema(
        summary="Delete Price Alert",
        description="Delete a price alert",
        responses={204: None},
        tags=["Alerts"]
    )
    def delete(self, request, *args, **kwargs):
        alert = self.get_object()

        # Log alert deletion
        AuditLog.objects.create(
            user=request.user,
            action='DELETE',
            resource_type='PriceAlert',
            resource_id=str(alert.id),
            ip_address=self._get_client_ip(request),
            user_agent=request.META.get('HTTP_USER_AGENT', ''),
            details={
                'alert_name': alert.name,
                'alert_type': alert.alert_type
            }
        )

        return super().delete(request, *args, **kwargs)

    def _get_client_ip(self, request):
        """Get client IP address."""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class AlertHistoryListView(generics.ListAPIView):
    """List alert trigger history."""

    serializer_class = AlertHistorySerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['alert', 'alert__alert_type']
    ordering_fields = ['created_at', 'triggered_value']
    ordering = ['-created_at']

    def get_queryset(self):
        """Get alert history for the current user."""
        return AlertHistory.objects.filter(user=self.request.user).select_related(
            'alert', 'alert__asset'
        )

    @extend_schema(
        summary="List Alert History",
        description="Get paginated list of alert trigger history",
        responses={200: AlertHistorySerializer(many=True)},
        tags=["Alerts"]
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)


class NotificationPreferenceView(generics.RetrieveUpdateAPIView):
    """Get and update notification preferences."""

    serializer_class = NotificationPreferenceSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_object(self):
        """Get or create notification preferences for the user."""
        preferences, created = NotificationPreference.objects.get_or_create(
            user=self.request.user,
            defaults={
                'email_address': self.request.user.email,
            }
        )
        return preferences

    @extend_schema(
        summary="Get Notification Preferences",
        description="Get user's notification preferences",
        responses={200: NotificationPreferenceSerializer},
        tags=["Notifications"]
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)

    @extend_schema(
        summary="Update Notification Preferences",
        description="Update user's notification preferences",
        request=NotificationPreferenceSerializer,
        responses={200: NotificationPreferenceSerializer},
        tags=["Notifications"]
    )
    def put(self, request, *args, **kwargs):
        response = super().put(request, *args, **kwargs)

        if response.status_code == status.HTTP_200_OK:
            # Log preference update
            AuditLog.objects.create(
                user=request.user,
                action='UPDATE',
                resource_type='NotificationPreference',
                resource_id=str(response.data['id']),
                ip_address=self._get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                details={'updated_preferences': True}
            )

        return response

    def _get_client_ip(self, request):
        """Get client IP address."""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class NotificationListView(generics.ListAPIView):
    """List sent notifications."""

    serializer_class = NotificationSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['channel', 'status', 'alert']
    ordering_fields = ['created_at', 'sent_at']
    ordering = ['-created_at']

    def get_queryset(self):
        """Get notifications for the current user."""
        return Notification.objects.filter(user=self.request.user).select_related('alert')

    @extend_schema(
        summary="List Notifications",
        description="Get paginated list of sent notifications",
        responses={200: NotificationSerializer(many=True)},
        tags=["Notifications"]
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)


class ReportListCreateView(generics.ListCreateAPIView):
    """List and create reports."""

    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['report_type', 'report_format', 'status', 'portfolio']
    search_fields = ['name', 'description']
    ordering_fields = ['created_at', 'generated_at']
    ordering = ['-created_at']

    def get_serializer_class(self):
        """Use different serializers for list and create."""
        if self.request.method == 'POST':
            return ReportCreateSerializer
        return ReportSerializer

    def get_queryset(self):
        """Get reports for the current user."""
        return Report.objects.filter(user=self.request.user).select_related('portfolio')

    @extend_schema(
        summary="List Reports",
        description="Get paginated list of user's reports",
        responses={200: ReportSerializer(many=True)},
        tags=["Reports"]
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)

    @extend_schema(
        summary="Create Report",
        description="Create a new report (will be generated asynchronously)",
        request=ReportCreateSerializer,
        responses={201: ReportSerializer},
        tags=["Reports"]
    )
    def post(self, request, *args, **kwargs):
        # Use create serializer for validation
        create_serializer = ReportCreateSerializer(data=request.data, context={'request': request})

        if create_serializer.is_valid():
            # Create the report
            report = create_serializer.save()

            # Return full report data using the main serializer
            response_serializer = ReportSerializer(report, context={'request': request})

            # Log report creation
            AuditLog.objects.create(
                user=request.user,
                action='CREATE',
                resource_type='Report',
                resource_id=str(report.id),
                ip_address=self._get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                details={
                    'report_type': report.report_type,
                    'report_format': report.report_format
                }
            )

            # Record metrics
            record_report_generated(report.report_type, report.report_format)

            # TODO: Trigger async report generation task
            # from .tasks import generate_report
            # generate_report.delay(str(report.id))

            return Response(response_serializer.data, status=status.HTTP_201_CREATED)
        else:
            return Response(create_serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def _get_client_ip(self, request):
        """Get client IP address."""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class ReportDetailView(generics.RetrieveDestroyAPIView):
    """Retrieve or delete a report."""

    serializer_class = ReportSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        """Get reports for the current user."""
        return Report.objects.filter(user=self.request.user).select_related('portfolio')

    @extend_schema(
        summary="Get Report",
        description="Retrieve a specific report",
        responses={200: ReportSerializer},
        tags=["Reports"]
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)

    @extend_schema(
        summary="Delete Report",
        description="Delete a report and its associated file",
        responses={204: None},
        tags=["Reports"]
    )
    def delete(self, request, *args, **kwargs):
        report = self.get_object()

        # Log report deletion
        AuditLog.objects.create(
            user=request.user,
            action='DELETE',
            resource_type='Report',
            resource_id=str(report.id),
            ip_address=self._get_client_ip(request),
            user_agent=request.META.get('HTTP_USER_AGENT', ''),
            details={
                'report_name': report.name,
                'report_type': report.report_type
            }
        )

        # TODO: Delete associated file
        # if report.file_path and os.path.exists(report.file_path):
        #     os.remove(report.file_path)

        return super().delete(request, *args, **kwargs)

    def _get_client_ip(self, request):
        """Get client IP address."""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class ReportDownloadView(APIView):
    """Download a generated report."""

    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(
        summary="Download Report",
        description="Download a generated report file",
        responses={200: OpenApiTypes.BINARY},
        tags=["Reports"]
    )
    def get(self, request, pk):
        """Download report file."""
        try:
            report = Report.objects.get(id=pk, user=request.user)
        except Report.DoesNotExist:
            raise Http404("Report not found")

        if report.status != 'COMPLETED' or not report.file_path:
            return Response(
                {'error': 'Report is not ready for download'},
                status=status.HTTP_400_BAD_REQUEST
            )

        if report.is_expired():
            return Response(
                {'error': 'Report has expired'},
                status=status.HTTP_410_GONE
            )

        # TODO: Implement actual file serving
        # For now, return a placeholder response
        response = HttpResponse(
            content_type='application/pdf' if report.report_format == 'PDF' else 'application/octet-stream'
        )
        response['Content-Disposition'] = f'attachment; filename="{report.name}.{report.report_format.lower()}"'
        response.write(b'Placeholder report content')

        # Log report download
        AuditLog.objects.create(
            user=request.user,
            action='ACCESS',
            resource_type='Report',
            resource_id=str(report.id),
            ip_address=self._get_client_ip(request),
            user_agent=request.META.get('HTTP_USER_AGENT', ''),
            details={
                'action': 'download',
                'report_name': report.name
            }
        )

        return response

    def _get_client_ip(self, request):
        """Get client IP address."""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class AlertChoicesView(APIView):
    """Get choices for alert-related fields."""

    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(
        summary="Get Alert Choices",
        description="Get available choices for alert types, statuses, and notification channels",
        responses={200: {
            'alert_types': AlertTypeChoicesSerializer(many=True),
            'alert_statuses': AlertStatusChoicesSerializer(many=True),
            'notification_channels': NotificationChannelChoicesSerializer(many=True),
        }},
        tags=["Alerts"]
    )
    def get(self, request):
        """Get alert-related choices."""
        return Response({
            'alert_types': [
                {'value': choice[0], 'label': choice[1]}
                for choice in AlertType.choices
            ],
            'alert_statuses': [
                {'value': choice[0], 'label': choice[1]}
                for choice in AlertStatus.choices
            ],
            'notification_channels': [
                {'value': choice[0], 'label': choice[1]}
                for choice in NotificationChannel.choices
            ],
        })


class AlertStatsView(APIView):
    """Get alert statistics for the user."""

    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(
        summary="Get Alert Statistics",
        description="Get statistics about user's alerts",
        responses={200: {
            'total_alerts': OpenApiTypes.INT,
            'active_alerts': OpenApiTypes.INT,
            'triggered_alerts': OpenApiTypes.INT,
            'alerts_by_type': OpenApiTypes.OBJECT,
            'recent_triggers': OpenApiTypes.INT,
        }},
        tags=["Alerts"]
    )
    def get(self, request):
        """Get alert statistics."""
        user_alerts = PriceAlert.objects.filter(user=request.user)

        # Basic counts
        total_alerts = user_alerts.count()
        active_alerts = user_alerts.filter(status=AlertStatus.ACTIVE).count()
        triggered_alerts = user_alerts.filter(status=AlertStatus.TRIGGERED).count()

        # Alerts by type
        alerts_by_type = dict(
            user_alerts.values('alert_type').annotate(
                count=Count('id')
            ).values_list('alert_type', 'count')
        )

        # Recent triggers (last 7 days)
        recent_triggers = AlertHistory.objects.filter(
            user=request.user,
            created_at__gte=timezone.now() - timezone.timedelta(days=7)
        ).count()

        return Response({
            'total_alerts': total_alerts,
            'active_alerts': active_alerts,
            'triggered_alerts': triggered_alerts,
            'alerts_by_type': alerts_by_type,
            'recent_triggers': recent_triggers,
        })


class AlertsMetricsView(APIView):
    """Get comprehensive alerts metrics."""

    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(
        summary="Get Alerts Metrics",
        description="Get comprehensive metrics about the alerts system",
        responses={200: {
            'alert_counts': OpenApiTypes.OBJECT,
            'triggers_24h': OpenApiTypes.INT,
            'notification_stats': OpenApiTypes.OBJECT,
            'report_stats': OpenApiTypes.OBJECT,
            'user_engagement': OpenApiTypes.OBJECT,
        }},
        tags=["Alerts"]
    )
    def get(self, request):
        """Get comprehensive alerts metrics."""
        # Only allow admin users to see system-wide metrics
        if not request.user.is_staff:
            return Response(
                {'error': 'Permission denied. Admin access required.'},
                status=status.HTTP_403_FORBIDDEN
            )

        metrics = metrics_collector.collect_all_metrics()
        return Response(metrics)


class CreateTestNotificationsView(APIView):
    """Create test notifications for development."""

    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(
        summary="Create Test Notifications",
        description="Create test notifications for development and testing",
        responses={201: {'message': 'Test notifications created'}},
        tags=["Notifications"]
    )
    def post(self, request):
        """Create test notifications."""
        if not request.user.is_staff:
            return Response(
                {'error': 'Permission denied. Admin access required.'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Create test notifications
        test_notifications = [
            {
                'channel': 'IN_APP',
                'subject': 'AAPL Price Alert Triggered',
                'message': 'Your AAPL price alert has been triggered. Current price: $165.50',
                'status': 'DELIVERED',
            },
            {
                'channel': 'IN_APP',
                'subject': 'Portfolio Performance Report Ready',
                'message': 'Your Q1 2024 portfolio performance report has been generated and is ready for download.',
                'status': 'DELIVERED',
            },
            {
                'channel': 'IN_APP',
                'subject': 'New Security Alert',
                'message': 'Unusual login activity detected from a new device. Please verify if this was you.',
                'status': 'DELIVERED',
            },
            {
                'channel': 'EMAIL',
                'subject': 'Weekly Portfolio Summary',
                'message': 'Your portfolio gained 2.5% this week. View detailed analysis in your dashboard.',
                'status': 'SENT',
            },
            {
                'channel': 'IN_APP',
                'subject': 'Market Alert: High Volatility',
                'message': 'Market volatility is currently high. Consider reviewing your risk tolerance.',
                'status': 'DELIVERED',
            },
        ]

        created_count = 0
        for notification_data in test_notifications:
            notification = Notification.objects.create(
                user=request.user,
                recipient=f"user_{request.user.id}",
                **notification_data
            )
            created_count += 1

        return Response({
            'message': f'Created {created_count} test notifications',
            'count': created_count
        }, status=status.HTTP_201_CREATED)


class AlertCreationTestView(APIView):
    """Test alert creation with detailed debugging."""

    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(
        summary="Test Alert Creation",
        description="Test alert creation with detailed debugging information",
        responses={200: {'message': 'Test completed'}},
        tags=["Alerts"]
    )
    def post(self, request):
        """Test alert creation."""
        logger.info("🧪 ALERT CREATION TEST STARTED")

        # Get or create test asset
        from apps.portfolio.models import Asset
        from decimal import Decimal

        asset, created = Asset.objects.get_or_create(
            symbol='TEST',
            defaults={
                'name': 'Test Asset',
                'asset_type': 'STOCK',
                'current_price': Decimal('100.00'),
                'currency': 'USD'
            }
        )

        # Test data
        test_data = {
            'name': 'Test Alert via API',
            'alert_type': 'PRICE_ABOVE',
            'asset': str(asset.id),
            'threshold_value': 110.00,
            'comparison_operator': 'GT',
            'notification_channels': ['EMAIL'],
            'max_triggers': 3,
            'cooldown_minutes': 30
        }

        logger.info(f"📝 Test data: {test_data}")

        # Test serializer
        serializer = PriceAlertSerializer(data=test_data, context={'request': request})

        if serializer.is_valid():
            alert = serializer.save()
            logger.info(f"✅ Test alert created successfully: {alert.id}")

            return Response({
                'success': True,
                'message': 'Test alert created successfully',
                'alert_id': str(alert.id),
                'alert_name': alert.name,
                'test_data': test_data,
                'validated_data': serializer.validated_data
            })
        else:
            logger.error(f"❌ Test alert creation failed: {serializer.errors}")

            return Response({
                'success': False,
                'message': 'Test alert creation failed',
                'errors': serializer.errors,
                'test_data': test_data
            }, status=status.HTTP_400_BAD_REQUEST)

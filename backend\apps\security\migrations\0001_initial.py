# Generated by Django 4.2.7 on 2025-07-27 14:43

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('core', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='ThreatIntelligence',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('threat_type', models.CharField(choices=[('IP_BLACKLIST', 'IP Blacklist'), ('DOMAIN_BLACKLIST', 'Domain Blacklist'), ('MALWARE_HASH', 'Malware Hash'), ('IOC', 'Indicator of Compromise'), ('CVE', 'Common Vulnerabilities and Exposures'), ('SIGNATURE', 'Attack Signature')], max_length=20)),
                ('value', models.TextField()),
                ('description', models.TextField()),
                ('severity', models.CharField(choices=[('LOW', 'Low'), ('MEDIUM', 'Medium'), ('HIGH', 'High'), ('CRITICAL', 'Critical')], max_length=10)),
                ('source', models.CharField(max_length=100)),
                ('confidence', models.IntegerField(default=50)),
                ('first_seen', models.DateTimeField(default=django.utils.timezone.now)),
                ('last_seen', models.DateTimeField(default=django.utils.timezone.now)),
                ('expires_at', models.DateTimeField(blank=True, null=True)),
                ('tags', models.JSONField(blank=True, default=list)),
                ('references', models.JSONField(blank=True, default=list)),
            ],
            options={
                'db_table': 'security_threat_intelligence',
                'ordering': ['-last_seen'],
                'indexes': [models.Index(fields=['threat_type', 'value'], name='security_th_threat__c27c21_idx'), models.Index(fields=['severity', 'last_seen'], name='security_th_severit_6869ab_idx'), models.Index(fields=['expires_at'], name='security_th_expires_82f8fe_idx')],
            },
        ),
        migrations.CreateModel(
            name='SecurityMetric',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('metric_type', models.CharField(choices=[('EVENT_COUNT', 'Event Count'), ('THREAT_LEVEL', 'Threat Level'), ('RESPONSE_TIME', 'Response Time'), ('UPTIME', 'System Uptime'), ('FAILED_LOGINS', 'Failed Login Attempts'), ('BLOCKED_IPS', 'Blocked IP Addresses'), ('VULNERABILITY_COUNT', 'Vulnerability Count'), ('COMPLIANCE_SCORE', 'Compliance Score')], max_length=20)),
                ('value', models.DecimalField(decimal_places=4, max_digits=15)),
                ('unit', models.CharField(blank=True, max_length=20)),
                ('period_start', models.DateTimeField()),
                ('period_end', models.DateTimeField()),
                ('context', models.JSONField(blank=True, default=dict)),
            ],
            options={
                'db_table': 'security_metric',
                'ordering': ['-period_end'],
                'indexes': [models.Index(fields=['metric_type', 'period_end'], name='security_me_metric__54fbfb_idx'), models.Index(fields=['period_start', 'period_end'], name='security_me_period__f9eb30_idx')],
            },
        ),
        migrations.CreateModel(
            name='SecurityAlert',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('alert_type', models.CharField(choices=[('THRESHOLD_EXCEEDED', 'Threshold Exceeded'), ('ANOMALY_DETECTED', 'Anomaly Detected'), ('CRITICAL_EVENT', 'Critical Event'), ('SYSTEM_DOWN', 'System Down'), ('COMPLIANCE_VIOLATION', 'Compliance Violation'), ('MAINTENANCE_REQUIRED', 'Maintenance Required')], max_length=25)),
                ('priority', models.CharField(choices=[('LOW', 'Low'), ('MEDIUM', 'Medium'), ('HIGH', 'High'), ('URGENT', 'Urgent')], max_length=10)),
                ('title', models.CharField(max_length=200)),
                ('message', models.TextField()),
                ('is_acknowledged', models.BooleanField(default=False)),
                ('acknowledged_at', models.DateTimeField(blank=True, null=True)),
                ('notification_sent', models.BooleanField(default=False)),
                ('notification_channels', models.JSONField(blank=True, default=list)),
                ('acknowledged_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='acknowledged_alerts', to=settings.AUTH_USER_MODEL)),
                ('security_event', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='security_alerts', to='core.securityevent')),
            ],
            options={
                'db_table': 'security_alert',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['alert_type', 'created_at'], name='security_al_alert_t_2102b8_idx'), models.Index(fields=['priority', 'created_at'], name='security_al_priorit_bc1b8a_idx'), models.Index(fields=['is_acknowledged', 'created_at'], name='security_al_is_ackn_3504ac_idx')],
            },
        ),
    ]

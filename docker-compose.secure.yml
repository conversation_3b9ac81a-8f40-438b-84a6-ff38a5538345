version: '3.8'

services:
  # Secure Nginx Reverse Proxy with SSL
  nginx-ssl:
    image: nginx:alpine
    container_name: trustvault-nginx-ssl
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/ssl-nginx.conf:/etc/nginx/nginx.conf:ro
      - ./security/ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - django
      - react
      - prometheus
      - grafana
    networks:
      - trustvault-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Fail2Ban for Intrusion Prevention
  fail2ban:
    image: crazymax/fail2ban:latest
    container_name: trustvault-fail2ban
    network_mode: "host"
    cap_add:
      - NET_ADMIN
      - NET_RAW
    volumes:
      - ./fail2ban:/data
      - ./logs:/var/log:ro
    environment:
      - TZ=UTC
      - F2B_LOG_LEVEL=INFO
      - F2B_DB_PURGE_AGE=1d
    restart: unless-stopped

  # Security Scanner (runs periodically)
  security-scanner:
    build:
      context: .
      dockerfile: Dockerfile.security-scanner
    container_name: trustvault-security-scanner
    volumes:
      - ./security/reports:/reports
      - ./logs:/logs:ro
    environment:
      - SCAN_INTERVAL=3600  # 1 hour
      - TARGET_HOSTS=django:8000,react:80
    networks:
      - trustvault-network
    restart: unless-stopped
    profiles:
      - security

networks:
  trustvault-network:
    external: true

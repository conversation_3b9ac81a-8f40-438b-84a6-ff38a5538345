// TrustVault - Authentication Store

import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import { User, AuthTokens } from '../types';
import apiService from '../services/api';

interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  login: (email: string, password: string, rememberMe?: boolean) => Promise<void>;
  register: (data: any) => Promise<void>;
  logout: () => Promise<void>;
  loadUser: () => Promise<void>;
  updateProfile: (data: Partial<User>) => Promise<void>;
  changePassword: (data: any) => Promise<void>;
  clearError: () => void;
  setLoading: (loading: boolean) => void;
}

export const useAuthStore = create<AuthState>()(
  persist(
    immer((set, get) => ({
      user: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      login: async (email: string, password: string, rememberMe = false) => {
        set((state) => {
          state.isLoading = true;
          state.error = null;
        });

        try {
          const response = await apiService.login({
            email,
            password,
            remember_me: rememberMe,
          });

          if (response.mfa_required) {
            // Handle MFA flow
            set((state) => {
              state.isLoading = false;
              state.error = 'MFA verification required';
            });
            return;
          }

          set((state) => {
            state.user = response.user;
            state.isAuthenticated = true;
            state.isLoading = false;
            state.error = null;
          });
        } catch (error: any) {
          set((state) => {
            state.isLoading = false;
            state.error = error.response?.data?.message || 'Login failed';
          });
          throw error;
        }
      },

      register: async (data: any) => {
        set((state) => {
          state.isLoading = true;
          state.error = null;
        });

        try {
          await apiService.register(data);
          
          set((state) => {
            state.isLoading = false;
            state.error = null;
          });
        } catch (error: any) {
          set((state) => {
            state.isLoading = false;
            state.error = error.response?.data?.message || 'Registration failed';
          });
          throw error;
        }
      },

      logout: async () => {
        set((state) => {
          state.isLoading = true;
        });

        try {
          await apiService.logout();
        } catch (error) {
          // Continue with logout even if API call fails
          console.error('Logout error:', error);
        } finally {
          set((state) => {
            state.user = null;
            state.isAuthenticated = false;
            state.isLoading = false;
            state.error = null;
          });
        }
      },

      loadUser: async () => {
        const token = localStorage.getItem('access_token');
        if (!token) {
          return;
        }

        set((state) => {
          state.isLoading = true;
        });

        try {
          const user = await apiService.getCurrentUser();
          
          set((state) => {
            state.user = user;
            state.isAuthenticated = true;
            state.isLoading = false;
            state.error = null;
          });
        } catch (error: any) {
          // Token might be invalid
          localStorage.removeItem('access_token');
          localStorage.removeItem('refresh_token');
          
          set((state) => {
            state.user = null;
            state.isAuthenticated = false;
            state.isLoading = false;
            state.error = null;
          });
        }
      },

      updateProfile: async (data: Partial<User>) => {
        set((state) => {
          state.isLoading = true;
          state.error = null;
        });

        try {
          const updatedUser = await apiService.updateProfile(data);
          
          set((state) => {
            state.user = updatedUser;
            state.isLoading = false;
            state.error = null;
          });
        } catch (error: any) {
          set((state) => {
            state.isLoading = false;
            state.error = error.response?.data?.message || 'Profile update failed';
          });
          throw error;
        }
      },

      changePassword: async (data: any) => {
        set((state) => {
          state.isLoading = true;
          state.error = null;
        });

        try {
          await apiService.changePassword(data);
          
          set((state) => {
            state.isLoading = false;
            state.error = null;
          });
        } catch (error: any) {
          set((state) => {
            state.isLoading = false;
            state.error = error.response?.data?.message || 'Password change failed';
          });
          throw error;
        }
      },

      clearError: () => {
        set((state) => {
          state.error = null;
        });
      },

      setLoading: (loading: boolean) => {
        set((state) => {
          state.isLoading = loading;
        });
      },
    })),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);

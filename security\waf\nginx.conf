# TrustVault - Advanced Web Application Firewall Configuration
# This configuration implements comprehensive WAF protection using ModSecurity

user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

# Load ModSecurity module
load_module modules/ngx_http_modsecurity_module.so;

events {
    worker_connections 4096;
    use epoll;
    multi_accept on;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Logging format for security analysis
    log_format security_log '$remote_addr - $remote_user [$time_local] '
                           '"$request" $status $body_bytes_sent '
                           '"$http_referer" "$http_user_agent" '
                           '$request_time $upstream_response_time '
                           '$modsec_transaction_id';

    access_log /var/log/nginx/access.log security_log;

    # Basic security settings
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    server_tokens off;

    # Buffer size limits
    client_body_buffer_size 128k;
    client_max_body_size 10m;
    client_header_buffer_size 1k;
    large_client_header_buffers 4 4k;
    output_buffers 1 32k;
    postpone_output 1460;

    # Timeouts
    client_header_timeout 3m;
    client_body_timeout 3m;
    send_timeout 3m;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 10240;
    gzip_proxied expired no-cache no-store private must-revalidate auth;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/x-javascript
        application/xml+rss
        application/javascript
        application/json;

    # Rate limiting zones
    limit_req_zone $binary_remote_addr zone=login:10m rate=5r/m;
    limit_req_zone $binary_remote_addr zone=api:10m rate=100r/m;
    limit_req_zone $binary_remote_addr zone=general:10m rate=200r/m;

    # Connection limiting
    limit_conn_zone $binary_remote_addr zone=conn_limit_per_ip:10m;

    # ModSecurity configuration
    modsecurity on;
    modsecurity_rules_file /etc/nginx/modsecurity/modsecurity.conf;

    # SSL configuration
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    ssl_session_tickets off;
    ssl_stapling on;
    ssl_stapling_verify on;

    # Security headers map
    map $sent_http_content_type $csp_header {
        ~^text/html "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' https:; connect-src 'self'; frame-ancestors 'none'; base-uri 'self'; form-action 'self'";
        default "";
    }

    # Upstream backend servers
    upstream backend_servers {
        least_conn;
        server backend:8000 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }

    upstream frontend_servers {
        least_conn;
        server frontend:3000 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }

    # HTTP to HTTPS redirect
    server {
        listen 80;
        server_name _;
        return 301 https://$host$request_uri;
    }

    # Main HTTPS server
    server {
        listen 443 ssl http2;
        server_name trustvault.local;

        # SSL certificates
        ssl_certificate /etc/nginx/ssl/trustvault.crt;
        ssl_certificate_key /etc/nginx/ssl/trustvault.key;

        # Security headers
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
        add_header X-Frame-Options "DENY" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header Referrer-Policy "strict-origin-when-cross-origin" always;
        add_header Permissions-Policy "geolocation=(), microphone=(), camera=()" always;
        add_header Content-Security-Policy $csp_header always;

        # Rate limiting
        limit_req zone=general burst=50 nodelay;
        limit_conn conn_limit_per_ip 20;

        # Frontend static files
        location / {
            proxy_pass http://frontend_servers;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Host $host;
            proxy_set_header X-Forwarded-Port $server_port;

            # Caching for static assets
            location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
                expires 1y;
                add_header Cache-Control "public, immutable";
                proxy_pass http://frontend_servers;
            }
        }

        # API endpoints with enhanced security
        location /api/ {
            # Enhanced rate limiting for API
            limit_req zone=api burst=20 nodelay;

            # ModSecurity specific rules for API
            modsecurity_rules '
                SecRuleEngine On
                SecRule REQUEST_HEADERS:Content-Type "!@beginsWith application/json" \
                    "id:1001,phase:1,block,msg:Only JSON content allowed for API"
                SecRule REQUEST_METHOD "!@rx ^(GET|POST|PUT|DELETE|PATCH)$" \
                    "id:1002,phase:1,block,msg:Invalid HTTP method"
            ';

            proxy_pass http://backend_servers;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Host $host;
            proxy_set_header X-Forwarded-Port $server_port;

            # API-specific headers
            add_header X-API-Version "1.0" always;
            add_header X-Rate-Limit-Remaining $limit_req_status always;
        }

        # Authentication endpoints with strict rate limiting
        location /api/v1/auth/ {
            limit_req zone=login burst=5 nodelay;

            # Enhanced ModSecurity rules for auth endpoints
            modsecurity_rules '
                SecRuleEngine On
                SecRule ARGS "@detectSQLi" \
                    "id:2001,phase:2,block,msg:SQL Injection Attack Detected"
                SecRule ARGS "@detectXSS" \
                    "id:2002,phase:2,block,msg:XSS Attack Detected"
                SecRule REQUEST_BODY "@detectSQLi" \
                    "id:2003,phase:2,block,msg:SQL Injection in Request Body"
            ';

            proxy_pass http://backend_servers;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Admin endpoints with IP restrictions
        location /admin/ {
            # Restrict to specific IP ranges (adjust as needed)
            allow ***********/24;
            allow 10.0.0.0/8;
            allow **********/12;
            deny all;

            limit_req zone=general burst=10 nodelay;

            proxy_pass http://backend_servers;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Health check endpoint (no rate limiting)
        location /health/ {
            proxy_pass http://backend_servers;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            access_log off;
        }

        # Block common attack patterns
        location ~* \.(php|asp|aspx|jsp|cgi)$ {
            return 444;
        }

        location ~* /\.(git|svn|hg|bzr) {
            return 444;
        }

        location ~* \.(bak|backup|old|orig|save|swp|tmp)$ {
            return 444;
        }

        # Custom error pages
        error_page 400 401 402 403 404 /error.html;
        error_page 500 501 502 503 504 /error.html;

        location = /error.html {
            root /usr/share/nginx/html;
            internal;
        }
    }

    # Security monitoring server (internal only)
    server {
        listen 8081;
        server_name localhost;
        allow 127.0.0.1;
        allow **********/16;  # Docker network
        deny all;

        location /nginx_status {
            stub_status on;
            access_log off;
        }

        location /modsec_status {
            modsecurity_rules '
                SecRuleEngine DetectionOnly
                SecDebugLog /var/log/nginx/modsec_debug.log
                SecDebugLogLevel 3
            ';
            return 200 "ModSecurity Status: Active\n";
            add_header Content-Type text/plain;
        }
    }
}

# Stream module for TCP/UDP load balancing
stream {
    # Log format for stream connections
    log_format stream_log '$remote_addr [$time_local] $protocol $bytes_sent $bytes_received $session_time';
    access_log /var/log/nginx/stream.log stream_log;

    # Rate limiting for stream connections
    limit_conn_zone $binary_remote_addr zone=stream_conn:10m;

    # Database connection proxy (if needed)
    upstream database_servers {
        server postgres:5432 max_fails=3 fail_timeout=30s;
    }

    server {
        listen 5432;
        proxy_pass database_servers;
        proxy_timeout 1s;
        proxy_responses 1;
        limit_conn stream_conn 10;
        
        # Only allow connections from application network
        allow **********/16;
        deny all;
    }
}

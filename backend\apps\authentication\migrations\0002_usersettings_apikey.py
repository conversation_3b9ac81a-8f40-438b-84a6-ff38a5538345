# Generated by Django 4.2.7 on 2025-07-29 13:04

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('authentication', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='UserSettings',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('email_notifications', models.BooleanField(default=True)),
                ('push_notifications', models.BooleanField(default=True)),
                ('security_notifications', models.BooleanField(default=True)),
                ('portfolio_notifications', models.BooleanField(default=True)),
                ('marketing_notifications', models.BooleanField(default=False)),
                ('news_notifications', models.<PERSON>oleanField(default=True)),
                ('email_frequency', models.CharField(choices=[('immediate', 'Immediate'), ('daily', 'Daily'), ('weekly', 'Weekly'), ('never', 'Never')], default='daily', max_length=20)),
                ('profile_visibility', models.CharField(choices=[('public', 'Public'), ('private', 'Private')], default='private', max_length=10)),
                ('portfolio_visibility', models.CharField(choices=[('public', 'Public'), ('private', 'Private')], default='private', max_length=10)),
                ('data_sharing', models.BooleanField(default=False)),
                ('analytics_enabled', models.BooleanField(default=True)),
                ('theme_preference', models.CharField(choices=[('light', 'Light'), ('dark', 'Dark'), ('auto', 'Auto')], default='light', max_length=10)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='settings', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'auth_user_settings',
            },
        ),
        migrations.CreateModel(
            name='ApiKey',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=100)),
                ('key_hash', models.CharField(max_length=128, unique=True)),
                ('key_prefix', models.CharField(max_length=8)),
                ('permissions', models.JSONField(default=list)),
                ('is_active', models.BooleanField(default=True)),
                ('last_used', models.DateTimeField(blank=True, null=True)),
                ('expires_at', models.DateTimeField(blank=True, null=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='api_keys', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'auth_api_key',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['user', 'is_active'], name='auth_api_ke_user_id_911088_idx'), models.Index(fields=['key_hash'], name='auth_api_ke_key_has_8e1a5d_idx'), models.Index(fields=['expires_at'], name='auth_api_ke_expires_7caa43_idx')],
            },
        ),
    ]

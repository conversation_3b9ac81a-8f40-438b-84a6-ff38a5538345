#!/bin/bash

# TrustVault - Deployment and Initialization Script
# ============================================================================

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running in WSL2
check_wsl2() {
    if grep -qi microsoft /proc/version; then
        log_info "Running in WSL2 environment"
        return 0
    else
        log_warning "Not running in WSL2 - some features may not work as expected"
        return 1
    fi
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    local missing_tools=()
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        missing_tools+=("docker")
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        missing_tools+=("docker-compose")
    fi
    
    # Check OpenSSL
    if ! command -v openssl &> /dev/null; then
        missing_tools+=("openssl")
    fi
    
    # Check Git
    if ! command -v git &> /dev/null; then
        missing_tools+=("git")
    fi
    
    if [ ${#missing_tools[@]} -ne 0 ]; then
        log_error "Missing required tools: ${missing_tools[*]}"
        log_info "Please install the missing tools and run this script again"
        exit 1
    fi
    
    log_success "All prerequisites are installed"
}

# Generate environment file
generate_env_file() {
    log_info "Generating environment configuration..."
    
    local env_file="$PROJECT_ROOT/.env"
    
    if [[ -f "$env_file" ]]; then
        log_warning ".env file already exists, backing up to .env.backup"
        cp "$env_file" "$env_file.backup"
    fi
    
    # Generate random passwords
    local db_password=$(openssl rand -base64 32)
    local redis_password=$(openssl rand -base64 32)
    local django_secret=$(openssl rand -base64 50)
    local jwt_secret=$(openssl rand -base64 32)
    local vault_token=$(openssl rand -base64 32)
    local wazuh_password=$(openssl rand -base64 32)
    local wazuh_api_password=$(openssl rand -base64 32)
    local grafana_password=$(openssl rand -base64 32)
    local restic_password=$(openssl rand -base64 32)
    
    # Create .env file
    cat > "$env_file" << EOF
# TrustVault Environment Configuration
# Generated on $(date)

# Database Configuration
DB_PASSWORD=$db_password
POSTGRES_DB=trustvault
POSTGRES_USER=trustvault

# Redis Configuration
REDIS_PASSWORD=$redis_password

# Django Configuration
DJANGO_SECRET_KEY=$django_secret
DEBUG=False
ALLOWED_HOSTS=localhost,127.0.0.1,trustvault.local,api.trustvault.local

# HashiCorp Vault
VAULT_ROOT_TOKEN=$vault_token

# Wazuh SIEM Configuration
WAZUH_PASSWORD=$wazuh_password
WAZUH_API_PASSWORD=$wazuh_api_password

# Monitoring
GRAFANA_USER=admin
GRAFANA_PASSWORD=$grafana_password

# JWT Configuration
JWT_SECRET_KEY=$jwt_secret
JWT_EXPIRATION_HOURS=1
JWT_REFRESH_EXPIRATION_DAYS=7

# Backup Configuration
RESTIC_PASSWORD=$restic_password
BACKUP_SCHEDULE=0 2 * * *

# Security Settings
FAIL2BAN_BANTIME=3600
FAIL2BAN_MAXRETRY=3
MODSECURITY_PARANOIA_LEVEL=2

# Network Security
RATE_LIMIT_PER_MINUTE=60

# Compliance
GDPR_ENABLED=True
AUDIT_LOG_RETENTION_DAYS=2555
EOF
    
    chmod 600 "$env_file"
    log_success "Environment file generated: $env_file"
}

# Generate secrets
generate_secrets() {
    log_info "Generating application secrets..."
    
    local secrets_dir="$PROJECT_ROOT/secrets"
    mkdir -p "$secrets_dir"
    
    # Generate Django secret
    openssl rand -base64 50 > "$secrets_dir/django_secret.txt"
    
    # Generate database password
    openssl rand -base64 32 > "$secrets_dir/db_password.txt"
    
    # Set proper permissions
    chmod 600 "$secrets_dir"/*
    
    log_success "Application secrets generated"
}

# Generate SSL certificates
generate_ssl_certificates() {
    log_info "Generating SSL certificates..."
    
    if [[ -f "$PROJECT_ROOT/nginx/ssl/trustvault.crt" ]]; then
        log_warning "SSL certificates already exist, skipping generation"
        return 0
    fi
    
    bash "$SCRIPT_DIR/generate-ssl-certs.sh"
    log_success "SSL certificates generated"
}

# Initialize Docker volumes
init_docker_volumes() {
    log_info "Initializing Docker volumes..."
    
    # Create named volumes
    docker volume create trustvault_postgres_data || true
    docker volume create trustvault_redis_data || true
    docker volume create trustvault_elasticsearch_data || true
    docker volume create trustvault_wazuh_data || true
    docker volume create trustvault_vault_data || true
    docker volume create trustvault_grafana_data || true
    docker volume create trustvault_prometheus_data || true
    
    log_success "Docker volumes initialized"
}

# Build Docker images
build_docker_images() {
    log_info "Building Docker images..."
    
    cd "$PROJECT_ROOT"
    
    # Build images
    docker-compose build --no-cache
    
    log_success "Docker images built successfully"
}

# Initialize services
init_services() {
    log_info "Initializing services..."
    
    cd "$PROJECT_ROOT"
    
    # Start core services first
    log_info "Starting core services..."
    docker-compose up -d postgres redis vault
    
    # Wait for services to be ready
    log_info "Waiting for services to be ready..."
    sleep 30
    
    # Initialize Vault
    log_info "Initializing HashiCorp Vault..."
    docker-compose exec -T vault vault operator init -key-shares=1 -key-threshold=1 > vault-keys.txt || true
    
    # Start remaining services
    log_info "Starting remaining services..."
    docker-compose up -d
    
    log_success "Services initialized"
}

# Setup monitoring
setup_monitoring() {
    log_info "Setting up monitoring and alerting..."
    
    # Wait for Grafana to be ready
    sleep 30
    
    # Import Grafana dashboards (if available)
    # This would be implemented with actual dashboard JSON files
    
    log_success "Monitoring setup completed"
}

# Run security tests
run_security_tests() {
    log_info "Running basic security tests..."
    
    # Test SSL configuration
    if command -v nmap &> /dev/null; then
        log_info "Testing SSL configuration..."
        nmap --script ssl-enum-ciphers -p 443 localhost || true
    fi
    
    # Test for common vulnerabilities
    if command -v nikto &> /dev/null; then
        log_info "Running Nikto web vulnerability scan..."
        nikto -h https://localhost -ssl || true
    fi
    
    log_success "Security tests completed"
}

# Display deployment information
display_deployment_info() {
    log_success "🎉 TrustVault deployment completed successfully!"
    echo ""
    echo "📋 Deployment Information:"
    echo "=========================="
    echo ""
    echo "🌐 Web Interfaces:"
    echo "   - Main Application: https://trustvault.local"
    echo "   - API Endpoint: https://api.trustvault.local"
    echo "   - Wazuh Dashboard: http://localhost:5601"
    echo "   - Grafana Monitoring: http://localhost:3000"
    echo "   - Prometheus Metrics: http://localhost:9090"
    echo ""
    echo "🔐 Default Credentials:"
    echo "   - Grafana: admin / $(grep GRAFANA_PASSWORD "$PROJECT_ROOT/.env" | cut -d'=' -f2)"
    echo ""
    echo "📁 Important Files:"
    echo "   - Environment: $PROJECT_ROOT/.env"
    echo "   - SSL Certificates: $PROJECT_ROOT/nginx/ssl/"
    echo "   - Vault Keys: $PROJECT_ROOT/vault-keys.txt"
    echo ""
    echo "🔧 Next Steps:"
    echo "   1. Add entries to your /etc/hosts file:"
    echo "      127.0.0.1 trustvault.local"
    echo "      127.0.0.1 api.trustvault.local"
    echo ""
    echo "   2. Import the CA certificate ($PROJECT_ROOT/nginx/ssl/ca.crt) into your browser"
    echo ""
    echo "   3. Access the application at https://trustvault.local"
    echo ""
    echo "📊 Monitoring:"
    echo "   - Check service status: docker-compose ps"
    echo "   - View logs: docker-compose logs -f [service-name]"
    echo "   - Monitor security events in Wazuh dashboard"
    echo ""
    echo "🛡️ Security Features Enabled:"
    echo "   ✅ WAF (ModSecurity)"
    echo "   ✅ IDS/IPS (Suricata)"
    echo "   ✅ SIEM (Wazuh)"
    echo "   ✅ DDoS Protection (Fail2Ban)"
    echo "   ✅ SSL/TLS Encryption"
    echo "   ✅ Database Encryption"
    echo "   ✅ Secrets Management (Vault)"
    echo "   ✅ Automated Backups (Restic)"
    echo "   ✅ Security Monitoring (Prometheus/Grafana)"
}

# Main deployment function
main() {
    echo "🛡️ TrustVault Security Infrastructure Deployment"
    echo "================================================"
    echo ""
    
    check_wsl2 || true
    check_prerequisites
    generate_env_file
    generate_secrets
    generate_ssl_certificates
    init_docker_volumes
    build_docker_images
    init_services
    setup_monitoring
    run_security_tests || true
    display_deployment_info
}

# Handle script arguments
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "update")
        log_info "Updating TrustVault..."
        cd "$PROJECT_ROOT"
        docker-compose pull
        docker-compose up -d --build
        log_success "Update completed"
        ;;
    "stop")
        log_info "Stopping TrustVault..."
        cd "$PROJECT_ROOT"
        docker-compose down
        log_success "TrustVault stopped"
        ;;
    "restart")
        log_info "Restarting TrustVault..."
        cd "$PROJECT_ROOT"
        docker-compose restart
        log_success "TrustVault restarted"
        ;;
    "logs")
        cd "$PROJECT_ROOT"
        docker-compose logs -f "${2:-}"
        ;;
    "status")
        cd "$PROJECT_ROOT"
        docker-compose ps
        ;;
    *)
        echo "Usage: $0 {deploy|update|stop|restart|logs [service]|status}"
        exit 1
        ;;
esac

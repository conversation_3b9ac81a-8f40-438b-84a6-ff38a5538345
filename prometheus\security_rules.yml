# TrustVault - Security Alert Rules

groups:
  - name: security.rules
    rules:
      # ================================================================
      # AUTHENTICATION SECURITY
      # ================================================================
      
      - alert: HighFailedLoginRate
        expr: rate(django_failed_logins_total[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
          category: authentication
        annotations:
          summary: "High failed login rate detected"
          description: "Failed login rate is {{ $value }} per second for the last 5 minutes"

      - alert: BruteForceAttack
        expr: rate(django_failed_logins_total[1m]) > 1
        for: 1m
        labels:
          severity: critical
          category: authentication
        annotations:
          summary: "Potential brute force attack detected"
          description: "Failed login rate is {{ $value }} per second, indicating a potential brute force attack"

      - alert: SuspiciousLoginPattern
        expr: increase(django_login_attempts_total[10m]) > 100
        for: 0m
        labels:
          severity: warning
          category: authentication
        annotations:
          summary: "Suspicious login pattern detected"
          description: "{{ $value }} login attempts in the last 10 minutes"

      # ================================================================
      # WEB APPLICATION SECURITY
      # ================================================================
      
      - alert: HighErrorRate
        expr: rate(nginx_http_requests_total{status=~"4..|5.."}[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
          category: web_security
        annotations:
          summary: "High HTTP error rate"
          description: "HTTP error rate is {{ $value }} per second"

      - alert: SQLInjectionAttempt
        expr: increase(wazuh_alerts_total{rule_id="100200"}[5m]) > 0
        for: 0m
        labels:
          severity: critical
          category: web_security
        annotations:
          summary: "SQL injection attempt detected"
          description: "{{ $value }} SQL injection attempts detected in the last 5 minutes"

      - alert: XSSAttempt
        expr: increase(wazuh_alerts_total{rule_id="100201"}[5m]) > 0
        for: 0m
        labels:
          severity: critical
          category: web_security
        annotations:
          summary: "XSS attempt detected"
          description: "{{ $value }} XSS attempts detected in the last 5 minutes"

      - alert: DirectoryTraversalAttempt
        expr: increase(wazuh_alerts_total{rule_id="100202"}[5m]) > 0
        for: 0m
        labels:
          severity: high
          category: web_security
        annotations:
          summary: "Directory traversal attempt detected"
          description: "{{ $value }} directory traversal attempts detected in the last 5 minutes"

      # ================================================================
      # NETWORK SECURITY
      # ================================================================
      
      - alert: DDoSAttack
        expr: rate(nginx_http_requests_total[1m]) > 100
        for: 2m
        labels:
          severity: critical
          category: network_security
        annotations:
          summary: "Potential DDoS attack"
          description: "Request rate is {{ $value }} per second, indicating a potential DDoS attack"

      - alert: PortScanDetected
        expr: increase(suricata_alerts_total{signature="Port Scan Detected"}[5m]) > 0
        for: 0m
        labels:
          severity: warning
          category: network_security
        annotations:
          summary: "Port scan detected"
          description: "{{ $value }} port scan attempts detected in the last 5 minutes"

      - alert: SuspiciousNetworkTraffic
        expr: rate(suricata_alerts_total[5m]) > 0.5
        for: 5m
        labels:
          severity: warning
          category: network_security
        annotations:
          summary: "High rate of network security alerts"
          description: "Network security alert rate is {{ $value }} per second"

      # ================================================================
      # DATA SECURITY
      # ================================================================
      
      - alert: UnauthorizedDataAccess
        expr: increase(django_unauthorized_access_total[5m]) > 0
        for: 0m
        labels:
          severity: high
          category: data_security
        annotations:
          summary: "Unauthorized data access attempt"
          description: "{{ $value }} unauthorized data access attempts in the last 5 minutes"

      - alert: LargeDataTransfer
        expr: rate(nginx_http_response_size_bytes[5m]) > 10000000
        for: 2m
        labels:
          severity: warning
          category: data_security
        annotations:
          summary: "Large data transfer detected"
          description: "Data transfer rate is {{ $value }} bytes per second"

      - alert: SuspiciousFileAccess
        expr: increase(wazuh_alerts_total{rule_id="100600"}[5m]) > 10
        for: 0m
        labels:
          severity: warning
          category: data_security
        annotations:
          summary: "Suspicious file access pattern"
          description: "{{ $value }} suspicious file access events in the last 5 minutes"

      # ================================================================
      # SYSTEM SECURITY
      # ================================================================
      
      - alert: PrivilegeEscalation
        expr: increase(wazuh_alerts_total{rule_id="100501"}[5m]) > 0
        for: 0m
        labels:
          severity: critical
          category: system_security
        annotations:
          summary: "Privilege escalation attempt detected"
          description: "{{ $value }} privilege escalation attempts detected"

      - alert: MalwareDetected
        expr: increase(wazuh_alerts_total{rule_id="100800"}[5m]) > 0
        for: 0m
        labels:
          severity: critical
          category: system_security
        annotations:
          summary: "Malware detected"
          description: "{{ $value }} malware detection events in the last 5 minutes"

      - alert: RootkitActivity
        expr: increase(wazuh_rootcheck_alerts_total[5m]) > 0
        for: 0m
        labels:
          severity: critical
          category: system_security
        annotations:
          summary: "Rootkit activity detected"
          description: "{{ $value }} rootkit-related alerts in the last 5 minutes"

      # ================================================================
      # INFRASTRUCTURE SECURITY
      # ================================================================
      
      - alert: ContainerSecurityViolation
        expr: increase(wazuh_alerts_total{rule_id="100500"}[5m]) > 0
        for: 0m
        labels:
          severity: high
          category: infrastructure_security
        annotations:
          summary: "Container security violation"
          description: "{{ $value }} container security violations detected"

      - alert: SSLCertificateExpiring
        expr: probe_ssl_earliest_cert_expiry - time() < 86400 * 7
        for: 0m
        labels:
          severity: warning
          category: infrastructure_security
        annotations:
          summary: "SSL certificate expiring soon"
          description: "SSL certificate for {{ $labels.instance }} expires in {{ $value | humanizeDuration }}"

      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
          category: infrastructure_security
        annotations:
          summary: "Service is down"
          description: "{{ $labels.job }} service is down"

      # ================================================================
      # COMPLIANCE ALERTS
      # ================================================================
      
      - alert: GDPRViolation
        expr: increase(wazuh_alerts_total{rule_id="100700"}[5m]) > 0
        for: 0m
        labels:
          severity: high
          category: compliance
        annotations:
          summary: "Potential GDPR violation detected"
          description: "{{ $value }} GDPR-related security events detected"

      - alert: AuditLogTampering
        expr: increase(wazuh_alerts_total{rule_id="100701"}[5m]) > 0
        for: 0m
        labels:
          severity: critical
          category: compliance
        annotations:
          summary: "Audit log tampering detected"
          description: "{{ $value }} audit log tampering attempts detected"

      # ================================================================
      # PERFORMANCE SECURITY
      # ================================================================
      
      - alert: HighCPUUsage
        expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
          category: performance_security
        annotations:
          summary: "High CPU usage"
          description: "CPU usage is {{ $value }}% on {{ $labels.instance }}"

      - alert: HighMemoryUsage
        expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100 > 80
        for: 5m
        labels:
          severity: warning
          category: performance_security
        annotations:
          summary: "High memory usage"
          description: "Memory usage is {{ $value }}% on {{ $labels.instance }}"

      - alert: DiskSpaceLow
        expr: (1 - (node_filesystem_avail_bytes / node_filesystem_size_bytes)) * 100 > 85
        for: 5m
        labels:
          severity: warning
          category: performance_security
        annotations:
          summary: "Low disk space"
          description: "Disk usage is {{ $value }}% on {{ $labels.instance }}"

      # ================================================================
      # THREAT INTELLIGENCE
      # ================================================================
      
      - alert: APTActivity
        expr: increase(wazuh_alerts_total{rule_id="100801"}[5m]) > 0
        for: 0m
        labels:
          severity: critical
          category: threat_intelligence
        annotations:
          summary: "Advanced Persistent Threat (APT) activity detected"
          description: "{{ $value }} APT indicators detected in the last 5 minutes"

      - alert: ThreatIntelMatch
        expr: increase(misp_ioc_matches_total[5m]) > 0
        for: 0m
        labels:
          severity: high
          category: threat_intelligence
        annotations:
          summary: "Threat intelligence match"
          description: "{{ $value }} threat intelligence indicators matched in the last 5 minutes"

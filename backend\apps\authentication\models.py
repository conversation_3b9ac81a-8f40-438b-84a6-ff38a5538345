# TrustVault - Authentication Models

import uuid
from django.contrib.auth.models import AbstractUser
from django.db import models
from django.utils import timezone
from django.core.validators import RegexValidator
from apps.core.models import BaseModel


class User(AbstractUser):
    """Custom User model with enhanced security features."""
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    email = models.EmailField(unique=True)
    phone_number = models.CharField(
        max_length=20,
        blank=True,
        validators=[RegexValidator(r'^\+?1?\d{9,15}$')]
    )
    
    # Security fields
    is_mfa_enabled = models.BooleanField(default=False)
    failed_login_attempts = models.PositiveIntegerField(default=0)
    account_locked_until = models.DateTimeField(null=True, blank=True)
    last_password_change = models.DateTimeField(default=timezone.now)
    password_reset_required = models.BooleanField(default=False)
    
    # Profile fields
    profile_picture = models.ImageField(upload_to='profiles/', blank=True, null=True)
    timezone = models.CharField(max_length=50, default='UTC')
    language = models.CharField(max_length=10, default='en')
    
    # Privacy and consent
    gdpr_consent = models.BooleanField(default=False)
    gdpr_consent_date = models.DateTimeField(null=True, blank=True)
    marketing_consent = models.BooleanField(default=False)
    
    # Audit fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    last_login_ip = models.GenericIPAddressField(null=True, blank=True)
    last_login_user_agent = models.TextField(blank=True)
    
    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['username', 'first_name', 'last_name']
    
    class Meta:
        db_table = 'auth_user'
        indexes = [
            models.Index(fields=['email']),
            models.Index(fields=['is_active', 'date_joined']),
            models.Index(fields=['last_login']),
        ]
    
    def __str__(self):
        return f"{self.email} ({self.get_full_name()})"
    
    @property
    def is_account_locked(self):
        """Check if account is currently locked."""
        if self.account_locked_until:
            return timezone.now() < self.account_locked_until
        return False
    
    def lock_account(self, duration_minutes=30):
        """Lock the account for specified duration."""
        self.account_locked_until = timezone.now() + timezone.timedelta(minutes=duration_minutes)
        self.save(update_fields=['account_locked_until'])
    
    def unlock_account(self):
        """Unlock the account."""
        self.account_locked_until = None
        self.failed_login_attempts = 0
        self.save(update_fields=['account_locked_until', 'failed_login_attempts'])
    
    def increment_failed_login(self):
        """Increment failed login attempts."""
        self.failed_login_attempts += 1
        if self.failed_login_attempts >= 5:
            self.lock_account()
        self.save(update_fields=['failed_login_attempts'])
    
    def reset_failed_login(self):
        """Reset failed login attempts."""
        self.failed_login_attempts = 0
        self.save(update_fields=['failed_login_attempts'])


class UserRole(BaseModel):
    """User roles for RBAC."""
    
    name = models.CharField(max_length=50, unique=True)
    description = models.TextField()
    permissions = models.JSONField(default=list)
    
    class Meta:
        db_table = 'auth_user_role'
    
    def __str__(self):
        return self.name


class UserRoleAssignment(BaseModel):
    """User role assignments."""
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='role_assignments')
    role = models.ForeignKey(UserRole, on_delete=models.CASCADE)
    assigned_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='assigned_roles')
    assigned_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        db_table = 'auth_user_role_assignment'
        unique_together = ['user', 'role']
    
    def __str__(self):
        return f"{self.user.email} - {self.role.name}"


class LoginAttempt(BaseModel):
    """Track login attempts for security monitoring."""
    
    ATTEMPT_TYPES = [
        ('SUCCESS', 'Successful Login'),
        ('FAILED_PASSWORD', 'Failed - Wrong Password'),
        ('FAILED_USER', 'Failed - User Not Found'),
        ('FAILED_LOCKED', 'Failed - Account Locked'),
        ('FAILED_MFA', 'Failed - MFA Required'),
        ('FAILED_DISABLED', 'Failed - Account Disabled'),
    ]
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True, related_name='login_attempts')
    email_attempted = models.EmailField()
    attempt_type = models.CharField(max_length=20, choices=ATTEMPT_TYPES)
    ip_address = models.GenericIPAddressField()
    user_agent = models.TextField()
    location = models.CharField(max_length=100, blank=True)
    is_suspicious = models.BooleanField(default=False)
    
    class Meta:
        db_table = 'auth_login_attempt'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'created_at']),
            models.Index(fields=['ip_address', 'created_at']),
            models.Index(fields=['attempt_type', 'created_at']),
            models.Index(fields=['is_suspicious', 'created_at']),
        ]
    
    def __str__(self):
        return f"{self.email_attempted} - {self.attempt_type} - {self.created_at}"


class UserSession(BaseModel):
    """Track user sessions for security."""
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='sessions')
    session_key = models.CharField(max_length=40, unique=True)
    ip_address = models.GenericIPAddressField()
    user_agent = models.TextField()
    location = models.CharField(max_length=100, blank=True)
    is_active = models.BooleanField(default=True)
    last_activity = models.DateTimeField(auto_now=True)
    expires_at = models.DateTimeField()
    
    class Meta:
        db_table = 'auth_user_session'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'is_active']),
            models.Index(fields=['session_key']),
            models.Index(fields=['expires_at']),
        ]
    
    def __str__(self):
        return f"{self.user.email} - {self.session_key[:8]}... - {self.created_at}"
    
    @property
    def is_expired(self):
        """Check if session is expired."""
        return timezone.now() > self.expires_at
    
    def terminate(self):
        """Terminate the session."""
        self.is_active = False
        self.save(update_fields=['is_active'])


class PasswordHistory(BaseModel):
    """Track password history to prevent reuse."""
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='password_history')
    password_hash = models.CharField(max_length=128)
    
    class Meta:
        db_table = 'auth_password_history'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'created_at']),
        ]
    
    def __str__(self):
        return f"{self.user.email} - {self.created_at}"


class UserSettings(BaseModel):
    """User preferences and settings."""

    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='settings')

    # Notification settings
    email_notifications = models.BooleanField(default=True)
    push_notifications = models.BooleanField(default=True)
    security_notifications = models.BooleanField(default=True)
    portfolio_notifications = models.BooleanField(default=True)
    marketing_notifications = models.BooleanField(default=False)
    news_notifications = models.BooleanField(default=True)
    email_frequency = models.CharField(
        max_length=20,
        choices=[
            ('immediate', 'Immediate'),
            ('daily', 'Daily'),
            ('weekly', 'Weekly'),
            ('never', 'Never'),
        ],
        default='daily'
    )

    # Privacy settings
    profile_visibility = models.CharField(
        max_length=10,
        choices=[('public', 'Public'), ('private', 'Private')],
        default='private'
    )
    portfolio_visibility = models.CharField(
        max_length=10,
        choices=[('public', 'Public'), ('private', 'Private')],
        default='private'
    )
    data_sharing = models.BooleanField(default=False)
    analytics_enabled = models.BooleanField(default=True)

    # Theme and UI
    theme_preference = models.CharField(
        max_length=10,
        choices=[('light', 'Light'), ('dark', 'Dark'), ('auto', 'Auto')],
        default='light'
    )

    class Meta:
        db_table = 'auth_user_settings'

    def __str__(self):
        return f"{self.user.email} - Settings"


class ApiKey(BaseModel):
    """API keys for third-party integrations."""

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='api_keys')
    name = models.CharField(max_length=100)
    key_hash = models.CharField(max_length=128, unique=True)
    key_prefix = models.CharField(max_length=8)  # First 8 chars for display
    permissions = models.JSONField(default=list)
    is_active = models.BooleanField(default=True)
    last_used = models.DateTimeField(null=True, blank=True)
    expires_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        db_table = 'auth_api_key'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'is_active']),
            models.Index(fields=['key_hash']),
            models.Index(fields=['expires_at']),
        ]

    def __str__(self):
        return f"{self.user.email} - {self.name}"

    @property
    def is_expired(self):
        """Check if API key is expired."""
        if self.expires_at:
            return timezone.now() > self.expires_at
        return False

    def revoke(self):
        """Revoke the API key."""
        self.is_active = False
        self.save(update_fields=['is_active'])

// TrustVault - Debug Page

import React from 'react';
import {
  Box,
  Typography,
  Tabs,
  Tab,
  Paper,
} from '@mui/material';
import { Helmet } from 'react-helmet-async';

// Components
import { AlertCreationChecker } from '../../components/Debug';
import NotificationTestPage from '../NotificationTest/NotificationTestPage';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`debug-tabpanel-${index}`}
      aria-labelledby={`debug-tab-${index}`}
      {...other}
    >
      {value === index && <Box>{children}</Box>}
    </div>
  );
}

const DebugPage: React.FC = () => {
  const [tabValue, setTabValue] = React.useState(0);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  return (
    <>
      <Helmet>
        <title>Debug Tools - TrustVault</title>
      </Helmet>

      <Box sx={{ p: 3 }}>
        {/* Header */}
        <Typography variant="h4" component="h1" gutterBottom>
          Debug Tools
        </Typography>
        <Typography variant="body1" color="text.secondary" paragraph>
          Development and debugging tools for TrustVault features.
        </Typography>

        {/* Tabs */}
        <Paper sx={{ mt: 3 }}>
          <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
            <Tabs value={tabValue} onChange={handleTabChange}>
              <Tab label="Alert Creation" />
              <Tab label="Notifications" />
            </Tabs>
          </Box>

          <TabPanel value={tabValue} index={0}>
            <AlertCreationChecker />
          </TabPanel>

          <TabPanel value={tabValue} index={1}>
            <NotificationTestPage />
          </TabPanel>
        </Paper>
      </Box>
    </>
  );
};

export default DebugPage;

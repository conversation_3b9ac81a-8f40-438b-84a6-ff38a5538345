#!/usr/bin/env python3

"""
Script de validation complète de l'infrastructure de sécurité TrustVault

Ce script exécute tous les tests de sécurité et génère un rapport complet.
"""

import os
import sys
import subprocess
import json
import time
from datetime import datetime
from pathlib import Path

def run_command(command, cwd=None, timeout=60):
    """Exécute une commande et retourne le résultat"""
    try:
        result = subprocess.run(
            command,
            shell=True,
            cwd=cwd,
            capture_output=True,
            text=True,
            timeout=timeout
        )
        return {
            'success': result.returncode == 0,
            'stdout': result.stdout,
            'stderr': result.stderr,
            'returncode': result.returncode
        }
    except subprocess.TimeoutExpired:
        return {
            'success': False,
            'stdout': '',
            'stderr': 'Command timed out',
            'returncode': -1
        }
    except Exception as e:
        return {
            'success': False,
            'stdout': '',
            'stderr': str(e),
            'returncode': -1
        }

def test_django_setup():
    """Test de la configuration Django"""
    print("🔧 Test de la configuration Django...")
    
    backend_dir = Path(__file__).parent / "backend"
    
    # Test Django check
    result = run_command("python manage.py check", cwd=backend_dir)
    
    if result['success']:
        print("  ✅ Configuration Django valide")
        return True
    else:
        print(f"  ❌ Erreur de configuration Django: {result['stderr']}")
        return False

def test_security_modules():
    """Test des modules de sécurité"""
    print("\n🛡️ Test des modules de sécurité...")
    
    backend_dir = Path(__file__).parent / "backend"
    
    modules_to_test = [
        ("Crypto", "from apps.security.advanced_crypto import crypto; print('✅ Crypto OK')"),
        ("Auth", "from apps.security.advanced_auth import auth_system; print('✅ Auth OK')"),
        ("IDS", "from apps.security.intrusion_detection import ids; print('✅ IDS OK')"),
        ("Compliance", "from apps.security.compliance import compliance_framework; print('✅ Compliance OK')"),
        ("Penetration Testing", "from apps.security.penetration_testing import penetration_testing_suite; print('✅ PenTest OK')")
    ]
    
    results = {}
    
    for module_name, test_code in modules_to_test:
        cmd = f'python -c "import os; os.environ.setdefault(\'DJANGO_SETTINGS_MODULE\', \'trustvault.settings\'); import django; django.setup(); {test_code}"'
        result = run_command(cmd, cwd=backend_dir)
        
        if result['success']:
            print(f"  ✅ Module {module_name}: OK")
            results[module_name] = True
        else:
            print(f"  ❌ Module {module_name}: ERREUR - {result['stderr']}")
            results[module_name] = False
    
    return results

def test_api_endpoints():
    """Test des endpoints API"""
    print("\n🌐 Test des endpoints API...")
    
    # Exécuter le script de test API
    result = run_command("python test_security_api.py")
    
    if result['success']:
        print("  ✅ Tests API réussis")
        
        # Lire les résultats détaillés
        try:
            with open('security_api_test_results.json', 'r') as f:
                api_results = json.load(f)
            return api_results
        except:
            return {'success': True, 'details': 'Results file not found'}
    else:
        print(f"  ❌ Tests API échoués: {result['stderr']}")
        return {'success': False, 'error': result['stderr']}

def test_database_migrations():
    """Test des migrations de base de données"""
    print("\n🗄️ Test des migrations de base de données...")
    
    backend_dir = Path(__file__).parent / "backend"
    
    # Test des migrations
    result = run_command("python manage.py showmigrations", cwd=backend_dir)
    
    if result['success']:
        print("  ✅ Migrations de base de données OK")
        return True
    else:
        print(f"  ❌ Erreur de migrations: {result['stderr']}")
        return False

def test_security_compliance():
    """Test de conformité sécuritaire"""
    print("\n📋 Test de conformité sécuritaire...")
    
    backend_dir = Path(__file__).parent / "backend"
    
    # Test de conformité ISO 27001
    cmd = '''python -c "
import os
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'trustvault.settings')
import django
django.setup()
from apps.security.compliance import compliance_framework
result = compliance_framework.assess_compliance('ISO_27001')
print(f'Score ISO 27001: {result[\"overall_score\"]}/100')
print(f'Statut: {result[\"compliance_status\"]}')
"'''
    
    result = run_command(cmd, cwd=backend_dir)
    
    if result['success']:
        print("  ✅ Test de conformité réussi")
        print(f"  📊 {result['stdout'].strip()}")
        return True
    else:
        print(f"  ❌ Test de conformité échoué: {result['stderr']}")
        return False

def test_docker_infrastructure():
    """Test de l'infrastructure Docker"""
    print("\n🐳 Test de l'infrastructure Docker...")
    
    # Vérifier si Docker est disponible
    docker_check = run_command("docker --version")
    
    if not docker_check['success']:
        print("  ⚠️ Docker non disponible - tests d'infrastructure ignorés")
        return False
    
    # Vérifier si docker-compose est disponible
    compose_check = run_command("docker-compose --version")
    
    if not compose_check['success']:
        print("  ⚠️ Docker Compose non disponible - tests d'infrastructure ignorés")
        return False
    
    print("  ✅ Docker et Docker Compose disponibles")
    print("  ℹ️ Infrastructure de sécurité prête pour le déploiement")
    return True

def generate_final_report(test_results):
    """Génère le rapport final de validation"""
    print("\n" + "="*80)
    print("📊 RAPPORT FINAL DE VALIDATION DE SÉCURITÉ TRUSTVAULT")
    print("="*80)
    
    total_tests = 0
    passed_tests = 0
    
    # Analyser les résultats
    for category, result in test_results.items():
        if isinstance(result, bool):
            total_tests += 1
            if result:
                passed_tests += 1
        elif isinstance(result, dict):
            if 'success' in result:
                total_tests += 1
                if result['success']:
                    passed_tests += 1
            elif 'report' in result:
                # Résultats API détaillés
                api_report = result['report']
                total_tests += 1
                if api_report.get('success_rate', 0) >= 80:
                    passed_tests += 1
    
    success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
    
    print(f"📈 Taux de réussite global: {success_rate:.1f}% ({passed_tests}/{total_tests})")
    print(f"⏰ Date de validation: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    print(f"\n📋 Détail des tests:")
    for category, result in test_results.items():
        if isinstance(result, bool):
            status = "✅" if result else "❌"
            print(f"  {status} {category}")
        elif isinstance(result, dict):
            if 'success' in result:
                status = "✅" if result['success'] else "❌"
                print(f"  {status} {category}")
            elif 'report' in result:
                api_rate = result['report'].get('success_rate', 0)
                status = "✅" if api_rate >= 80 else "⚠️" if api_rate >= 60 else "❌"
                print(f"  {status} {category} ({api_rate:.1f}%)")
    
    # Recommandations
    print(f"\n🎯 Recommandations:")
    if success_rate >= 90:
        print("  🎉 Infrastructure de sécurité excellente!")
        print("  🚀 Prête pour la production")
    elif success_rate >= 80:
        print("  ✅ Infrastructure de sécurité solide")
        print("  🔧 Quelques améliorations mineures recommandées")
    elif success_rate >= 60:
        print("  ⚠️ Infrastructure de sécurité acceptable")
        print("  🛠️ Améliorations importantes recommandées")
    else:
        print("  ❌ Infrastructure de sécurité insuffisante")
        print("  🚨 Corrections critiques requises")
    
    # Sauvegarder le rapport
    report_data = {
        'validation_date': datetime.now().isoformat(),
        'overall_success_rate': success_rate,
        'total_tests': total_tests,
        'passed_tests': passed_tests,
        'test_results': test_results,
        'status': 'PASS' if success_rate >= 80 else 'FAIL'
    }
    
    with open('security_validation_report.json', 'w') as f:
        json.dump(report_data, f, indent=2, default=str)
    
    print(f"\n💾 Rapport complet sauvegardé: security_validation_report.json")
    
    return success_rate >= 80

def main():
    """Fonction principale"""
    print("🚀 VALIDATION COMPLÈTE DE L'INFRASTRUCTURE DE SÉCURITÉ TRUSTVAULT")
    print("="*80)
    print("🛡️ Tests de sécurité avancés en cours...")
    print()
    
    test_results = {}
    
    # Exécuter tous les tests
    test_results['Django Configuration'] = test_django_setup()
    test_results['Security Modules'] = test_security_modules()
    test_results['Database Migrations'] = test_database_migrations()
    test_results['API Endpoints'] = test_api_endpoints()
    test_results['Security Compliance'] = test_security_compliance()
    test_results['Docker Infrastructure'] = test_docker_infrastructure()
    
    # Générer le rapport final
    success = generate_final_report(test_results)
    
    if success:
        print("\n🎉 VALIDATION RÉUSSIE - Infrastructure de sécurité opérationnelle!")
        return 0
    else:
        print("\n❌ VALIDATION ÉCHOUÉE - Corrections requises")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)

"""
TrustVault - Advanced Cryptographic Security Module

This module implements enterprise-grade cryptographic security measures including:
- AES-256-GCM encryption with key rotation
- RSA-4096 asymmetric encryption
- PBKDF2 key derivation with high iterations
- Digital signatures and integrity verification
- Hardware Security Module (HSM) integration ready
"""

import os
import secrets
import hashlib
import hmac
import base64
from typing import Tuple, Optional, Dict, Any
from datetime import datetime, timedelta
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.primitives import hashes, serialization, padding
from cryptography.hazmat.primitives.asymmetric import rsa, padding as asym_padding
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.backends import default_backend
from cryptography.exceptions import InvalidSignature
from django.conf import settings
from django.core.cache import cache
import logging

logger = logging.getLogger(__name__)

class AdvancedCrypto:
    """Advanced cryptographic operations for TrustVault"""
    
    # Security constants
    AES_KEY_SIZE = 32  # 256 bits
    RSA_KEY_SIZE = 4096
    PBKDF2_ITERATIONS = 600000  # OWASP recommended minimum
    SALT_SIZE = 32
    IV_SIZE = 12  # For GCM mode
    TAG_SIZE = 16  # GCM authentication tag
    
    def __init__(self):
        self.backend = default_backend()
        self._master_key = self._get_or_create_master_key()
        
    def _get_or_create_master_key(self) -> bytes:
        """Get or create the master encryption key"""
        master_key = getattr(settings, 'MASTER_ENCRYPTION_KEY', None)
        if not master_key:
            # Generate a new master key (should be stored securely in production)
            master_key = base64.b64encode(secrets.token_bytes(self.AES_KEY_SIZE)).decode()
            logger.warning("Generated new master key - store this securely!")
        
        return base64.b64decode(master_key.encode())
    
    def generate_key_pair(self) -> Tuple[bytes, bytes]:
        """Generate RSA-4096 key pair for asymmetric encryption"""
        private_key = rsa.generate_private_key(
            public_exponent=65537,
            key_size=self.RSA_KEY_SIZE,
            backend=self.backend
        )
        
        private_pem = private_key.private_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PrivateFormat.PKCS8,
            encryption_algorithm=serialization.NoEncryption()
        )
        
        public_key = private_key.public_key()
        public_pem = public_key.public_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PublicFormat.SubjectPublicKeyInfo
        )
        
        return private_pem, public_pem
    
    def derive_key(self, password: str, salt: bytes = None) -> Tuple[bytes, bytes]:
        """Derive encryption key from password using PBKDF2"""
        if salt is None:
            salt = secrets.token_bytes(self.SALT_SIZE)
        
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=self.AES_KEY_SIZE,
            salt=salt,
            iterations=self.PBKDF2_ITERATIONS,
            backend=self.backend
        )
        
        key = kdf.derive(password.encode())
        return key, salt
    
    def encrypt_data(self, data: bytes, additional_data: bytes = None) -> Dict[str, str]:
        """
        Encrypt data using AES-256-GCM with authenticated encryption
        Returns base64-encoded encrypted data with metadata
        """
        # Generate random IV for each encryption
        iv = secrets.token_bytes(self.IV_SIZE)
        
        # Create cipher
        cipher = Cipher(
            algorithms.AES(self._master_key),
            modes.GCM(iv),
            backend=self.backend
        )
        encryptor = cipher.encryptor()
        
        # Add additional authenticated data if provided
        if additional_data:
            encryptor.authenticate_additional_data(additional_data)
        
        # Encrypt the data
        ciphertext = encryptor.update(data) + encryptor.finalize()
        
        # Get authentication tag
        tag = encryptor.tag
        
        return {
            'ciphertext': base64.b64encode(ciphertext).decode(),
            'iv': base64.b64encode(iv).decode(),
            'tag': base64.b64encode(tag).decode(),
            'algorithm': 'AES-256-GCM',
            'timestamp': datetime.utcnow().isoformat()
        }
    
    def decrypt_data(self, encrypted_data: Dict[str, str], additional_data: bytes = None) -> bytes:
        """Decrypt AES-256-GCM encrypted data"""
        try:
            ciphertext = base64.b64decode(encrypted_data['ciphertext'])
            iv = base64.b64decode(encrypted_data['iv'])
            tag = base64.b64decode(encrypted_data['tag'])
            
            # Create cipher
            cipher = Cipher(
                algorithms.AES(self._master_key),
                modes.GCM(iv, tag),
                backend=self.backend
            )
            decryptor = cipher.decryptor()
            
            # Add additional authenticated data if provided
            if additional_data:
                decryptor.authenticate_additional_data(additional_data)
            
            # Decrypt the data
            plaintext = decryptor.update(ciphertext) + decryptor.finalize()
            
            return plaintext
            
        except Exception as e:
            logger.error(f"Decryption failed: {e}")
            raise ValueError("Decryption failed - data may be corrupted or tampered with")
    
    def encrypt_asymmetric(self, data: bytes, public_key_pem: bytes) -> str:
        """Encrypt data using RSA-4096 public key"""
        public_key = serialization.load_pem_public_key(public_key_pem, backend=self.backend)
        
        # RSA can only encrypt small amounts of data, so we use hybrid encryption
        # Generate a random AES key for the actual data encryption
        aes_key = secrets.token_bytes(self.AES_KEY_SIZE)
        
        # Encrypt the data with AES
        encrypted_data = self.encrypt_data(data)
        
        # Encrypt the AES key with RSA
        encrypted_aes_key = public_key.encrypt(
            aes_key,
            asym_padding.OAEP(
                mgf=asym_padding.MGF1(algorithm=hashes.SHA256()),
                algorithm=hashes.SHA256(),
                label=None
            )
        )
        
        # Combine encrypted key and data
        result = {
            'encrypted_key': base64.b64encode(encrypted_aes_key).decode(),
            'encrypted_data': encrypted_data
        }
        
        return base64.b64encode(str(result).encode()).decode()
    
    def create_digital_signature(self, data: bytes, private_key_pem: bytes) -> str:
        """Create digital signature for data integrity verification"""
        private_key = serialization.load_pem_private_key(
            private_key_pem, 
            password=None, 
            backend=self.backend
        )
        
        signature = private_key.sign(
            data,
            asym_padding.PSS(
                mgf=asym_padding.MGF1(hashes.SHA256()),
                salt_length=asym_padding.PSS.MAX_LENGTH
            ),
            hashes.SHA256()
        )
        
        return base64.b64encode(signature).decode()
    
    def verify_digital_signature(self, data: bytes, signature: str, public_key_pem: bytes) -> bool:
        """Verify digital signature"""
        try:
            public_key = serialization.load_pem_public_key(public_key_pem, backend=self.backend)
            signature_bytes = base64.b64decode(signature)
            
            public_key.verify(
                signature_bytes,
                data,
                asym_padding.PSS(
                    mgf=asym_padding.MGF1(hashes.SHA256()),
                    salt_length=asym_padding.PSS.MAX_LENGTH
                ),
                hashes.SHA256()
            )
            return True
            
        except InvalidSignature:
            return False
        except Exception as e:
            logger.error(f"Signature verification error: {e}")
            return False
    
    def generate_secure_token(self, length: int = 32) -> str:
        """Generate cryptographically secure random token"""
        return secrets.token_urlsafe(length)
    
    def secure_hash(self, data: str, salt: str = None) -> Tuple[str, str]:
        """Create secure hash with salt"""
        if salt is None:
            salt = secrets.token_hex(16)
        
        # Use PBKDF2 for password hashing
        key, _ = self.derive_key(data, salt.encode())
        hash_value = base64.b64encode(key).decode()
        
        return hash_value, salt
    
    def constant_time_compare(self, a: str, b: str) -> bool:
        """Constant-time string comparison to prevent timing attacks"""
        return hmac.compare_digest(a, b)
    
    def rotate_encryption_key(self) -> str:
        """Generate new encryption key for key rotation"""
        new_key = secrets.token_bytes(self.AES_KEY_SIZE)
        new_key_b64 = base64.b64encode(new_key).decode()
        
        # In production, this should trigger re-encryption of all data
        logger.info("Encryption key rotation initiated")
        
        return new_key_b64


# Global instance
crypto = AdvancedCrypto()


class SecureDataField:
    """Custom field for encrypting sensitive data in database"""
    
    def __init__(self, field_name: str):
        self.field_name = field_name
    
    def encrypt_field(self, value: str, user_id: str = None) -> str:
        """Encrypt field value with optional user context"""
        if not value:
            return value
        
        additional_data = f"{self.field_name}:{user_id}".encode() if user_id else None
        encrypted = crypto.encrypt_data(value.encode(), additional_data)
        
        return base64.b64encode(str(encrypted).encode()).decode()
    
    def decrypt_field(self, encrypted_value: str, user_id: str = None) -> str:
        """Decrypt field value"""
        if not encrypted_value:
            return encrypted_value
        
        try:
            encrypted_dict = eval(base64.b64decode(encrypted_value).decode())
            additional_data = f"{self.field_name}:{user_id}".encode() if user_id else None
            
            decrypted = crypto.decrypt_data(encrypted_dict, additional_data)
            return decrypted.decode()
            
        except Exception as e:
            logger.error(f"Field decryption failed for {self.field_name}: {e}")
            raise ValueError("Failed to decrypt sensitive data")


# Security utilities
def generate_api_key() -> str:
    """Generate secure API key"""
    return f"tv_{crypto.generate_secure_token(32)}"

def validate_password_strength(password: str) -> Dict[str, Any]:
    """Validate password strength according to security policies"""
    issues = []
    score = 0
    
    if len(password) >= 12:
        score += 2
    elif len(password) >= 8:
        score += 1
    else:
        issues.append("Password must be at least 8 characters long")
    
    if any(c.isupper() for c in password):
        score += 1
    else:
        issues.append("Password must contain uppercase letters")
    
    if any(c.islower() for c in password):
        score += 1
    else:
        issues.append("Password must contain lowercase letters")
    
    if any(c.isdigit() for c in password):
        score += 1
    else:
        issues.append("Password must contain numbers")
    
    if any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password):
        score += 2
    else:
        issues.append("Password must contain special characters")
    
    # Check for common patterns
    common_patterns = ['123', 'abc', 'password', 'admin', 'user']
    if any(pattern in password.lower() for pattern in common_patterns):
        issues.append("Password contains common patterns")
        score -= 1
    
    strength_levels = {
        0: "Very Weak",
        1: "Weak", 
        2: "Fair",
        3: "Good",
        4: "Strong",
        5: "Very Strong"
    }
    
    return {
        'score': max(0, min(5, score)),
        'strength': strength_levels.get(max(0, min(5, score)), "Unknown"),
        'issues': issues,
        'is_valid': len(issues) == 0 and score >= 3
    }

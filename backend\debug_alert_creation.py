#!/usr/bin/env python
"""
Debug Alert Creation - Comprehensive Test

This script will test alert creation step by step to identify the exact issue.
"""

import os
import sys
import django
import json
from decimal import Decimal

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'trustvault.settings')
django.setup()

from django.test import Client
from django.contrib.auth import get_user_model
from apps.portfolio.models import Portfolio, Asset
from apps.alerts.models import PriceAlert
from apps.alerts.serializers import PriceAlertSerializer

User = get_user_model()


def test_serializer_directly():
    """Test the serializer directly without HTTP."""
    print("🔍 Testing PriceAlertSerializer Directly")
    print("=" * 40)
    
    # Get or create test user
    user, created = User.objects.get_or_create(
        username='testuser',
        email='<EMAIL>',
        defaults={'first_name': 'Test', 'last_name': 'User'}
    )
    
    # Create test asset
    asset, created = Asset.objects.get_or_create(
        symbol='AAPL',
        defaults={
            'name': 'Apple Inc.',
            'asset_type': 'STOCK',
            'current_price': Decimal('150.00'),
            'currency': 'USD'
        }
    )
    
    print(f"✅ Test user: {user.email}")
    print(f"✅ Test asset: {asset.symbol} - {asset.name}")
    
    # Test 1: Valid data with asset only
    print("\n1. Testing valid data with asset only...")
    valid_data = {
        'name': 'Test AAPL Alert',
        'alert_type': 'PRICE_ABOVE',
        'asset': str(asset.id),
        'threshold_value': 160.00,
        'comparison_operator': 'GT',
        'notification_channels': ['EMAIL'],
        'max_triggers': 3,
        'cooldown_minutes': 30
    }
    
    print(f"   📝 Data: {json.dumps(valid_data, indent=2, default=str)}")
    
    class MockRequest:
        def __init__(self, user):
            self.user = user
    
    serializer = PriceAlertSerializer(data=valid_data, context={'request': MockRequest(user)})
    
    if serializer.is_valid():
        alert = serializer.save()
        print(f"   ✅ SUCCESS: Alert created with ID {alert.id}")
        print(f"   📝 Alert: {alert.name} - {alert.alert_type}")
        return True
    else:
        print(f"   ❌ VALIDATION FAILED: {serializer.errors}")
        return False


def test_http_request():
    """Test via HTTP request."""
    print("\n🌐 Testing via HTTP Request")
    print("=" * 30)
    
    client = Client()
    
    # Get test user
    user = User.objects.filter(email='<EMAIL>').first()
    if not user:
        print("❌ Test user not found")
        return False
    
    # Login user
    client.force_login(user)
    print(f"✅ Logged in as: {user.email}")
    
    # Get test asset
    asset = Asset.objects.filter(symbol='AAPL').first()
    if not asset:
        print("❌ Test asset not found")
        return False
    
    # Test data
    alert_data = {
        'name': 'HTTP Test Alert',
        'alert_type': 'PRICE_ABOVE',
        'asset': str(asset.id),
        'threshold_value': 165.00,
        'comparison_operator': 'GT',
        'notification_channels': ['EMAIL'],
        'max_triggers': 3,
        'cooldown_minutes': 30
    }
    
    print(f"📝 Sending data: {json.dumps(alert_data, indent=2, default=str)}")
    
    # Make request
    response = client.post(
        '/api/v1/alerts/alerts/',
        data=json.dumps(alert_data),
        content_type='application/json'
    )
    
    print(f"📊 Response status: {response.status_code}")
    print(f"📊 Response headers: {dict(response.items())}")
    
    if response.status_code == 201:
        result = response.json()
        print(f"✅ SUCCESS: Alert created")
        print(f"📝 Response: {json.dumps(result, indent=2, default=str)}")
        return True
    else:
        print(f"❌ FAILED: {response.status_code}")
        try:
            error_data = response.json()
            print(f"📝 Error response: {json.dumps(error_data, indent=2, default=str)}")
        except:
            print(f"📝 Raw response: {response.content.decode()}")
        return False


def test_edge_cases():
    """Test various edge cases."""
    print("\n🧪 Testing Edge Cases")
    print("=" * 20)
    
    user = User.objects.filter(email='<EMAIL>').first()
    asset = Asset.objects.filter(symbol='AAPL').first()
    
    # Test cases
    test_cases = [
        {
            'name': 'Empty asset and portfolio',
            'data': {
                'name': 'Test Alert',
                'alert_type': 'PRICE_ABOVE',
                'threshold_value': 160.00,
                'comparison_operator': 'GT',
                'notification_channels': ['EMAIL']
            },
            'should_fail': True
        },
        {
            'name': 'Both asset and portfolio',
            'data': {
                'name': 'Test Alert',
                'alert_type': 'PRICE_ABOVE',
                'asset': str(asset.id),
                'portfolio': 'some-portfolio-id',
                'threshold_value': 160.00,
                'comparison_operator': 'GT',
                'notification_channels': ['EMAIL']
            },
            'should_fail': True
        },
        {
            'name': 'Portfolio alert without portfolio',
            'data': {
                'name': 'Test Alert',
                'alert_type': 'PORTFOLIO_VALUE',
                'asset': str(asset.id),
                'threshold_value': 10000.00,
                'comparison_operator': 'GT',
                'notification_channels': ['EMAIL']
            },
            'should_fail': True
        },
        {
            'name': 'Asset alert without asset',
            'data': {
                'name': 'Test Alert',
                'alert_type': 'PRICE_ABOVE',
                'portfolio': 'some-portfolio-id',
                'threshold_value': 160.00,
                'comparison_operator': 'GT',
                'notification_channels': ['EMAIL']
            },
            'should_fail': True
        }
    ]
    
    class MockRequest:
        def __init__(self, user):
            self.user = user
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. {test_case['name']}...")
        
        serializer = PriceAlertSerializer(data=test_case['data'], context={'request': MockRequest(user)})
        
        if serializer.is_valid():
            if test_case['should_fail']:
                print(f"   ❌ UNEXPECTED SUCCESS (should have failed)")
            else:
                print(f"   ✅ SUCCESS (as expected)")
        else:
            if test_case['should_fail']:
                print(f"   ✅ FAILED (as expected): {serializer.errors}")
            else:
                print(f"   ❌ UNEXPECTED FAILURE: {serializer.errors}")


def check_database_state():
    """Check current database state."""
    print("\n📊 Database State Check")
    print("=" * 25)
    
    users_count = User.objects.count()
    assets_count = Asset.objects.count()
    portfolios_count = Portfolio.objects.count()
    alerts_count = PriceAlert.objects.count()
    
    print(f"👥 Users: {users_count}")
    print(f"📈 Assets: {assets_count}")
    print(f"💼 Portfolios: {portfolios_count}")
    print(f"🚨 Alerts: {alerts_count}")
    
    # Show sample data
    if assets_count > 0:
        print("\n📈 Sample Assets:")
        for asset in Asset.objects.all()[:3]:
            print(f"   - {asset.symbol}: {asset.name} (ID: {asset.id})")
    
    if portfolios_count > 0:
        print("\n💼 Sample Portfolios:")
        for portfolio in Portfolio.objects.all()[:3]:
            print(f"   - {portfolio.name} (ID: {portfolio.id}, User: {portfolio.user.email})")
    
    if alerts_count > 0:
        print("\n🚨 Sample Alerts:")
        for alert in PriceAlert.objects.all()[:3]:
            target = alert.asset.symbol if alert.asset else alert.portfolio.name if alert.portfolio else 'None'
            print(f"   - {alert.name}: {alert.alert_type} on {target}")


if __name__ == '__main__':
    try:
        print("🚀 Debugging Alert Creation Issue")
        print("=" * 50)
        
        # Check database state first
        check_database_state()
        
        # Test serializer directly
        serializer_ok = test_serializer_directly()
        
        # Test HTTP request
        http_ok = test_http_request()
        
        # Test edge cases
        test_edge_cases()
        
        print("\n" + "=" * 50)
        print("📋 SUMMARY:")
        print(f"   Serializer test: {'✅ PASS' if serializer_ok else '❌ FAIL'}")
        print(f"   HTTP test: {'✅ PASS' if http_ok else '❌ FAIL'}")
        
        if serializer_ok and not http_ok:
            print("\n🔍 DIAGNOSIS: Serializer works but HTTP fails")
            print("   This suggests an issue with the HTTP request handling")
            print("   Check: request data format, content-type, authentication")
        elif not serializer_ok:
            print("\n🔍 DIAGNOSIS: Serializer validation fails")
            print("   This suggests an issue with the data or validation logic")
        else:
            print("\n🎉 All tests passed!")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

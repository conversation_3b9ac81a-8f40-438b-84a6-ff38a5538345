#!/usr/bin/env python
"""
TrustVault - Attack Simulation Suite
Simulate various cyber attacks to test security defenses
"""

import os
import sys
import django
import requests
import time
import threading
import random
import string
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'trustvault.settings')
django.setup()

class AttackSimulator:
    def __init__(self):
        self.target_url = "http://localhost:8000"
        self.results = []
        self.attack_patterns = {
            'brute_force': [],
            'sql_injection': [],
            'xss': [],
            'ddos': [],
            'directory_traversal': [],
            'csrf': []
        }
        
    def log_attack(self, attack_type, success, details):
        """Log attack attempt result."""
        result = {
            'attack_type': attack_type,
            'success': success,
            'details': details,
            'timestamp': datetime.now().isoformat()
        }
        self.results.append(result)
        self.attack_patterns[attack_type].append(result)
        
        status_icon = "🔴" if success else "🟢"
        print(f"{status_icon} {attack_type.upper()}: {details}")

    def simulate_brute_force_attack(self):
        """Simulate brute force login attack."""
        print("\n💥 Simulating Brute Force Attack")
        print("=" * 40)
        
        common_passwords = [
            'password', '123456', 'password123', 'admin', 'qwerty',
            'letmein', 'welcome', 'monkey', '1234567890', 'password1'
        ]
        
        target_emails = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'
        ]
        
        session = requests.Session()
        successful_attacks = 0
        blocked_attempts = 0
        
        for email in target_emails:
            for password in common_passwords:
                try:
                    response = session.post(f"{self.target_url}/api/v1/auth/login/", 
                        json={'email': email, 'password': password},
                        timeout=3
                    )
                    
                    if response.status_code == 200:
                        successful_attacks += 1
                        self.log_attack('brute_force', True, 
                            f"Successful login: {email}:{password}")
                        break  # Stop trying passwords for this email
                    elif response.status_code == 429:
                        blocked_attempts += 1
                        self.log_attack('brute_force', False, 
                            f"Rate limited after {blocked_attempts} attempts")
                        break
                    else:
                        self.log_attack('brute_force', False, 
                            f"Failed login: {email}:{password}")
                        
                except requests.exceptions.RequestException:
                    blocked_attempts += 1
                    self.log_attack('brute_force', False, 
                        "Connection blocked/refused")
                
                time.sleep(0.1)  # Small delay between attempts
        
        print(f"   📊 Results: {successful_attacks} successful, {blocked_attempts} blocked")
        return successful_attacks, blocked_attempts

    def simulate_sql_injection_attack(self):
        """Simulate SQL injection attacks."""
        print("\n💉 Simulating SQL Injection Attack")
        print("=" * 40)
        
        sql_payloads = [
            "' OR '1'='1' --",
            "'; DROP TABLE users; --",
            "' UNION SELECT username, password FROM users --",
            "admin'/*",
            "' OR 1=1#",
            "'; INSERT INTO users VALUES ('hacker', 'password'); --",
            "' OR 'x'='x",
            "1' AND (SELECT COUNT(*) FROM users) > 0 --"
        ]
        
        session = requests.Session()
        
        # Try to login first
        try:
            session.post(f"{self.target_url}/api/v1/auth/login/", 
                json={'email': '<EMAIL>', 'password': 'password123'})
        except:
            pass
        
        successful_attacks = 0
        blocked_attempts = 0
        
        for payload in sql_payloads:
            try:
                # Test on search endpoints
                endpoints = [
                    f"/api/v1/portfolio/?search={payload}",
                    f"/api/v1/alerts/alerts/?search={payload}",
                ]
                
                for endpoint in endpoints:
                    response = session.get(f"{self.target_url}{endpoint}", timeout=3)
                    
                    if response.status_code == 200:
                        # Check if we got unexpected data (potential SQL injection success)
                        response_text = response.text.lower()
                        if any(keyword in response_text for keyword in ['error', 'sql', 'syntax']):
                            successful_attacks += 1
                            self.log_attack('sql_injection', True, 
                                f"Potential SQL error exposed: {payload[:20]}...")
                        else:
                            self.log_attack('sql_injection', False, 
                                f"Payload handled safely: {payload[:20]}...")
                    elif response.status_code in [400, 403, 422]:
                        blocked_attempts += 1
                        self.log_attack('sql_injection', False, 
                            f"Payload blocked: {payload[:20]}...")
                    elif response.status_code == 500:
                        successful_attacks += 1
                        self.log_attack('sql_injection', True, 
                            f"Server error (potential injection): {payload[:20]}...")
                        
            except requests.exceptions.RequestException:
                blocked_attempts += 1
                self.log_attack('sql_injection', False, "Connection blocked")
            
            time.sleep(0.1)
        
        print(f"   📊 Results: {successful_attacks} potential successes, {blocked_attempts} blocked")
        return successful_attacks, blocked_attempts

    def simulate_xss_attack(self):
        """Simulate XSS attacks."""
        print("\n🕷️ Simulating XSS Attack")
        print("=" * 35)
        
        xss_payloads = [
            "<script>alert('XSS')</script>",
            "<img src=x onerror=alert('XSS')>",
            "<svg onload=alert('XSS')>",
            "javascript:alert('XSS')",
            "<iframe src=javascript:alert('XSS')></iframe>",
            "<body onload=alert('XSS')>",
            "<input onfocus=alert('XSS') autofocus>",
            "<select onfocus=alert('XSS') autofocus>"
        ]
        
        session = requests.Session()
        
        # Login first
        try:
            session.post(f"{self.target_url}/api/v1/auth/login/", 
                json={'email': '<EMAIL>', 'password': 'password123'})
        except:
            pass
        
        successful_attacks = 0
        blocked_attempts = 0
        
        for payload in xss_payloads:
            try:
                # Try to inject XSS in profile fields
                response = session.patch(f"{self.target_url}/api/v1/auth/profile/", 
                    json={'first_name': payload}, timeout=3)
                
                if response.status_code == 200:
                    # Check if payload was stored without sanitization
                    profile_response = session.get(f"{self.target_url}/api/v1/auth/profile/")
                    if profile_response.status_code == 200:
                        profile_data = profile_response.text
                        if payload in profile_data:
                            successful_attacks += 1
                            self.log_attack('xss', True, 
                                f"XSS payload stored: {payload[:30]}...")
                        else:
                            self.log_attack('xss', False, 
                                f"XSS payload sanitized: {payload[:30]}...")
                elif response.status_code in [400, 403, 422]:
                    blocked_attempts += 1
                    self.log_attack('xss', False, 
                        f"XSS payload blocked: {payload[:30]}...")
                        
            except requests.exceptions.RequestException:
                blocked_attempts += 1
                self.log_attack('xss', False, "Connection blocked")
            
            time.sleep(0.1)
        
        print(f"   📊 Results: {successful_attacks} potential successes, {blocked_attempts} blocked")
        return successful_attacks, blocked_attempts

    def simulate_ddos_attack(self):
        """Simulate DDoS attack."""
        print("\n🌊 Simulating DDoS Attack")
        print("=" * 30)
        
        def attack_worker():
            """Worker function for DDoS simulation."""
            session = requests.Session()
            requests_made = 0
            blocked_count = 0
            
            for _ in range(20):  # Each worker makes 20 requests
                try:
                    response = session.get(f"{self.target_url}/api/v1/alerts/alerts/choices/", 
                        timeout=2)
                    requests_made += 1
                    
                    if response.status_code == 429:
                        blocked_count += 1
                        
                except requests.exceptions.RequestException:
                    blocked_count += 1
                
                time.sleep(0.01)  # Very fast requests
            
            return requests_made, blocked_count
        
        # Launch multiple workers simultaneously
        with ThreadPoolExecutor(max_workers=20) as executor:
            futures = [executor.submit(attack_worker) for _ in range(20)]
            results = [future.result() for future in futures]
        
        total_requests = sum(r[0] for r in results)
        total_blocked = sum(r[1] for r in results)
        
        if total_blocked > total_requests * 0.3:  # More than 30% blocked
            self.log_attack('ddos', False, 
                f"DDoS mitigated: {total_blocked}/{total_requests} requests blocked")
        else:
            self.log_attack('ddos', True, 
                f"DDoS successful: Only {total_blocked}/{total_requests} requests blocked")
        
        print(f"   📊 Results: {total_requests} requests sent, {total_blocked} blocked")
        return total_requests - total_blocked, total_blocked

    def simulate_directory_traversal_attack(self):
        """Simulate directory traversal attacks."""
        print("\n📁 Simulating Directory Traversal Attack")
        print("=" * 45)
        
        traversal_payloads = [
            "../../../etc/passwd",
            "..\\..\\..\\windows\\system32\\drivers\\etc\\hosts",
            "....//....//....//etc/passwd",
            "%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd",
            "..%252f..%252f..%252fetc%252fpasswd",
            "..%c0%af..%c0%af..%c0%afetc%c0%afpasswd"
        ]
        
        session = requests.Session()
        successful_attacks = 0
        blocked_attempts = 0
        
        for payload in traversal_payloads:
            try:
                # Try different endpoints that might serve files
                endpoints = [
                    f"/static/{payload}",
                    f"/media/{payload}",
                    f"/api/v1/files/{payload}",
                ]
                
                for endpoint in endpoints:
                    response = session.get(f"{self.target_url}{endpoint}", timeout=3)
                    
                    if response.status_code == 200:
                        response_text = response.text.lower()
                        if any(keyword in response_text for keyword in ['root:', 'bin:', 'daemon:']):
                            successful_attacks += 1
                            self.log_attack('directory_traversal', True, 
                                f"File access successful: {payload[:30]}...")
                        else:
                            self.log_attack('directory_traversal', False, 
                                f"No sensitive data exposed: {payload[:30]}...")
                    elif response.status_code in [403, 404]:
                        blocked_attempts += 1
                        self.log_attack('directory_traversal', False, 
                            f"Access denied: {payload[:30]}...")
                        
            except requests.exceptions.RequestException:
                blocked_attempts += 1
                self.log_attack('directory_traversal', False, "Connection blocked")
            
            time.sleep(0.1)
        
        print(f"   📊 Results: {successful_attacks} potential successes, {blocked_attempts} blocked")
        return successful_attacks, blocked_attempts

    def run_attack_simulation(self):
        """Run complete attack simulation."""
        print("🚀 TrustVault Attack Simulation Suite")
        print("=" * 50)
        print(f"Target: {self.target_url}")
        print(f"Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("⚠️  WARNING: This is a controlled security test")
        print("=" * 50)
        
        # Run all attack simulations
        attack_results = {}
        
        attack_results['brute_force'] = self.simulate_brute_force_attack()
        attack_results['sql_injection'] = self.simulate_sql_injection_attack()
        attack_results['xss'] = self.simulate_xss_attack()
        attack_results['ddos'] = self.simulate_ddos_attack()
        attack_results['directory_traversal'] = self.simulate_directory_traversal_attack()
        
        # Calculate overall security score
        total_successful = sum(result[0] for result in attack_results.values())
        total_blocked = sum(result[1] for result in attack_results.values())
        total_attempts = total_successful + total_blocked
        
        if total_attempts > 0:
            security_score = (total_blocked / total_attempts) * 100
        else:
            security_score = 100
        
        # Summary
        print("\n" + "=" * 50)
        print("📊 ATTACK SIMULATION SUMMARY")
        print("=" * 50)
        print(f"Total Attack Attempts: {total_attempts}")
        print(f"Successful Attacks: {total_successful}")
        print(f"Blocked Attacks: {total_blocked}")
        print(f"Security Score: {security_score:.1f}%")
        
        if security_score >= 90:
            print("🛡️ EXCELLENT SECURITY - System well protected!")
        elif security_score >= 75:
            print("✅ GOOD SECURITY - Minor improvements possible")
        elif security_score >= 60:
            print("⚠️ MODERATE SECURITY - Improvements needed")
        else:
            print("❌ POOR SECURITY - Immediate action required!")
        
        print("\n📝 Attack Breakdown:")
        for attack_type, (successful, blocked) in attack_results.items():
            total = successful + blocked
            if total > 0:
                success_rate = (successful / total) * 100
                print(f"   {attack_type.replace('_', ' ').title()}: "
                      f"{successful}/{total} successful ({success_rate:.1f}%)")
        
        return security_score >= 75


if __name__ == '__main__':
    print("⚠️  SECURITY WARNING ⚠️")
    print("This script simulates cyber attacks for testing purposes only.")
    print("Only run this against systems you own or have explicit permission to test.")
    print("=" * 70)
    
    response = input("Do you want to proceed with the attack simulation? (yes/no): ")
    if response.lower() != 'yes':
        print("Attack simulation cancelled.")
        sys.exit(0)
    
    simulator = AttackSimulator()
    success = simulator.run_attack_simulation()
    
    if success:
        print("\n🎯 Security defenses are working effectively!")
    else:
        print("\n⚠️ Security vulnerabilities detected - review needed!")
    
    sys.exit(0 if success else 1)

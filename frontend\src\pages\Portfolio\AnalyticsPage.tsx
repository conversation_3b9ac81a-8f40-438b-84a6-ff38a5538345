// TrustVault - Portfolio Analytics Page

import React, { useState } from 'react';
import {
  Box,
  Typography,
  Button,
  Grid,
  Card,
  CardContent,
  Paper,
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  LinearProgress,
} from '@mui/material';
import {
  ArrowBack,
  TrendingUp,
  TrendingDown,
  PieChart,
  ShowChart,
  Assessment,
} from '@mui/icons-material';
import { Helmet } from 'react-helmet-async';
import { useNavigate, useParams } from 'react-router-dom';
import { useQuery } from 'react-query';

// Services
import apiService from '../../services/api';

const AnalyticsPage: React.FC = () => {
  const navigate = useNavigate();
  const { id: portfolioId } = useParams<{ id: string }>();
  
  const [timeRange, setTimeRange] = useState<string>('1M');

  // Fetch portfolio details
  const { data: portfolio } = useQuery(
    ['portfolio', portfolioId],
    () => apiService.getPortfolio(portfolioId!),
    {
      enabled: !!portfolioId,
    }
  );

  // Fetch analytics data
  const { data: analytics, isLoading: analyticsLoading } = useQuery(
    ['analytics', portfolioId],
    () => apiService.getPortfolioAnalytics(portfolioId!),
    {
      enabled: !!portfolioId,
    }
  );

  // Fetch holdings for allocation analysis
  const { data: holdings } = useQuery(
    ['holdings', portfolioId],
    () => apiService.getHoldings(portfolioId!),
    {
      enabled: !!portfolioId,
    }
  );

  const formatCurrency = (value: string | number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(typeof value === 'string' ? parseFloat(value) : value);
  };

  const formatPercentage = (value: number) => {
    return `${value >= 0 ? '+' : ''}${value.toFixed(2)}%`;
  };

  // Calculate portfolio metrics
  const totalValue = analytics?.total_value || (portfolio ? parseFloat(portfolio.total_value) : 0);

  // Performance metrics from analytics or mock data
  const performanceMetrics = {
    totalReturn: analytics?.total_profit_loss || 1250.75,
    totalReturnPercent: analytics?.total_profit_loss_percentage || 8.45,
    totalValue: analytics?.total_value || totalValue,
    totalCost: analytics?.total_cost || 0,
    dayChange: -45.32, // This would come from real-time data
    dayChangePercent: -0.32,
    weekChange: 125.50,
    weekChangePercent: 0.89,
    monthChange: 450.25,
    monthChangePercent: 3.21,
  };

  // Use analytics data if available, otherwise use holdings data
  const allocationData = analytics?.asset_allocation || holdings?.map((holding: any) => ({
    symbol: holding.asset.symbol,
    name: holding.asset.name,
    value: parseFloat(holding.current_value),
    percentage: (parseFloat(holding.current_value) / totalValue) * 100,
    sector: holding.asset.sector,
  })) || [];

  // Use analytics sector data if available, otherwise calculate from holdings
  const sectorAllocation = analytics?.sector_allocation || allocationData.reduce((acc: Record<string, any>, holding: any) => {
    const sector = holding.sector || 'Other';
    if (!(sector in acc)) {
      acc[sector] = { value: 0, percentage: 0 };
    }
    acc[sector].value += holding.value;
    acc[sector].percentage += holding.percentage;
    return acc;
  }, {} as Record<string, any>);

  if (!portfolioId) {
    return (
      <Box sx={{ maxWidth: 1200, mx: 'auto', p: 3 }}>
        <Typography variant="h6" color="error">
          Portfolio ID is required
        </Typography>
      </Box>
    );
  }

  if (analyticsLoading) {
    return (
      <Box sx={{ maxWidth: 1200, mx: 'auto', p: 3 }}>
        <Typography variant="h6">
          Loading analytics...
        </Typography>
      </Box>
    );
  }

  return (
    <>
      <Helmet>
        <title>Analytics - TrustVault</title>
        <meta name="description" content="Portfolio performance analytics and insights" />
      </Helmet>

      <Box sx={{ maxWidth: 1200, mx: 'auto', p: 3 }}>
        {/* Header */}
        <Box display="flex" alignItems="center" justifyContent="space-between" mb={3}>
          <Box display="flex" alignItems="center">
            <Button
              startIcon={<ArrowBack />}
              onClick={() => navigate(`/portfolios/${portfolioId}`)}
              sx={{ mr: 2 }}
            >
              Back to Portfolio
            </Button>
            <Box>
              <Typography variant="h4" component="h1">
                Portfolio Analytics
              </Typography>
              {portfolio && (
                <Typography variant="body2" color="text.secondary">
                  {portfolio.name}
                </Typography>
              )}
            </Box>
          </Box>
          
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Time Range</InputLabel>
            <Select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value)}
              label="Time Range"
            >
              <MenuItem value="1D">1 Day</MenuItem>
              <MenuItem value="1W">1 Week</MenuItem>
              <MenuItem value="1M">1 Month</MenuItem>
              <MenuItem value="3M">3 Months</MenuItem>
              <MenuItem value="6M">6 Months</MenuItem>
              <MenuItem value="1Y">1 Year</MenuItem>
              <MenuItem value="ALL">All Time</MenuItem>
            </Select>
          </FormControl>
        </Box>

        <Grid container spacing={3}>
          {/* Performance Overview */}
          <Grid item xs={12}>
            <Paper sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>
                Performance Overview
              </Typography>
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6} md={3}>
                  <Card variant="outlined">
                    <CardContent>
                      <Box display="flex" alignItems="center" mb={1}>
                        <Assessment color="primary" sx={{ mr: 1 }} />
                        <Typography variant="body2" color="text.secondary">
                          Total Value
                        </Typography>
                      </Box>
                      <Typography variant="h5" fontWeight="bold">
                        {formatCurrency(totalValue)}
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
                
                <Grid item xs={12} sm={6} md={3}>
                  <Card variant="outlined">
                    <CardContent>
                      <Box display="flex" alignItems="center" mb={1}>
                        <TrendingUp color="success" sx={{ mr: 1 }} />
                        <Typography variant="body2" color="text.secondary">
                          Total Return
                        </Typography>
                      </Box>
                      <Typography variant="h5" fontWeight="bold" color="success.main">
                        {formatCurrency(performanceMetrics.totalReturn)}
                      </Typography>
                      <Typography variant="body2" color="success.main">
                        {formatPercentage(performanceMetrics.totalReturnPercent)}
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
                
                <Grid item xs={12} sm={6} md={3}>
                  <Card variant="outlined">
                    <CardContent>
                      <Box display="flex" alignItems="center" mb={1}>
                        <TrendingDown color="error" sx={{ mr: 1 }} />
                        <Typography variant="body2" color="text.secondary">
                          Day Change
                        </Typography>
                      </Box>
                      <Typography variant="h5" fontWeight="bold" color="error.main">
                        {formatCurrency(performanceMetrics.dayChange)}
                      </Typography>
                      <Typography variant="body2" color="error.main">
                        {formatPercentage(performanceMetrics.dayChangePercent)}
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
                
                <Grid item xs={12} sm={6} md={3}>
                  <Card variant="outlined">
                    <CardContent>
                      <Box display="flex" alignItems="center" mb={1}>
                        <ShowChart color="info" sx={{ mr: 1 }} />
                        <Typography variant="body2" color="text.secondary">
                          Month Change
                        </Typography>
                      </Box>
                      <Typography variant="h5" fontWeight="bold" color="success.main">
                        {formatCurrency(performanceMetrics.monthChange)}
                      </Typography>
                      <Typography variant="body2" color="success.main">
                        {formatPercentage(performanceMetrics.monthChangePercent)}
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </Paper>
          </Grid>

          {/* Asset Allocation */}
          <Grid item xs={12} md={6}>
            <Paper sx={{ p: 3 }}>
              <Box display="flex" alignItems="center" mb={3}>
                <PieChart color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6">
                  Asset Allocation
                </Typography>
              </Box>
              
              {allocationData.length === 0 ? (
                <Box textAlign="center" py={4}>
                  <Typography variant="body2" color="text.secondary">
                    No holdings to display
                  </Typography>
                </Box>
              ) : (
                <Box>
                  {allocationData.slice(0, 5).map((holding: any, index: number) => (
                    <Box key={holding.symbol} mb={2}>
                      <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                        <Box>
                          <Typography variant="body2" fontWeight="medium">
                            {holding.symbol}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {holding.name}
                          </Typography>
                        </Box>
                        <Box textAlign="right">
                          <Typography variant="body2" fontWeight="medium">
                            {holding.percentage.toFixed(1)}%
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {formatCurrency(holding.value)}
                          </Typography>
                        </Box>
                      </Box>
                      <LinearProgress
                        variant="determinate"
                        value={holding.percentage}
                        sx={{ height: 6, borderRadius: 3 }}
                      />
                    </Box>
                  ))}
                </Box>
              )}
            </Paper>
          </Grid>

          {/* Sector Allocation */}
          <Grid item xs={12} md={6}>
            <Paper sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>
                Sector Allocation
              </Typography>
              
              {Object.keys(sectorAllocation).length === 0 ? (
                <Box textAlign="center" py={4}>
                  <Typography variant="body2" color="text.secondary">
                    No sector data available
                  </Typography>
                </Box>
              ) : (
                <Box>
                  {Object.entries(sectorAllocation).map(([sector, data]: [string, any]) => (
                    <Box key={sector} mb={2}>
                      <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                        <Typography variant="body2" fontWeight="medium">
                          {sector}
                        </Typography>
                        <Box textAlign="right">
                          <Typography variant="body2" fontWeight="medium">
                            {data.percentage.toFixed(1)}%
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {formatCurrency(data.value)}
                          </Typography>
                        </Box>
                      </Box>
                      <LinearProgress
                        variant="determinate"
                        value={data.percentage}
                        sx={{ height: 6, borderRadius: 3 }}
                      />
                    </Box>
                  ))}
                </Box>
              )}
            </Paper>
          </Grid>

          {/* Performance Chart Placeholder */}
          <Grid item xs={12}>
            <Paper sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>
                Performance Chart
              </Typography>
              <Box
                sx={{
                  height: 300,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  bgcolor: 'background.default',
                  borderRadius: 1,
                }}
              >
                <Box textAlign="center">
                  <ShowChart sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
                  <Typography variant="h6" color="text.secondary" gutterBottom>
                    Performance Chart
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Interactive chart showing portfolio performance over time
                  </Typography>
                  <Chip label="Coming Soon" color="primary" size="small" sx={{ mt: 1 }} />
                </Box>
              </Box>
            </Paper>
          </Grid>
        </Grid>
      </Box>
    </>
  );
};

export default AnalyticsPage;

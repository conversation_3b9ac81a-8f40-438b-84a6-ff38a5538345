# TrustVault - Production Requirements
# Optimized and cleaned dependencies

# Django Core
Django==4.2.7
djangorestframework==3.14.0
django-cors-headers==4.3.1
django-environ==0.11.2

# Authentication & Security
djangorestframework-simplejwt==5.3.0
django-otp==1.2.2
qrcode==7.4.2
cryptography==41.0.7
bcrypt==4.1.2
django-ratelimit==4.1.0
django-axes==6.1.1
pyotp==2.9.0

# Database
psycopg2-binary==2.9.7
redis==5.0.1
django-redis==5.4.0

# API Documentation
drf-spectacular==0.26.5

# Security Framework
pycryptodome==3.19.0
user-agents==2.2.0
geoip2==4.7.0
aiohttp==3.9.1
pynacl==1.5.0
jwcrypto==1.5.0

# Security Headers & CSP
django-csp==3.7
hvac==2.1.0

# Validation & Filtering
django-filter==23.4

# Task Queue (Production)
celery==5.3.4
django-celery-beat==2.5.0
django-celery-results==2.5.0

# File Handling
Pillow==10.1.0

# HTTP Client
requests==2.31.0

# Utilities
python-decouple==3.8
python-dateutil==2.8.2

# WSGI Server
gunicorn==21.2.0
whitenoise==6.6.0

# Compliance & Audit
django-audit-log==0.7.0

# Database Extensions
django-extensions==3.2.3
django-model-utils==4.3.1

# Advanced Security
django-guardian==2.4.0
django-two-factor-auth==1.15.0

# Monitoring (Production)
sentry-sdk==1.38.0
structlog==23.2.0

# Advanced Logging
python-json-logger==2.0.7

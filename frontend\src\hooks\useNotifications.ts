// TrustVault - Notifications Hook

import { useQuery, useMutation, useQueryClient } from 'react-query';
import apiService from '../services/api';

export interface Notification {
  id: string;
  alert_name?: string;
  channel: 'EMAIL' | 'SMS' | 'PUSH' | 'IN_APP';
  recipient: string;
  subject: string;
  message: string;
  status: 'PENDING' | 'SENT' | 'DELIVERED' | 'FAILED' | 'BOUNCED';
  sent_at?: string;
  delivered_at?: string;
  error_message?: string;
  created_at: string;
  is_read?: boolean;
}

export interface NotificationStats {
  total: number;
  unread: number;
  by_channel: {
    [key: string]: number;
  };
}

export const useNotifications = () => {
  const queryClient = useQueryClient();

  // Fetch notifications
  const {
    data: notifications = [],
    isLoading,
    error,
    refetch
  } = useQuery<Notification[]>(
    'notifications',
    () => apiService.getNotifications({ ordering: '-created_at' }),
    {
      refetchInterval: 30000, // Refetch every 30 seconds
      staleTime: 10000, // Consider data stale after 10 seconds
    }
  );

  // Calculate stats
  const stats: NotificationStats = {
    total: notifications.length,
    unread: notifications.filter(n => !n.is_read && n.channel === 'IN_APP').length,
    by_channel: notifications.reduce((acc, notification) => {
      acc[notification.channel] = (acc[notification.channel] || 0) + 1;
      return acc;
    }, {} as { [key: string]: number })
  };

  // Mark notification as read (mock implementation)
  const markAsReadMutation = useMutation(
    (notificationId: string) => {
      // TODO: Implement actual API call when backend supports it
      return Promise.resolve();
    },
    {
      onSuccess: () => {
        queryClient.invalidateQueries('notifications');
      },
    }
  );

  // Mark all as read (mock implementation)
  const markAllAsReadMutation = useMutation(
    () => {
      // TODO: Implement actual API call when backend supports it
      return Promise.resolve();
    },
    {
      onSuccess: () => {
        queryClient.invalidateQueries('notifications');
      },
    }
  );

  return {
    notifications,
    stats,
    isLoading,
    error,
    refetch,
    markAsRead: markAsReadMutation.mutate,
    markAllAsRead: markAllAsReadMutation.mutate,
    isMarkingAsRead: markAsReadMutation.isLoading,
    isMarkingAllAsRead: markAllAsReadMutation.isLoading,
  };
};

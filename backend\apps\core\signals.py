# TrustVault - Core Signals

import logging
from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from django.contrib.auth import get_user_model
from .models import AuditLog, SecurityEvent

logger = logging.getLogger(__name__)

User = get_user_model()


@receiver(post_save, sender=SecurityEvent)
def security_event_created_handler(sender, instance, created, **kwargs):
    """Handle security event creation."""
    if created:
        logger.warning(f"Security event created: {instance.event_type} - {instance.risk_level} from {instance.source_ip}")
        
        # For critical events, you might want to send immediate notifications
        if instance.risk_level == 'CRITICAL':
            logger.critical(f"CRITICAL security event: {instance.description}")
            # Here you could integrate with external alerting systems


@receiver(post_save, sender=AuditLog)
def audit_log_created_handler(sender, instance, created, **kwargs):
    """Handle audit log creation."""
    if created and instance.severity in ['HIGH', 'CRITICAL']:
        logger.warning(f"High severity audit event: {instance.action} on {instance.resource_type} by {instance.user}")


# Data retention cleanup (this would typically be run as a scheduled task)
def cleanup_old_audit_logs():
    """Clean up old audit logs based on retention policy."""
    from django.utils import timezone
    from datetime import timedelta
    from django.conf import settings
    
    retention_days = getattr(settings, 'AUDIT_LOG_RETENTION_DAYS', 2555)  # 7 years default
    cutoff_date = timezone.now() - timedelta(days=retention_days)
    
    deleted_count = AuditLog.objects.filter(timestamp__lt=cutoff_date).delete()[0]
    
    if deleted_count > 0:
        logger.info(f"Cleaned up {deleted_count} old audit log entries")
    
    return deleted_count

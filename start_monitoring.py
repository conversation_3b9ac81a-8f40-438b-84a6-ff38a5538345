#!/usr/bin/env python
"""
TrustVault - Quick Monitoring Setup
Start Prometheus and Alertmanager for testing
"""

import os
import sys
import subprocess
import time
import requests
from datetime import datetime

def check_docker():
    """Check if Docker is available."""
    try:
        result = subprocess.run(['docker', '--version'], 
            capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Docker is available")
            return True
        else:
            print("❌ Docker is not available")
            return False
    except FileNotFoundError:
        print("❌ Docker is not installed")
        return False

def start_prometheus():
    """Start Prometheus in Docker."""
    print("🚀 Starting Prometheus...")
    
    # Create prometheus data directory
    os.makedirs("prometheus_data", exist_ok=True)
    
    # Start Prometheus container
    cmd = [
        'docker', 'run', '-d',
        '--name', 'trustvault-prometheus',
        '-p', '9090:9090',
        '-v', f'{os.getcwd()}/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml',
        '-v', f'{os.getcwd()}/prometheus/security_rules.yml:/etc/prometheus/rules/security_rules.yml',
        '-v', f'{os.getcwd()}/prometheus_data:/prometheus',
        'prom/prometheus:latest',
        '--config.file=/etc/prometheus/prometheus.yml',
        '--storage.tsdb.path=/prometheus',
        '--web.console.libraries=/etc/prometheus/console_libraries',
        '--web.console.templates=/etc/prometheus/consoles',
        '--storage.tsdb.retention.time=200h',
        '--web.enable-lifecycle',
        '--web.enable-admin-api'
    ]
    
    try:
        # Remove existing container if it exists
        subprocess.run(['docker', 'rm', '-f', 'trustvault-prometheus'], 
            capture_output=True)
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Prometheus started successfully")
            return True
        else:
            print(f"❌ Failed to start Prometheus: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ Error starting Prometheus: {e}")
        return False

def start_alertmanager():
    """Start Alertmanager in Docker."""
    print("🚀 Starting Alertmanager...")
    
    # Create alertmanager data directory
    os.makedirs("alertmanager_data", exist_ok=True)
    
    # Start Alertmanager container
    cmd = [
        'docker', 'run', '-d',
        '--name', 'trustvault-alertmanager',
        '-p', '9093:9093',
        '-v', f'{os.getcwd()}/prometheus/alertmanager.yml:/etc/alertmanager/alertmanager.yml',
        '-v', f'{os.getcwd()}/alertmanager_data:/alertmanager',
        'prom/alertmanager:latest',
        '--config.file=/etc/alertmanager/alertmanager.yml',
        '--storage.path=/alertmanager',
        '--web.external-url=http://localhost:9093'
    ]
    
    try:
        # Remove existing container if it exists
        subprocess.run(['docker', 'rm', '-f', 'trustvault-alertmanager'], 
            capture_output=True)
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Alertmanager started successfully")
            return True
        else:
            print(f"❌ Failed to start Alertmanager: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ Error starting Alertmanager: {e}")
        return False

def wait_for_services():
    """Wait for services to be ready."""
    print("⏳ Waiting for services to be ready...")
    
    services = [
        ("Prometheus", "http://localhost:9090/-/ready"),
        ("Alertmanager", "http://localhost:9093/-/ready")
    ]
    
    for service_name, url in services:
        for attempt in range(30):  # Wait up to 30 seconds
            try:
                response = requests.get(url, timeout=2)
                if response.status_code == 200:
                    print(f"✅ {service_name} is ready")
                    break
            except requests.exceptions.RequestException:
                pass
            
            if attempt == 29:
                print(f"⚠️ {service_name} may not be ready")
            else:
                time.sleep(1)

def create_simple_config():
    """Create simplified configurations for testing."""
    print("📝 Creating simplified configurations...")
    
    # Simple Prometheus config
    prometheus_config = """
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "rules/security_rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - localhost:9093

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'trustvault-app'
    static_configs:
      - targets: ['host.docker.internal:8000']
    metrics_path: '/metrics'
    scrape_interval: 30s
"""
    
    # Simple Alertmanager config
    alertmanager_config = """
global:
  smtp_smarthost: 'smtp-relay.brevo.com:587'
  smtp_from: '<EMAIL>'
  smtp_auth_username: '<EMAIL>'
  smtp_auth_password: 'SVWtMY6qOLjhNpE7'

route:
  group_by: ['alertname']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'default-receiver'

receivers:
  - name: 'default-receiver'
    email_configs:
      - to: '<EMAIL>'
        subject: 'TrustVault Alert: {{ .GroupLabels.alertname }}'
        body: |
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Severity: {{ .Labels.severity }}
          Time: {{ .StartsAt }}
          {{ end }}
"""
    
    # Create directories
    os.makedirs("prometheus", exist_ok=True)
    
    # Write configs
    with open("prometheus/prometheus-simple.yml", "w") as f:
        f.write(prometheus_config)
    
    with open("prometheus/alertmanager-simple.yml", "w") as f:
        f.write(alertmanager_config)
    
    print("✅ Simplified configurations created")

def main():
    """Main function to start monitoring stack."""
    print("🛡️ TrustVault Monitoring Setup")
    print("=" * 40)
    print(f"Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 40)
    
    # Check prerequisites
    if not check_docker():
        print("Please install Docker and try again.")
        return False
    
    # Create simple configs
    create_simple_config()
    
    # Start services
    prometheus_ok = start_prometheus()
    alertmanager_ok = start_alertmanager()
    
    if prometheus_ok and alertmanager_ok:
        # Wait for services to be ready
        wait_for_services()
        
        print("\n" + "=" * 40)
        print("🎉 MONITORING STACK STARTED!")
        print("=" * 40)
        print("📊 Prometheus: http://localhost:9090")
        print("🚨 Alertmanager: http://localhost:9093")
        print("\n📋 Next steps:")
        print("1. Open Prometheus at http://localhost:9090")
        print("2. Check targets in Status > Targets")
        print("3. Run security tests: python alert_system_tester.py")
        print("4. View alerts in Alertmanager at http://localhost:9093")
        
        return True
    else:
        print("\n❌ Failed to start monitoring stack")
        return False

def stop_services():
    """Stop monitoring services."""
    print("🛑 Stopping monitoring services...")
    
    containers = ['trustvault-prometheus', 'trustvault-alertmanager']
    
    for container in containers:
        try:
            result = subprocess.run(['docker', 'stop', container], 
                capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ Stopped {container}")
            
            subprocess.run(['docker', 'rm', container], 
                capture_output=True)
        except Exception as e:
            print(f"⚠️ Error stopping {container}: {e}")

if __name__ == '__main__':
    if len(sys.argv) > 1 and sys.argv[1] == 'stop':
        stop_services()
    else:
        success = main()
        if not success:
            sys.exit(1)

<!-- TrustVault Custom Security Rules -->
<group name="trustvault,">

  <!-- ================================================================== -->
  <!-- AUTHENTICATION RULES -->
  <!-- ================================================================== -->
  
  <rule id="100100" level="5">
    <if_sid>31100</if_sid>
    <match>trustvault</match>
    <description>TrustVault: User authentication attempt</description>
    <group>authentication_success,pci_dss_10.2.5,gpg13_7.1,gdpr_IV_32.2,hipaa_164.312.b,nist_800_53_AU.14,nist_800_53_AC.7,tsc_CC6.8,tsc_CC7.2,tsc_CC7.3,</group>
  </rule>

  <rule id="100101" level="10" frequency="5" timeframe="300">
    <if_matched_sid>100100</if_matched_sid>
    <same_source_ip />
    <description>TrustVault: Multiple authentication failures from same IP</description>
    <mitre>
      <id>T1110</id>
    </mitre>
    <group>authentication_failures,pci_dss_10.2.4,pci_dss_10.2.5,gpg13_7.1,gdpr_IV_35.7.d,hipaa_164.312.b,nist_800_53_AU.14,nist_800_53_AC.7,tsc_CC6.1,tsc_CC6.8,tsc_CC7.2,tsc_CC7.3,</group>
  </rule>

  <rule id="100102" level="12" frequency="10" timeframe="600">
    <if_matched_sid>100101</if_matched_sid>
    <description>TrustVault: Brute force attack detected</description>
    <mitre>
      <id>T1110</id>
    </mitre>
    <group>attack,authentication_failures,pci_dss_10.2.4,pci_dss_10.2.5,gpg13_7.1,gdpr_IV_35.7.d,hipaa_164.312.b,nist_800_53_AU.14,nist_800_53_AC.7,tsc_CC6.1,tsc_CC6.8,tsc_CC7.2,tsc_CC7.3,</group>
  </rule>

  <!-- ================================================================== -->
  <!-- WEB APPLICATION SECURITY RULES -->
  <!-- ================================================================== -->
  
  <rule id="100200" level="7">
    <if_sid>31100</if_sid>
    <match>SQL injection|sql injection|union select|' or 1=1|' or '1'='1</match>
    <description>TrustVault: SQL injection attempt detected</description>
    <mitre>
      <id>T1190</id>
    </mitre>
    <group>web,attack,sql_injection,pci_dss_6.5.1,nist_800_53_SI.10,tsc_CC6.1,tsc_CC7.1,tsc_CC8.1,</group>
  </rule>

  <rule id="100201" level="7">
    <if_sid>31100</if_sid>
    <match>script>|javascript:|vbscript:|onload=|onerror=|&lt;script&gt;</match>
    <description>TrustVault: XSS (Cross-Site Scripting) attempt detected</description>
    <mitre>
      <id>T1190</id>
    </mitre>
    <group>web,attack,xss,pci_dss_6.5.7,nist_800_53_SI.10,tsc_CC6.1,tsc_CC7.1,tsc_CC8.1,</group>
  </rule>

  <rule id="100202" level="8">
    <if_sid>31100</if_sid>
    <match>../../../|..\..\..\..|/etc/passwd|/etc/shadow|boot.ini|win.ini</match>
    <description>TrustVault: Directory traversal attempt detected</description>
    <mitre>
      <id>T1190</id>
    </mitre>
    <group>web,attack,directory_traversal,pci_dss_6.5.8,nist_800_53_SI.10,tsc_CC6.1,tsc_CC7.1,tsc_CC8.1,</group>
  </rule>

  <rule id="100203" level="6">
    <if_sid>31100</if_sid>
    <match>eval\(|base64_decode|gzinflate|str_rot13|system\(|exec\(|shell_exec</match>
    <description>TrustVault: Code injection attempt detected</description>
    <mitre>
      <id>T1190</id>
    </mitre>
    <group>web,attack,code_injection,pci_dss_6.5.1,nist_800_53_SI.10,tsc_CC6.1,tsc_CC7.1,tsc_CC8.1,</group>
  </rule>

  <!-- ================================================================== -->
  <!-- PORTFOLIO MANAGEMENT RULES -->
  <!-- ================================================================== -->
  
  <rule id="100300" level="3">
    <if_sid>31100</if_sid>
    <match>/api/v1/portfolio</match>
    <description>TrustVault: Portfolio access</description>
    <group>portfolio,access,pci_dss_10.2.1,gpg13_7.1,gdpr_IV_30.1.g,hipaa_164.312.a.1,nist_800_53_AU.14,tsc_CC6.8,tsc_CC7.2,</group>
  </rule>

  <rule id="100301" level="5">
    <if_sid>100300</if_sid>
    <match>POST|PUT|DELETE</match>
    <description>TrustVault: Portfolio modification attempt</description>
    <group>portfolio,modification,pci_dss_10.2.2,gpg13_7.1,gdpr_IV_30.1.g,hipaa_164.312.a.1,nist_800_53_AU.14,tsc_CC6.8,tsc_CC7.2,</group>
  </rule>

  <rule id="100302" level="8" frequency="10" timeframe="300">
    <if_matched_sid>100301</if_matched_sid>
    <same_source_ip />
    <description>TrustVault: Suspicious portfolio modification activity</description>
    <mitre>
      <id>T1565</id>
    </mitre>
    <group>portfolio,suspicious_activity,pci_dss_10.2.2,gpg13_7.1,gdpr_IV_35.7.d,hipaa_164.312.a.1,nist_800_53_AU.14,tsc_CC6.8,tsc_CC7.2,</group>
  </rule>

  <!-- ================================================================== -->
  <!-- FINANCIAL DATA RULES -->
  <!-- ================================================================== -->
  
  <rule id="100400" level="4">
    <if_sid>31100</if_sid>
    <match>/api/v1/transactions|/api/v1/balance|/api/v1/assets</match>
    <description>TrustVault: Financial data access</description>
    <group>financial,access,pci_dss_10.2.1,gpg13_7.1,gdpr_IV_30.1.g,hipaa_164.312.a.1,nist_800_53_AU.14,tsc_CC6.8,tsc_CC7.2,</group>
  </rule>

  <rule id="100401" level="7">
    <if_sid>100400</if_sid>
    <match>amount.*[0-9]{6,}|value.*[0-9]{6,}</match>
    <description>TrustVault: Large financial transaction detected</description>
    <group>financial,large_transaction,pci_dss_10.2.2,gpg13_7.1,gdpr_IV_30.1.g,hipaa_164.312.a.1,nist_800_53_AU.14,tsc_CC6.8,tsc_CC7.2,</group>
  </rule>

  <!-- ================================================================== -->
  <!-- SYSTEM SECURITY RULES -->
  <!-- ================================================================== -->
  
  <rule id="100500" level="8">
    <if_sid>31100</if_sid>
    <match>docker|container|kubernetes|k8s</match>
    <description>TrustVault: Container-related activity detected</description>
    <group>container,system,pci_dss_10.2.7,gpg13_7.1,gdpr_IV_35.7.d,hipaa_164.312.b,nist_800_53_AU.14,tsc_CC6.8,tsc_CC7.2,</group>
  </rule>

  <rule id="100501" level="10">
    <if_sid>31100</if_sid>
    <match>privilege escalation|sudo su|su root|chmod 777</match>
    <description>TrustVault: Privilege escalation attempt detected</description>
    <mitre>
      <id>T1068</id>
    </mitre>
    <group>privilege_escalation,attack,pci_dss_10.2.2,gpg13_7.1,gdpr_IV_35.7.d,hipaa_164.312.b,nist_800_53_AU.14,tsc_CC6.8,tsc_CC7.2,</group>
  </rule>

  <!-- ================================================================== -->
  <!-- DATA EXFILTRATION RULES -->
  <!-- ================================================================== -->
  
  <rule id="100600" level="6">
    <if_sid>31100</if_sid>
    <match>export|download|backup</match>
    <description>TrustVault: Data export activity detected</description>
    <group>data_export,access,pci_dss_10.2.1,gpg13_7.1,gdpr_IV_30.1.g,hipaa_164.312.a.1,nist_800_53_AU.14,tsc_CC6.8,tsc_CC7.2,</group>
  </rule>

  <rule id="100601" level="9" frequency="5" timeframe="300">
    <if_matched_sid>100600</if_matched_sid>
    <same_source_ip />
    <description>TrustVault: Suspicious data exfiltration activity</description>
    <mitre>
      <id>T1041</id>
    </mitre>
    <group>data_exfiltration,attack,pci_dss_10.2.1,gpg13_7.1,gdpr_IV_35.7.d,hipaa_164.312.a.1,nist_800_53_AU.14,tsc_CC6.8,tsc_CC7.2,</group>
  </rule>

  <!-- ================================================================== -->
  <!-- COMPLIANCE RULES -->
  <!-- ================================================================== -->
  
  <rule id="100700" level="4">
    <if_sid>31100</if_sid>
    <match>gdpr|privacy|consent|personal_data</match>
    <description>TrustVault: GDPR-related activity detected</description>
    <group>gdpr,compliance,privacy,gdpr_IV_30.1.g,tsc_CC6.8,tsc_CC7.2,</group>
  </rule>

  <rule id="100701" level="5">
    <if_sid>31100</if_sid>
    <match>audit|compliance|iso27001|pci_dss</match>
    <description>TrustVault: Compliance audit activity detected</description>
    <group>audit,compliance,pci_dss_10.2.1,gpg13_7.1,gdpr_IV_30.1.g,hipaa_164.312.a.1,nist_800_53_AU.14,tsc_CC6.8,tsc_CC7.2,</group>
  </rule>

  <!-- ================================================================== -->
  <!-- THREAT INTELLIGENCE RULES -->
  <!-- ================================================================== -->
  
  <rule id="100800" level="10">
    <if_sid>31100</if_sid>
    <match>malware|trojan|virus|ransomware|cryptolocker</match>
    <description>TrustVault: Malware-related activity detected</description>
    <mitre>
      <id>T1566</id>
    </mitre>
    <group>malware,attack,pci_dss_5.1,gpg13_4.2,gdpr_IV_35.7.d,hipaa_164.312.a.1,nist_800_53_SI.3,tsc_CC6.8,tsc_CC7.1,</group>
  </rule>

  <rule id="100801" level="12">
    <if_sid>31100</if_sid>
    <match>apt|advanced persistent threat|lateral movement|command and control</match>
    <description>TrustVault: Advanced Persistent Threat (APT) indicators detected</description>
    <mitre>
      <id>T1071</id>
    </mitre>
    <group>apt,attack,advanced_threat,pci_dss_11.4,gpg13_4.2,gdpr_IV_35.7.d,hipaa_164.312.a.1,nist_800_53_SI.4,tsc_CC6.8,tsc_CC7.1,</group>
  </rule>

</group>

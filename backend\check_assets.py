#!/usr/bin/env python
"""
Check assets in database
"""

import os
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'trustvault.settings')
django.setup()

from apps.portfolio.models import Asset

print("📈 Assets in Database:")
print("=" * 30)

assets = Asset.objects.all()
print(f"Total assets: {assets.count()}")

for asset in assets:
    print(f"ID: {asset.id}")
    print(f"Symbol: {asset.symbol}")
    print(f"Name: {asset.name}")
    print(f"Type: {asset.asset_type}")
    print("-" * 20)

if assets.count() == 0:
    print("No assets found. Creating test assets...")
    
    from decimal import Decimal
    
    test_assets = [
        {
            'symbol': 'AAPL',
            'name': 'Apple Inc.',
            'asset_type': 'STOCK',
            'current_price': Decimal('150.00'),
            'currency': 'USD'
        },
        {
            'symbol': 'GOOGL',
            'name': 'Alphabet Inc.',
            'asset_type': 'STOCK',
            'current_price': Decimal('2800.00'),
            'currency': 'USD'
        },
        {
            'symbol': 'MSFT',
            'name': 'Microsoft Corporation',
            'asset_type': 'STOCK',
            'current_price': Decimal('380.00'),
            'currency': 'USD'
        },
        {
            'symbol': 'TSLA',
            'name': 'Tesla Inc.',
            'asset_type': 'STOCK',
            'current_price': Decimal('200.00'),
            'currency': 'USD'
        },
        {
            'symbol': 'BTC',
            'name': 'Bitcoin',
            'asset_type': 'CRYPTO',
            'current_price': Decimal('45000.00'),
            'currency': 'USD'
        }
    ]
    
    for asset_data in test_assets:
        asset = Asset.objects.create(**asset_data)
        print(f"✅ Created: {asset.symbol} (ID: {asset.id})")

print("\n✅ Check completed!")

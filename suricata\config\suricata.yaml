# TrustVault - Suricata IDS/IPS Configuration

# ============================================================================
# GLOBAL CONFIGURATION
# ============================================================================

# Number of packets preallocated per thread
max-pending-packets: 1024

# Runmode the engine should use
runmode: autofp

# Default log directory
default-log-dir: /var/log/suricata/

# Global stats configuration
stats:
  enabled: yes
  interval: 8

# Configure the type of alert (and other) logging
outputs:
  # Extensible Event Format (JSON) output
  - eve-log:
      enabled: yes
      filetype: regular
      filename: eve.json
      community-id: true
      community-id-seed: 0
      types:
        - alert:
            payload: yes
            payload-buffer-size: 4kb
            payload-printable: yes
            packet: yes
            metadata: yes
            http-body: yes
            http-body-printable: yes
            tagged-packets: yes
        - anomaly:
            enabled: yes
            types:
              decode: yes
              stream: yes
              applayer: yes
        - http:
            extended: yes
        - dns:
            query: yes
            answer: yes
        - tls:
            extended: yes
        - files:
            force-magic: no
        - smtp:
        - ssh
        - stats:
            totals: yes
            threads: no
            deltas: no
        - flow
        - netflow

  # Alert output for integration with Wazuh
  - alert-debug:
      enabled: no
      filename: alert-debug.log
      append: yes

  # HTTP transaction log
  - http-log:
      enabled: yes
      filename: http.log
      append: yes

  # TLS handshake log
  - tls-log:
      enabled: yes
      filename: tls.log
      append: yes
      extended: yes

  # DNS transaction log
  - dns-log:
      enabled: yes
      filename: dns.log
      append: yes

  # File transaction log
  - file-log:
      enabled: yes
      filename: files-json.log
      append: yes
      force-magic: no

# ============================================================================
# LOGGING CONFIGURATION
# ============================================================================

logging:
  default-log-level: notice
  default-log-format: "[%i] %t - (%f:%l) <%d> (%n) -- "
  
  outputs:
  - console:
      enabled: yes
      type: json
  - file:
      enabled: yes
      level: info
      filename: /var/log/suricata/suricata.log
      type: json
  - syslog:
      enabled: no
      facility: local5
      format: "[%i] <%d> -- "

# ============================================================================
# APPLICATION LAYER PARSERS
# ============================================================================

app-layer:
  protocols:
    tls:
      enabled: yes
      detection-ports:
        dp: 443
      ja3-fingerprints: yes
    http:
      enabled: yes
      libhtp:
        default-config:
          personality: IDS
          request-body-limit: 100kb
          response-body-limit: 100kb
          request-body-minimal-inspect-size: 32kb
          request-body-inspect-window: 4kb
          response-body-minimal-inspect-size: 40kb
          response-body-inspect-window: 16kb
          response-body-decompress-layer-limit: 2
          http-body-inline: auto
          swf-decompression:
            enabled: yes
            type: both
            compress-depth: 100kb
            decompress-depth: 100kb
          double-decode-path: no
          double-decode-query: no
    ftp:
      enabled: yes
    ssh:
      enabled: yes
    smtp:
      enabled: yes
      mime:
        decode-mime: yes
        decode-base64: yes
        decode-quoted-printable: yes
        header-value-depth: 2000
        extract-urls: yes
        body-md5: no
    dns:
      tcp:
        enabled: yes
        detection-ports:
          dp: 53
      udp:
        enabled: yes
        detection-ports:
          dp: 53

# ============================================================================
# DETECTION ENGINE
# ============================================================================

# Suricata core
threading:
  set-cpu-affinity: no
  cpu-affinity:
    - management-cpu-set:
        cpu: [ 0 ]
    - receive-cpu-set:
        cpu: [ 0 ]
    - worker-cpu-set:
        cpu: [ "all" ]
        mode: "exclusive"
        prio:
          low: [ 0 ]
          medium: [ "1-2" ]
          high: [ 3 ]
          default: "medium"

# Defrag settings
defrag:
  memcap: 32mb
  hash-size: 65536
  trackers: 65535
  max-frags: 65535
  prealloc: yes
  timeout: 60

# Flow settings
flow:
  memcap: 128mb
  hash-size: 65536
  prealloc: 10000
  emergency-recovery: 30
  managers: 1
  recyclers: 1

# Flow timeouts
flow-timeouts:
  default:
    new: 30
    established: 300
    closed: 0
    bypassed: 100
    emergency-new: 10
    emergency-established: 100
    emergency-closed: 0
    emergency-bypassed: 50
  tcp:
    new: 60
    established: 600
    closed: 60
    bypassed: 100
    emergency-new: 5
    emergency-established: 100
    emergency-closed: 10
    emergency-bypassed: 50
  udp:
    new: 30
    established: 300
    bypassed: 100
    emergency-new: 10
    emergency-established: 100
    emergency-bypassed: 50
  icmp:
    new: 30
    established: 300
    bypassed: 100
    emergency-new: 10
    emergency-established: 100
    emergency-bypassed: 50

# Stream engine settings
stream:
  memcap: 64mb
  checksum-validation: yes
  inline: auto
  reassembly:
    memcap: 256mb
    depth: 1mb
    toserver-chunk-size: 2560
    toclient-chunk-size: 2560
    randomize-chunk-size: yes

# Host table
host:
  hash-size: 4096
  prealloc: 1000
  memcap: 32mb

# ============================================================================
# RULE CONFIGURATION
# ============================================================================

# Suricata rules
default-rule-path: /var/lib/suricata/rules
rule-files:
  - suricata.rules
  - emerging-threats.rules
  - emerging-web_client.rules
  - emerging-web_server.rules
  - emerging-malware.rules
  - emerging-trojan.rules
  - emerging-exploit.rules
  - emerging-scan.rules
  - emerging-dos.rules
  - emerging-attack_response.rules
  - emerging-inappropriate.rules
  - emerging-policy.rules
  - emerging-info.rules
  - emerging-dns.rules
  - emerging-current_events.rules
  - trustvault-custom.rules

classification-file: /var/lib/suricata/rules/classification.config
reference-config-file: /var/lib/suricata/rules/reference.config

# ============================================================================
# VARIABLES
# ============================================================================

vars:
  address-groups:
    HOME_NET: "[***********/16,10.0.0.0/8,**********/12]"
    EXTERNAL_NET: "!$HOME_NET"
    HTTP_SERVERS: "$HOME_NET"
    SMTP_SERVERS: "$HOME_NET"
    SQL_SERVERS: "$HOME_NET"
    DNS_SERVERS: "$HOME_NET"
    TELNET_SERVERS: "$HOME_NET"
    AIM_SERVERS: "$EXTERNAL_NET"
    DC_SERVERS: "$HOME_NET"
    DNP3_SERVER: "$HOME_NET"
    DNP3_CLIENT: "$HOME_NET"
    MODBUS_CLIENT: "$HOME_NET"
    MODBUS_SERVER: "$HOME_NET"
    ENIP_CLIENT: "$HOME_NET"
    ENIP_SERVER: "$HOME_NET"

  port-groups:
    HTTP_PORTS: "80"
    SHELLCODE_PORTS: "!80"
    ORACLE_PORTS: 1521
    SSH_PORTS: 22
    DNP3_PORTS: 20000
    MODBUS_PORTS: 502
    FILE_DATA_PORTS: "[$HTTP_PORTS,110,143]"
    FTP_PORTS: 21
    GENEVE_PORTS: 6081
    VXLAN_PORTS: 4789
    TEREDO_PORTS: 3544

# ============================================================================
# ENGINE ANALYSIS
# ============================================================================

engine-analysis:
  rules-fast-pattern: yes
  rules: yes

# ============================================================================
# PCAP PROCESSING
# ============================================================================

pcap-file:
  checksum-checks: auto

# ============================================================================
# UNIX SOCKET
# ============================================================================

unix-command:
  enabled: auto

# TrustVault - Grafana Datasources Provisioning

apiVersion: 1

datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
    editable: true
    jsonData:
      timeInterval: "5s"
      queryTimeout: "60s"
      httpMethod: "POST"

  - name: Loki
    type: loki
    access: proxy
    url: http://loki:3100
    editable: true
    jsonData:
      maxLines: 1000

  - name: Elasticsearch
    type: elasticsearch
    access: proxy
    url: http://wazuh-indexer:9200
    database: "wazuh-alerts-*"
    editable: true
    jsonData:
      interval: "Daily"
      timeField: "@timestamp"
      esVersion: "7.10.0"
    basicAuth: true
    basicAuthUser: admin
    secureJsonData:
      basicAuthPassword: ${WAZUH_PASSWORD}

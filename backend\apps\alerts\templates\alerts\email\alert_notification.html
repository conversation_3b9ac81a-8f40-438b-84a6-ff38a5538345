<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TrustVault Alert Notification</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #1976d2;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #1976d2;
            margin-bottom: 10px;
        }
        .alert-box {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 20px;
            margin: 20px 0;
        }
        .alert-critical {
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        .alert-info {
            background-color: #d1ecf1;
            border-color: #bee5eb;
        }
        .alert-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #856404;
        }
        .alert-critical .alert-title {
            color: #721c24;
        }
        .alert-info .alert-title {
            color: #0c5460;
        }
        .details-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .details-table th,
        .details-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        .details-table th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #495057;
        }
        .value-highlight {
            font-size: 20px;
            font-weight: bold;
            color: #1976d2;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            text-align: center;
            color: #666;
            font-size: 14px;
        }
        .button {
            display: inline-block;
            padding: 12px 24px;
            background-color: #1976d2;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-weight: bold;
            margin: 20px 0;
        }
        .button:hover {
            background-color: #1565c0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">🛡️ TrustVault</div>
            <h1>Alert Notification</h1>
        </div>

        <div class="alert-box {% if alert.alert_type in 'PRICE_BELOW,PORTFOLIO_VALUE' %}alert-critical{% else %}alert-info{% endif %}">
            <div class="alert-title">
                🚨 {{ alert.name }}
            </div>
            <p>Your alert has been triggered!</p>
        </div>

        <table class="details-table">
            <tr>
                <th>Alert Type</th>
                <td>{{ alert.get_alert_type_display }}</td>
            </tr>
            {% if asset_symbol %}
            <tr>
                <th>Asset</th>
                <td><strong>{{ asset_symbol }}</strong></td>
            </tr>
            {% endif %}
            {% if portfolio_name %}
            <tr>
                <th>Portfolio</th>
                <td><strong>{{ portfolio_name }}</strong></td>
            </tr>
            {% endif %}
            <tr>
                <th>Threshold</th>
                <td>${{ alert.threshold_value|floatformat:2 }}</td>
            </tr>
            <tr>
                <th>Triggered Value</th>
                <td class="value-highlight">${{ triggered_value|floatformat:2 }}</td>
            </tr>
            <tr>
                <th>Triggered At</th>
                <td>{{ alert.triggered_at|date:"F j, Y g:i A" }}</td>
            </tr>
        </table>

        {% if alert.description %}
        <div style="margin: 20px 0;">
            <h3>Description</h3>
            <p>{{ alert.description }}</p>
        </div>
        {% endif %}

        <div style="text-align: center;">
            <a href="https://trustvault.com/dashboard" class="button">
                View Dashboard
            </a>
        </div>

        <div class="footer">
            <p>
                This alert was sent to {{ user.email }} at {{ alert.triggered_at|date:"F j, Y g:i A" }}.
            </p>
            <p>
                To manage your alerts and notification preferences, 
                <a href="https://trustvault.com/settings">visit your settings</a>.
            </p>
            <p>
                <small>
                    TrustVault - Secure Portfolio Management<br>
                    This is an automated message. Please do not reply to this email.
                </small>
            </p>
        </div>
    </div>
</body>
</html>

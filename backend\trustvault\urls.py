# TrustVault - Main URLs Configuration

from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from drf_spectacular.views import SpectacularAPIView, SpectacularSwaggerView, SpectacularRedocView
from apps.core.views import HealthCheckView, APIRootView

urlpatterns = [
    # Admin
    path('admin/', admin.site.urls),
    
    # Health Check
    path('health/', HealthCheckView.as_view(), name='health-check'),
    
    # API Documentation
    path('api/schema/', SpectacularAPIView.as_view(), name='schema'),
    path('api/docs/', SpectacularSwaggerView.as_view(url_name='schema'), name='swagger-ui'),
    path('api/redoc/', SpectacularRedocView.as_view(url_name='schema'), name='redoc'),

    # API Root
    path('api/v1/', APIRootView.as_view(), name='api-root'),

    # API v1
    path('api/v1/auth/', include('apps.authentication.urls')),
    path('api/v1/portfolio/', include('apps.portfolio.urls')),
    path('api/v1/security/', include('apps.security.urls')),
    path('api/v1/core/', include('apps.core.urls')),
    path('api/v1/alerts/', include('apps.alerts.urls')),
    
    # Monitoring endpoints can be added here when needed
]

# Serve media files in development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)

# Custom error handlers
handler400 = 'apps.core.views.bad_request'
handler403 = 'apps.core.views.permission_denied'
handler404 = 'apps.core.views.page_not_found'
handler500 = 'apps.core.views.server_error'

# Admin customization
admin.site.site_header = 'TrustVault Administration'
admin.site.site_title = 'TrustVault Admin'
admin.site.index_title = 'Welcome to TrustVault Administration'

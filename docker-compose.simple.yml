version: '3.8'

networks:
  app:
    driver: bridge

volumes:
  postgres_data:
  redis_data:
  prometheus_data:
  grafana_data:

services:
  # Database
  postgres:
    image: postgres:15-alpine
    container_name: trustvault-postgres-simple
    environment:
      - POSTGRES_DB=trustvault
      - POSTGRES_USER=trustvault
      - POSTGRES_PASSWORD=SecureDBPassword123!TrustVault2024
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - app
    ports:
      - "5432:5432"
    restart: unless-stopped

  # Redis
  redis:
    image: redis:7-alpine
    container_name: trustvault-redis-simple
    command: redis-server --requirepass SecureRedisPassword123!TrustVault2024
    volumes:
      - redis_data:/data
    networks:
      - app
    ports:
      - "6379:6379"
    restart: unless-stopped

  # Django Backend
  django:
    build: ./backend
    container_name: trustvault-django-simple
    environment:
      - DEBUG=False
      - DJANGO_SECRET_KEY=django-secure-production-key-2024-trustvault-change-this
      - DB_PASSWORD=SecureDBPassword123!TrustVault2024
      - REDIS_PASSWORD=SecureRedisPassword123!TrustVault2024
      - POSTGRES_DB=trustvault
      - POSTGRES_USER=trustvault
      - DB_HOST=postgres
      - DB_PORT=5432
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - ALLOWED_HOSTS=localhost,127.0.0.1,testserver,django,trustvault-django-simple
    volumes:
      - ./backend:/app
      - ./logs/django:/app/logs
    networks:
      - app
    ports:
      - "8000:8000"
    depends_on:
      - postgres
      - redis
    restart: unless-stopped

  # Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: trustvault-prometheus-simple
    ports:
      - "9090:9090"
    volumes:
      - prometheus_data:/prometheus
      - ./prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
    networks:
      - app
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    restart: unless-stopped

  # Grafana
  grafana:
    image: grafana/grafana:latest
    container_name: trustvault-grafana-simple
    ports:
      - "3001:3000"
    volumes:
      - grafana_data:/var/lib/grafana
    networks:
      - app
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
    restart: unless-stopped

  # AlertManager (disabled for now)
  # alertmanager:
  #   image: prom/alertmanager:latest
  #   container_name: trustvault-alertmanager-simple
  #   ports:
  #     - "9093:9093"
  #   volumes:
  #     - ./prometheus/alertmanager.yml:/etc/alertmanager/alertmanager.yml
  #   networks:
  #     - app
  #   command:
  #     - '--config.file=/etc/alertmanager/alertmanager.yml'
  #     - '--storage.path=/alertmanager'
  #     - '--web.external-url=http://localhost:9093'
  #   restart: unless-stopped

  # React Frontend
  react:
    build: ./frontend
    container_name: trustvault-react-simple
    ports:
      - "3000:80"
    networks:
      - app
    environment:
      - REACT_APP_API_URL=/api/v1
      - REACT_APP_ENVIRONMENT=production
    restart: unless-stopped

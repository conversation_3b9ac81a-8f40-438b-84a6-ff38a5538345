// TrustVault - Notification Test Page

import React from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  <PERSON>,
  CardContent,
  Button,
  Grid,
  Alert,
  CircularProgress,
} from '@mui/material';
import {
  Notifications,
  Add,
  Refresh,
} from '@mui/icons-material';
import { Helmet } from 'react-helmet-async';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { toast } from 'react-hot-toast';

// Services
import apiService from '../../services/api';

// Hooks
import { useNotifications } from '../../hooks/useNotifications';

const NotificationTestPage: React.FC = () => {
  const queryClient = useQueryClient();
  
  const {
    notifications,
    stats,
    isLoading,
    refetch,
  } = useNotifications();

  // Create test notifications mutation
  const createTestNotificationsMutation = useMutation(
    () => apiService.createTestNotifications(),
    {
      onSuccess: () => {
        toast.success('Test notifications created successfully');
        queryClient.invalidateQueries('notifications');
        refetch();
      },
      onError: (error: any) => {
        const message = error.response?.data?.message || 'Failed to create test notifications';
        toast.error(message);
      },
    }
  );

  const handleCreateTestNotifications = () => {
    createTestNotificationsMutation.mutate();
  };

  const handleRefreshNotifications = () => {
    refetch();
    toast.success('Notifications refreshed');
  };

  return (
    <>
      <Helmet>
        <title>Notification Test - TrustVault</title>
      </Helmet>

      <Box sx={{ p: 3 }}>
        {/* Header */}
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
          <Typography variant="h4" component="h1">
            Notification System Test
          </Typography>
          <Box display="flex" gap={2}>
            <Button
              variant="outlined"
              startIcon={<Refresh />}
              onClick={handleRefreshNotifications}
              disabled={isLoading}
            >
              Refresh
            </Button>
            <Button
              variant="contained"
              startIcon={<Add />}
              onClick={handleCreateTestNotifications}
              disabled={createTestNotificationsMutation.isLoading}
            >
              {createTestNotificationsMutation.isLoading ? (
                <CircularProgress size={20} />
              ) : (
                'Create Test Notifications'
              )}
            </Button>
          </Box>
        </Box>

        {/* Instructions */}
        <Alert severity="info" sx={{ mb: 3 }}>
          <Typography variant="body2">
            This page is for testing the notification system. Click "Create Test Notifications" to generate sample notifications, 
            then check the notification icon in the top navigation bar to see the dropdown with notifications and badge count.
          </Typography>
        </Alert>

        {/* Stats Cards */}
        <Grid container spacing={3} mb={3}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" gap={2}>
                  <Notifications color="primary" />
                  <Box>
                    <Typography color="textSecondary" gutterBottom>
                      Total Notifications
                    </Typography>
                    <Typography variant="h4">
                      {isLoading ? <CircularProgress size={24} /> : stats.total}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" gap={2}>
                  <Notifications color="error" />
                  <Box>
                    <Typography color="textSecondary" gutterBottom>
                      Unread (In-App)
                    </Typography>
                    <Typography variant="h4" color="error.main">
                      {isLoading ? <CircularProgress size={24} /> : stats.unread}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>
                  Email Notifications
                </Typography>
                <Typography variant="h4">
                  {isLoading ? <CircularProgress size={24} /> : (stats.by_channel.EMAIL || 0)}
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>
                  In-App Notifications
                </Typography>
                <Typography variant="h4">
                  {isLoading ? <CircularProgress size={24} /> : (stats.by_channel.IN_APP || 0)}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Notifications List */}
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Recent Notifications
            </Typography>
            
            {isLoading ? (
              <Box display="flex" justifyContent="center" p={3}>
                <CircularProgress />
              </Box>
            ) : notifications.length === 0 ? (
              <Box textAlign="center" py={4}>
                <Notifications sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                <Typography variant="h6" color="text.secondary" gutterBottom>
                  No notifications found
                </Typography>
                <Typography variant="body2" color="text.secondary" mb={3}>
                  Click "Create Test Notifications" to generate sample notifications
                </Typography>
              </Box>
            ) : (
              <Box>
                {notifications.slice(0, 10).map((notification, index) => (
                  <Box
                    key={notification.id}
                    sx={{
                      p: 2,
                      border: 1,
                      borderColor: 'divider',
                      borderRadius: 1,
                      mb: 1,
                      backgroundColor: notification.is_read ? 'transparent' : 'action.hover',
                    }}
                  >
                    <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={1}>
                      <Typography variant="subtitle2" fontWeight="bold">
                        {notification.subject}
                      </Typography>
                      <Box display="flex" gap={1}>
                        <Typography variant="caption" color="text.secondary">
                          {notification.channel}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {notification.status}
                        </Typography>
                      </Box>
                    </Box>
                    <Typography variant="body2" color="text.secondary">
                      {notification.message}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {new Date(notification.created_at).toLocaleString()}
                    </Typography>
                  </Box>
                ))}
              </Box>
            )}
          </CardContent>
        </Card>
      </Box>
    </>
  );
};

export default NotificationTestPage;

#!/bin/bash

# TrustVault - Setup Permissions Script
# ============================================================================

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

echo "🔧 TrustVault - Configuration des Permissions"
echo "============================================="

# Make all scripts executable
echo "📋 Configuration des permissions des scripts..."
chmod +x "$PROJECT_ROOT/scripts"/*.sh

# Set proper permissions for configuration files
echo "📋 Configuration des permissions des fichiers de configuration..."

# Nginx configuration
chmod 644 "$PROJECT_ROOT/nginx/nginx.conf"
chmod 644 "$PROJECT_ROOT/nginx/conf.d"/*.conf
chmod 644 "$PROJECT_ROOT/nginx/security-headers.conf"

# SSL certificates directory (will be created by generate-ssl-certs.sh)
mkdir -p "$PROJECT_ROOT/nginx/ssl"
chmod 700 "$PROJECT_ROOT/nginx/ssl"

# Secrets directory
mkdir -p "$PROJECT_ROOT/secrets"
chmod 700 "$PROJECT_ROOT/secrets"

# Logs directory
mkdir -p "$PROJECT_ROOT/logs"/{nginx,django,celery,modsecurity,suricata}
chmod 755 "$PROJECT_ROOT/logs"
chmod 755 "$PROJECT_ROOT/logs"/*

# Configuration files
chmod 644 "$PROJECT_ROOT/docker-compose.yml"
chmod 600 "$PROJECT_ROOT/.env" 2>/dev/null || true
chmod 644 "$PROJECT_ROOT/.env.example"

# Wazuh configuration
chmod 644 "$PROJECT_ROOT/wazuh/config"/*.conf
chmod 644 "$PROJECT_ROOT/wazuh/config"/*.xml

# Suricata configuration
chmod 644 "$PROJECT_ROOT/suricata/config"/*.yaml
chmod 644 "$PROJECT_ROOT/suricata/rules"/*.rules

# Prometheus configuration
chmod 644 "$PROJECT_ROOT/prometheus"/*.yml

# Vault configuration
chmod 644 "$PROJECT_ROOT/vault/config"/*.hcl

# Redis configuration
chmod 644 "$PROJECT_ROOT/redis/redis.conf"

# Fail2Ban configuration
chmod 644 "$PROJECT_ROOT/fail2ban/jail.local"
chmod 644 "$PROJECT_ROOT/fail2ban/filter.d"/*.conf

# PostgreSQL initialization scripts
chmod 644 "$PROJECT_ROOT/postgres/init"/*.sql

# Documentation
chmod 644 "$PROJECT_ROOT/docs"/*.md
chmod 644 "$PROJECT_ROOT/README.md"

echo "✅ Permissions configurées avec succès!"
echo ""
echo "📁 Structure des permissions:"
echo "   - Scripts: 755 (exécutable)"
echo "   - Configurations: 644 (lecture/écriture propriétaire, lecture groupe/autres)"
echo "   - Secrets: 700 (accès propriétaire uniquement)"
echo "   - SSL: 700 (accès propriétaire uniquement)"
echo "   - Logs: 755 (écriture pour les services)"
echo ""
echo "🚀 Prêt pour le déploiement!"
echo "   Exécutez: ./scripts/deploy.sh deploy"

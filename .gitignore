# TrustVault - Optimized .gitignore

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Django
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal
media/
staticfiles/
static/

# Environment variables
.env
.env.local
.env.development
.env.production
.venv/
venv/
env/
ENV/

# Testing
.coverage
.pytest_cache/
htmlcov/
.tox/
.nox/
coverage.xml
*.cover
.hypothesis/
.cache

# Node.js & React
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
frontend/build/

# IDE & Editors
.vscode/
.idea/
*.swp
*.swo
*~
.*.swp
.*.swo

# OS
.DS_Store
Thumbs.db
*.tmp
*.temp

# Docker
.dockerignore

# Logs & Monitoring
logs/
*.log
monitoring/
.monitoring/

# Security & Certificates
*.key
*.pem
*.crt
*.p12
*.pfx
secrets/
.secrets/
certificates/
.certificates/

# Database
*.db
*.sqlite
*.sqlite3

# Backups
backups/
.backups/
*.bak
*.backup

# Cache
.cache/
*.cache

# Documentation
docs/_build/

# Deployment
deployment/
.deploy/

# Package files
*.tar.gz
*.zip
*.rar

# Jupyter
.ipynb_checkpoints/

# Celery
celerybeat-schedule
celerybeat.pid

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre
.pyre/

# Test results
security_api_test_results.json
security_validation_report.json

# Temporary test files
test_*.py
*_test.py

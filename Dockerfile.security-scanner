# TrustVault Security Scanner
FROM python:3.11-alpine

# Install security tools
RUN apk add --no-cache \
    nmap \
    curl \
    openssl \
    bash \
    jq

# Install Python packages
RUN pip install --no-cache-dir \
    requests \
    python-nmap \
    cryptography \
    pyyaml

# Create scanner user
RUN addgroup -g 1001 scanner && \
    adduser -D -u 1001 -G scanner scanner

# Create directories
RUN mkdir -p /app /reports /logs
WORKDIR /app

# Copy scanner scripts
COPY security_validation_comprehensive.py /app/
COPY scripts/security-scan.sh /app/

# Set permissions
RUN chown -R scanner:scanner /app /reports
USER scanner

# Health check
HEALTHCHECK --interval=300s --timeout=30s --start-period=10s --retries=3 \
    CMD python /app/security_validation_comprehensive.py || exit 1

# Default command
CMD ["python", "/app/security_validation_comprehensive.py"]

# TrustVault - Alerts Tests

from django.test import TestCase
from django.contrib.auth import get_user_model
from django.urls import reverse
from django.utils import timezone
from rest_framework.test import APITestCase
from rest_framework import status
from decimal import Decimal
from datetime import timed<PERSON><PERSON>

from .models import (
    PriceAlert, AlertHistory, NotificationPreference,
    Notification, Report, AlertType, AlertStatus
)
from .services import NotificationService, AlertProcessor
from apps.portfolio.models import Portfolio, Asset

User = get_user_model()


class PriceAlertModelTest(TestCase):
    """Test PriceAlert model."""

    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            first_name='Test',
            last_name='User'
        )
        self.asset = Asset.objects.create(
            symbol='AAPL',
            name='Apple Inc.',
            asset_type='STOCK',
            current_price=Decimal('150.00')
        )
        self.portfolio = Portfolio.objects.create(
            user=self.user,
            name='Test Portfolio',
            description='Test portfolio'
        )

    def test_create_price_alert(self):
        """Test creating a price alert."""
        alert = PriceAlert.objects.create(
            user=self.user,
            asset=self.asset,
            alert_type=AlertType.PRICE_ABOVE,
            threshold_value=Decimal('160.00'),
            name='AAPL Above $160',
            notification_channels=['EMAIL']
        )

        self.assertEqual(alert.user, self.user)
        self.assertEqual(alert.asset, self.asset)
        self.assertEqual(alert.alert_type, AlertType.PRICE_ABOVE)
        self.assertEqual(alert.threshold_value, Decimal('160.00'))
        self.assertEqual(alert.status, AlertStatus.ACTIVE)
        self.assertTrue(alert.can_trigger())

    def test_alert_expiration(self):
        """Test alert expiration logic."""
        # Create expired alert
        alert = PriceAlert.objects.create(
            user=self.user,
            asset=self.asset,
            alert_type=AlertType.PRICE_ABOVE,
            threshold_value=Decimal('160.00'),
            name='Expired Alert',
            expires_at=timezone.now() - timedelta(hours=1)
        )

        self.assertTrue(alert.is_expired())
        self.assertFalse(alert.can_trigger())

    def test_alert_trigger_limit(self):
        """Test alert trigger limit."""
        alert = PriceAlert.objects.create(
            user=self.user,
            asset=self.asset,
            alert_type=AlertType.PRICE_ABOVE,
            threshold_value=Decimal('160.00'),
            name='Limited Alert',
            max_triggers=1
        )

        # Trigger the alert
        alert.trigger()

        self.assertEqual(alert.trigger_count, 1)
        self.assertEqual(alert.status, AlertStatus.TRIGGERED)
        self.assertFalse(alert.can_trigger())

    def test_alert_cooldown(self):
        """Test alert cooldown period."""
        alert = PriceAlert.objects.create(
            user=self.user,
            asset=self.asset,
            alert_type=AlertType.PRICE_ABOVE,
            threshold_value=Decimal('160.00'),
            name='Cooldown Alert',
            max_triggers=5,
            cooldown_minutes=60
        )

        # Trigger the alert
        alert.trigger()

        # Should not be able to trigger again due to cooldown
        self.assertFalse(alert.can_trigger())

// TrustVault - TypeScript Type Definitions

export interface User {
  id: string;
  email: string;
  username: string;
  first_name: string;
  last_name: string;
  phone_number?: string;
  profile_picture?: string;
  timezone: string;
  language: string;
  is_mfa_enabled: boolean;
  last_login?: string;
  last_password_change?: string;
  date_joined: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
  remember_me?: boolean;
}

export interface RegisterData {
  email: string;
  username: string;
  first_name: string;
  last_name: string;
  phone_number?: string;
  password: string;
  password_confirm: string;
  gdpr_consent: boolean;
  marketing_consent?: boolean;
}

export interface AuthTokens {
  access_token: string;
  refresh_token: string;
  expires_in: number;
}

export interface AuthResponse {
  access_token: string;
  refresh_token: string;
  user: User;
  expires_in: number;
  mfa_required?: boolean;
  user_id?: string;
}

export interface Portfolio {
  id: string;
  name: string;
  description: string;
  portfolio_type: 'CONSERVATIVE' | 'MODERATE' | 'AGGRESSIVE' | 'CUSTOM';
  total_value: string;
  currency: string;
  is_public: boolean;
  holdings?: Holding[];
  holdings_count?: number;
  performance_24h?: {
    change: string;
    change_percentage: string;
  };
  created_at: string;
  updated_at: string;
}

export interface Asset {
  id: string;
  symbol: string;
  name: string;
  asset_type: 'STOCK' | 'BOND' | 'ETF' | 'MUTUAL_FUND' | 'CRYPTO' | 'COMMODITY' | 'REAL_ESTATE' | 'CASH';
  description: string;
  current_price: string;
  currency: string;
  market_cap?: string;
  volume_24h?: string;
  change_24h?: string;
  sector?: string;
  country?: string;
  created_at: string;
  updated_at: string;
}

export interface Holding {
  id: string;
  asset: Asset;
  quantity: string;
  average_cost: string;
  current_value: string;
  profit_loss: string;
  profit_loss_percentage: string;
  created_at: string;
  updated_at: string;
}

export interface Transaction {
  id: string;
  asset?: Asset;
  transaction_type: 'BUY' | 'SELL' | 'DIVIDEND' | 'SPLIT' | 'DEPOSIT' | 'WITHDRAWAL';
  quantity?: string;
  price?: string;
  total_amount: string;
  fees: string;
  notes: string;
  transaction_date: string;
  created_at: string;
  updated_at: string;
}

export interface SecurityEvent {
  id: string;
  event_type: string;
  risk_level: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  source_ip: string;
  user_agent: string;
  description: string;
  details: Record<string, any>;
  is_resolved: boolean;
  resolved_at?: string;
  created_at: string;
}

export interface AuditLog {
  id: string;
  user?: {
    id: string;
    email: string;
    name: string;
  };
  action: string;
  resource_type: string;
  resource_id?: string;
  ip_address: string;
  user_agent: string;
  details: Record<string, any>;
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  timestamp: string;
}

export interface ApiError {
  message: string;
  errors?: Record<string, string[]>;
  status?: number;
}

export interface PaginatedResponse<T> {
  count: number;
  next?: string;
  previous?: string;
  results: T[];
}

export interface SecurityDashboard {
  security_events: {
    total_events: number;
    last_24h: number;
    last_7d: number;
    unresolved: number;
    critical: number;
    high: number;
  };
  login_attempts: {
    total_attempts: number;
    failed_last_24h: number;
    successful_last_24h: number;
    suspicious_last_24h: number;
  };
  recent_events: SecurityEvent[];
  threat_sources: Array<{
    source_ip: string;
    count: number;
  }>;
  generated_at: string;
}

export interface ChangePasswordData {
  current_password: string;
  new_password: string;
  new_password_confirm: string;
}

export interface MFASetupData {
  token: string;
}

// Form validation schemas
export interface FormErrors {
  [key: string]: string | undefined;
}

// Chart data types
export interface ChartDataPoint {
  date: string;
  value: number;
  label?: string;
}

export interface PieChartData {
  name: string;
  value: number;
  color?: string;
}

// Theme types
export interface ThemeMode {
  mode: 'light' | 'dark';
}

// Navigation types
export interface NavItem {
  label: string;
  path: string;
  icon?: React.ComponentType;
  children?: NavItem[];
  requiresAuth?: boolean;
  roles?: string[];
}

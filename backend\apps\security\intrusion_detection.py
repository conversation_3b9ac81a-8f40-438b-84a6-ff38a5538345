"""
TrustVault - Advanced Intrusion Detection System (IDS)

This module implements a comprehensive intrusion detection system including:
- Real-time threat detection and analysis
- Behavioral anomaly detection using machine learning
- Network traffic analysis
- SQL injection and XSS detection
- Brute force attack detection
- Advanced persistent threat (APT) detection
- Automated incident response
"""

import re
import json
import hashlib
import statistics
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from collections import defaultdict, deque
from django.utils import timezone
from django.core.cache import cache
from django.conf import settings
from django.db.models import Count, Q
import logging

from .models import SecurityEvent, ThreatIntelligence, IncidentResponse
from .advanced_crypto import crypto

logger = logging.getLogger(__name__)

class IntrusionDetectionSystem:
    """Advanced Intrusion Detection System for TrustVault"""
    
    def __init__(self):
        self.threat_patterns = self._load_threat_patterns()
        self.anomaly_threshold = 0.7
        self.max_requests_per_minute = 100
        self.max_failed_logins = 5
        self.suspicious_user_agents = self._load_suspicious_user_agents()
        
    def analyze_request(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Comprehensive request analysis for threat detection
        """
        threats_detected = []
        risk_score = 0
        
        # SQL Injection Detection
        sql_threat = self._detect_sql_injection(request_data)
        if sql_threat['detected']:
            threats_detected.append(sql_threat)
            risk_score += sql_threat['severity']
        
        # XSS Detection
        xss_threat = self._detect_xss(request_data)
        if xss_threat['detected']:
            threats_detected.append(xss_threat)
            risk_score += xss_threat['severity']
        
        # Command Injection Detection
        cmd_threat = self._detect_command_injection(request_data)
        if cmd_threat['detected']:
            threats_detected.append(cmd_threat)
            risk_score += cmd_threat['severity']
        
        # Path Traversal Detection
        path_threat = self._detect_path_traversal(request_data)
        if path_threat['detected']:
            threats_detected.append(path_threat)
            risk_score += path_threat['severity']
        
        # Rate Limiting Analysis
        rate_threat = self._detect_rate_limiting_abuse(request_data)
        if rate_threat['detected']:
            threats_detected.append(rate_threat)
            risk_score += rate_threat['severity']
        
        # Suspicious User Agent Detection
        ua_threat = self._detect_suspicious_user_agent(request_data)
        if ua_threat['detected']:
            threats_detected.append(ua_threat)
            risk_score += ua_threat['severity']
        
        # Behavioral Anomaly Detection
        behavior_threat = self._detect_behavioral_anomaly(request_data)
        if behavior_threat['detected']:
            threats_detected.append(behavior_threat)
            risk_score += behavior_threat['severity']
        
        # Determine overall threat level
        threat_level = self._calculate_threat_level(risk_score)
        
        analysis_result = {
            'timestamp': timezone.now().isoformat(),
            'threats_detected': threats_detected,
            'risk_score': risk_score,
            'threat_level': threat_level,
            'requires_action': threat_level in ['HIGH', 'CRITICAL'],
            'recommended_actions': self._get_recommended_actions(threats_detected, threat_level)
        }
        
        # Log security event if threats detected
        if threats_detected:
            self._log_intrusion_attempt(request_data, analysis_result)
        
        # Trigger automated response if necessary
        if threat_level == 'CRITICAL':
            self._trigger_automated_response(request_data, analysis_result)
        
        return analysis_result
    
    def _detect_sql_injection(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """Detect SQL injection attempts"""
        sql_patterns = [
            r"(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)",
            r"(\b(OR|AND)\s+\d+\s*=\s*\d+)",
            r"(\b(OR|AND)\s+['\"]?\w+['\"]?\s*=\s*['\"]?\w+['\"]?)",
            r"(--|#|/\*|\*/)",
            r"(\bUNION\s+(ALL\s+)?SELECT\b)",
            r"(\b(INFORMATION_SCHEMA|SYSOBJECTS|SYSCOLUMNS)\b)",
            r"(\b(WAITFOR\s+DELAY|BENCHMARK|SLEEP)\b)",
            r"(\b(LOAD_FILE|INTO\s+OUTFILE|INTO\s+DUMPFILE)\b)"
        ]
        
        suspicious_content = []
        for key, value in request_data.items():
            if isinstance(value, str):
                for pattern in sql_patterns:
                    if re.search(pattern, value, re.IGNORECASE):
                        suspicious_content.append({
                            'field': key,
                            'pattern': pattern,
                            'value': value[:100]  # Truncate for logging
                        })
        
        if suspicious_content:
            return {
                'detected': True,
                'type': 'SQL_INJECTION',
                'severity': 80,
                'description': 'SQL injection attempt detected',
                'evidence': suspicious_content
            }
        
        return {'detected': False}
    
    def _detect_xss(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """Detect Cross-Site Scripting (XSS) attempts"""
        xss_patterns = [
            r"<script[^>]*>.*?</script>",
            r"javascript:",
            r"on\w+\s*=",
            r"<iframe[^>]*>",
            r"<object[^>]*>",
            r"<embed[^>]*>",
            r"<link[^>]*>",
            r"<meta[^>]*>",
            r"vbscript:",
            r"expression\s*\(",
            r"@import",
            r"<svg[^>]*onload"
        ]
        
        suspicious_content = []
        for key, value in request_data.items():
            if isinstance(value, str):
                for pattern in xss_patterns:
                    if re.search(pattern, value, re.IGNORECASE):
                        suspicious_content.append({
                            'field': key,
                            'pattern': pattern,
                            'value': value[:100]
                        })
        
        if suspicious_content:
            return {
                'detected': True,
                'type': 'XSS',
                'severity': 70,
                'description': 'Cross-site scripting attempt detected',
                'evidence': suspicious_content
            }
        
        return {'detected': False}
    
    def _detect_command_injection(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """Detect command injection attempts"""
        cmd_patterns = [
            r"[;&|`$(){}[\]\\]",
            r"\b(cat|ls|pwd|whoami|id|uname|ps|netstat|ifconfig|ping|wget|curl)\b",
            r"(&&|\|\|)",
            r"(\$\(|\`)",
            r"(>|>>|<|<<)",
            r"\b(rm|mv|cp|chmod|chown|kill|killall)\b"
        ]
        
        suspicious_content = []
        for key, value in request_data.items():
            if isinstance(value, str):
                for pattern in cmd_patterns:
                    if re.search(pattern, value, re.IGNORECASE):
                        suspicious_content.append({
                            'field': key,
                            'pattern': pattern,
                            'value': value[:100]
                        })
        
        if suspicious_content:
            return {
                'detected': True,
                'type': 'COMMAND_INJECTION',
                'severity': 85,
                'description': 'Command injection attempt detected',
                'evidence': suspicious_content
            }
        
        return {'detected': False}
    
    def _detect_path_traversal(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """Detect path traversal attempts"""
        path_patterns = [
            r"\.\.[\\/]",
            r"[\\/]\.\.[\\/]",
            r"%2e%2e[\\/]",
            r"[\\/]%2e%2e[\\/]",
            r"\.\.%2f",
            r"%2e%2e%2f",
            r"\.\.\\",
            r"%2e%2e%5c"
        ]
        
        suspicious_content = []
        for key, value in request_data.items():
            if isinstance(value, str):
                for pattern in path_patterns:
                    if re.search(pattern, value, re.IGNORECASE):
                        suspicious_content.append({
                            'field': key,
                            'pattern': pattern,
                            'value': value[:100]
                        })
        
        if suspicious_content:
            return {
                'detected': True,
                'type': 'PATH_TRAVERSAL',
                'severity': 75,
                'description': 'Path traversal attempt detected',
                'evidence': suspicious_content
            }
        
        return {'detected': False}
    
    def _detect_rate_limiting_abuse(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """Detect rate limiting abuse and potential DDoS"""
        ip_address = request_data.get('ip_address', '')
        if not ip_address:
            return {'detected': False}
        
        # Track requests per IP
        cache_key = f"requests:{ip_address}"
        current_requests = cache.get(cache_key, [])
        current_time = timezone.now()
        
        # Remove requests older than 1 minute
        current_requests = [req_time for req_time in current_requests 
                          if (current_time - datetime.fromisoformat(req_time)).seconds < 60]
        
        current_requests.append(current_time.isoformat())
        cache.set(cache_key, current_requests, timeout=60)
        
        if len(current_requests) > self.max_requests_per_minute:
            return {
                'detected': True,
                'type': 'RATE_LIMITING_ABUSE',
                'severity': 60,
                'description': f'Excessive requests detected: {len(current_requests)} requests in 1 minute',
                'evidence': {
                    'ip_address': ip_address,
                    'request_count': len(current_requests),
                    'time_window': '1 minute'
                }
            }
        
        return {'detected': False}
    
    def _detect_suspicious_user_agent(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """Detect suspicious user agents"""
        user_agent = request_data.get('user_agent', '').lower()
        
        for suspicious_ua in self.suspicious_user_agents:
            if suspicious_ua in user_agent:
                return {
                    'detected': True,
                    'type': 'SUSPICIOUS_USER_AGENT',
                    'severity': 40,
                    'description': 'Suspicious user agent detected',
                    'evidence': {
                        'user_agent': user_agent,
                        'matched_pattern': suspicious_ua
                    }
                }
        
        # Check for empty or very short user agents
        if len(user_agent) < 10:
            return {
                'detected': True,
                'type': 'SUSPICIOUS_USER_AGENT',
                'severity': 30,
                'description': 'Suspicious or missing user agent',
                'evidence': {'user_agent': user_agent}
            }
        
        return {'detected': False}
    
    def _detect_behavioral_anomaly(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """Detect behavioral anomalies using statistical analysis"""
        user_id = request_data.get('user_id')
        if not user_id:
            return {'detected': False}
        
        # Analyze request patterns
        cache_key = f"behavior:{user_id}"
        behavior_data = cache.get(cache_key, {
            'request_times': [],
            'endpoints': [],
            'request_sizes': []
        })
        
        current_time = timezone.now()
        endpoint = request_data.get('endpoint', '')
        request_size = len(str(request_data))
        
        # Update behavior data
        behavior_data['request_times'].append(current_time.isoformat())
        behavior_data['endpoints'].append(endpoint)
        behavior_data['request_sizes'].append(request_size)
        
        # Keep only recent data (last hour)
        cutoff_time = current_time - timedelta(hours=1)
        behavior_data['request_times'] = [
            req_time for req_time in behavior_data['request_times']
            if datetime.fromisoformat(req_time) > cutoff_time
        ]
        
        # Limit data size
        for key in behavior_data:
            if len(behavior_data[key]) > 100:
                behavior_data[key] = behavior_data[key][-100:]
        
        cache.set(cache_key, behavior_data, timeout=3600)
        
        # Analyze for anomalies
        anomalies = []
        
        # Check request frequency anomaly
        if len(behavior_data['request_times']) > 10:
            recent_requests = len([
                req_time for req_time in behavior_data['request_times']
                if (current_time - datetime.fromisoformat(req_time)).seconds < 300
            ])
            
            if recent_requests > 50:  # More than 50 requests in 5 minutes
                anomalies.append('High request frequency')
        
        # Check endpoint diversity anomaly
        if len(behavior_data['endpoints']) > 20:
            unique_endpoints = len(set(behavior_data['endpoints'][-20:]))
            if unique_endpoints > 15:  # Accessing many different endpoints
                anomalies.append('High endpoint diversity')
        
        # Check request size anomaly
        if len(behavior_data['request_sizes']) > 10:
            avg_size = statistics.mean(behavior_data['request_sizes'])
            if request_size > avg_size * 3:  # Request much larger than average
                anomalies.append('Unusually large request')
        
        if anomalies:
            return {
                'detected': True,
                'type': 'BEHAVIORAL_ANOMALY',
                'severity': 50,
                'description': 'Behavioral anomaly detected',
                'evidence': {
                    'anomalies': anomalies,
                    'user_id': user_id
                }
            }
        
        return {'detected': False}
    
    def _calculate_threat_level(self, risk_score: int) -> str:
        """Calculate overall threat level based on risk score"""
        if risk_score >= 200:
            return 'CRITICAL'
        elif risk_score >= 150:
            return 'HIGH'
        elif risk_score >= 100:
            return 'MEDIUM'
        elif risk_score >= 50:
            return 'LOW'
        else:
            return 'MINIMAL'
    
    def _get_recommended_actions(self, threats: List[Dict], threat_level: str) -> List[str]:
        """Get recommended actions based on detected threats"""
        actions = []
        
        if threat_level == 'CRITICAL':
            actions.extend([
                'Block IP address immediately',
                'Terminate user session',
                'Alert security team',
                'Initiate incident response procedure'
            ])
        elif threat_level == 'HIGH':
            actions.extend([
                'Increase monitoring for this IP/user',
                'Require additional authentication',
                'Log detailed request information'
            ])
        elif threat_level == 'MEDIUM':
            actions.extend([
                'Monitor closely',
                'Log request for analysis'
            ])
        
        # Specific actions based on threat types
        threat_types = [threat['type'] for threat in threats]
        
        if 'SQL_INJECTION' in threat_types:
            actions.append('Review database query logs')
        
        if 'XSS' in threat_types:
            actions.append('Check for stored XSS payloads')
        
        if 'RATE_LIMITING_ABUSE' in threat_types:
            actions.append('Implement temporary rate limiting')
        
        return list(set(actions))  # Remove duplicates
    
    def _log_intrusion_attempt(self, request_data: Dict, analysis_result: Dict):
        """Log intrusion attempt to security events"""
        SecurityEvent.objects.create(
            user_id=request_data.get('user_id'),
            action='INTRUSION_ATTEMPT',
            ip_address=request_data.get('ip_address', ''),
            user_agent=request_data.get('user_agent', ''),
            risk_level=analysis_result['threat_level'],
            details={
                'threats_detected': analysis_result['threats_detected'],
                'risk_score': analysis_result['risk_score'],
                'endpoint': request_data.get('endpoint', ''),
                'method': request_data.get('method', '')
            }
        )
    
    def _trigger_automated_response(self, request_data: Dict, analysis_result: Dict):
        """Trigger automated response for critical threats"""
        ip_address = request_data.get('ip_address', '')
        
        # Block IP address
        if ip_address:
            cache.set(f"blocked_ip:{ip_address}", True, timeout=3600)  # Block for 1 hour
        
        # Create incident response record
        IncidentResponse.objects.create(
            incident_type='AUTOMATED_THREAT_RESPONSE',
            severity=analysis_result['threat_level'],
            description=f"Automated response to {analysis_result['threat_level']} threat",
            affected_systems=['web_application'],
            response_actions=analysis_result['recommended_actions'],
            status='IN_PROGRESS',
            details=analysis_result
        )
        
        logger.critical(f"Critical threat detected and automated response triggered: {analysis_result}")
    
    def _load_threat_patterns(self) -> Dict[str, List[str]]:
        """Load threat patterns from threat intelligence"""
        # This would load from ThreatIntelligence model or external feeds
        return {
            'malicious_ips': [],
            'known_attack_signatures': [],
            'suspicious_domains': []
        }
    
    def _load_suspicious_user_agents(self) -> List[str]:
        """Load list of suspicious user agents"""
        return [
            'sqlmap',
            'nikto',
            'nmap',
            'masscan',
            'zap',
            'burp',
            'w3af',
            'havij',
            'pangolin',
            'acunetix',
            'netsparker',
            'appscan',
            'websecurify'
        ]


# Global instance
ids = IntrusionDetectionSystem()

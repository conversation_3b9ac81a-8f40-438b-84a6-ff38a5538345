# 🔍 TrustVault - Guide de Tests de Pénétration

## Vue d'ensemble

Ce guide détaille les procédures de tests de sécurité pour valider l'efficacité des mesures de protection de TrustVault. Les tests couvrent l'ensemble de la surface d'attaque selon la méthodologie OWASP.

## 🎯 Méthodologie de Test

### Phases de Test
1. **Reconnaissance** : Collecte d'informations
2. **Énumération** : Identification des services
3. **Exploitation** : Tests d'intrusion
4. **Post-exploitation** : Évaluation des impacts
5. **Reporting** : Documentation des résultats

### Standards Utilisés
- **OWASP Testing Guide v4.2**
- **NIST SP 800-115**
- **PTES (Penetration Testing Execution Standard)**
- **MITRE ATT&CK Framework**

## 🛠️ Outils de Test

### Scanners de Vulnérabilités
```bash
# OWASP ZAP - Scanner automatisé
docker run -t owasp/zap2docker-stable zap-baseline.py \
    -t https://trustvault.local

# Nikto - Scanner web
nikto -h https://trustvault.local -ssl

# Nmap - Scan de ports et services
nmap -sS -sV -O -A trustvault.local
```

### Tests d'Injection
```bash
# SQLMap - Tests d'injection SQL
sqlmap -u "https://api.trustvault.local/api/v1/login" \
    --data="username=admin&password=test" \
    --method=POST --batch

# XSStrike - Tests XSS
python3 xsstrike.py -u "https://trustvault.local/search?q=test"
```

### Tests d'Authentification
```bash
# Hydra - Attaques par force brute
hydra -l admin -P /usr/share/wordlists/rockyou.txt \
    trustvault.local https-post-form \
    "/api/v1/auth/login:username=^USER^&password=^PASS^:Invalid"

# Medusa - Tests de mots de passe
medusa -h trustvault.local -u admin -P passwords.txt \
    -M http -m DIR:/api/v1/auth/login
```

## 🔐 Tests de Sécurité par Catégorie

### 1. Tests d'Authentification et Session

#### Test de Force Brute
```python
#!/usr/bin/env python3
import requests
import time

def test_brute_force():
    url = "https://api.trustvault.local/api/v1/auth/login"
    passwords = ["admin", "password", "123456", "trustvault"]
    
    for password in passwords:
        data = {"username": "admin", "password": password}
        response = requests.post(url, json=data, verify=False)
        
        print(f"Password: {password} - Status: {response.status_code}")
        
        # Vérifier le rate limiting
        if response.status_code == 429:
            print("✅ Rate limiting détecté")
            break
        
        time.sleep(1)

test_brute_force()
```

#### Test de Session Fixation
```python
def test_session_fixation():
    session = requests.Session()
    
    # Obtenir un token de session
    response = session.get("https://trustvault.local")
    initial_cookies = session.cookies
    
    # Tenter de se connecter avec le même token
    login_data = {"username": "testuser", "password": "testpass"}
    response = session.post("https://api.trustvault.local/api/v1/auth/login", 
                          json=login_data, verify=False)
    
    # Vérifier si les cookies ont changé
    if session.cookies != initial_cookies:
        print("✅ Protection contre session fixation active")
    else:
        print("❌ Vulnérabilité de session fixation détectée")
```

### 2. Tests d'Injection

#### Test d'Injection SQL
```bash
# Tests manuels d'injection SQL
curl -k -X POST "https://api.trustvault.local/api/v1/portfolio" \
    -H "Content-Type: application/json" \
    -d '{"name": "test'\'' OR 1=1--", "description": "test"}'

# Test avec SQLMap
sqlmap -u "https://api.trustvault.local/api/v1/portfolio/1" \
    --cookie="sessionid=your_session_id" \
    --batch --level=5 --risk=3
```

#### Test XSS (Cross-Site Scripting)
```javascript
// Payloads XSS à tester
const xssPayloads = [
    '<script>alert("XSS")</script>',
    '"><script>alert("XSS")</script>',
    'javascript:alert("XSS")',
    '<img src=x onerror=alert("XSS")>',
    '<svg onload=alert("XSS")>'
];

// Test automatisé
xssPayloads.forEach(payload => {
    fetch('https://api.trustvault.local/api/v1/search', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({query: payload})
    }).then(response => {
        console.log(`Payload: ${payload} - Status: ${response.status}`);
    });
});
```

### 3. Tests de Contrôle d'Accès

#### Test d'Escalade de Privilèges
```python
def test_privilege_escalation():
    # Connexion avec un utilisateur normal
    login_data = {"username": "user", "password": "userpass"}
    response = requests.post("https://api.trustvault.local/api/v1/auth/login", 
                           json=login_data, verify=False)
    
    token = response.json().get("access_token")
    headers = {"Authorization": f"Bearer {token}"}
    
    # Tenter d'accéder aux fonctions admin
    admin_endpoints = [
        "/api/v1/admin/users",
        "/api/v1/admin/settings",
        "/api/v1/admin/logs"
    ]
    
    for endpoint in admin_endpoints:
        response = requests.get(f"https://api.trustvault.local{endpoint}", 
                              headers=headers, verify=False)
        
        if response.status_code == 200:
            print(f"❌ Accès non autorisé à {endpoint}")
        else:
            print(f"✅ Accès correctement refusé à {endpoint}")
```

#### Test IDOR (Insecure Direct Object Reference)
```python
def test_idor():
    # Connexion utilisateur 1
    user1_token = get_user_token("user1", "pass1")
    
    # Créer un portfolio pour user1
    portfolio_data = {"name": "Portfolio User1", "private": True}
    response = requests.post("https://api.trustvault.local/api/v1/portfolio", 
                           json=portfolio_data, 
                           headers={"Authorization": f"Bearer {user1_token}"},
                           verify=False)
    
    portfolio_id = response.json().get("id")
    
    # Connexion utilisateur 2
    user2_token = get_user_token("user2", "pass2")
    
    # Tenter d'accéder au portfolio de user1
    response = requests.get(f"https://api.trustvault.local/api/v1/portfolio/{portfolio_id}", 
                          headers={"Authorization": f"Bearer {user2_token}"},
                          verify=False)
    
    if response.status_code == 200:
        print("❌ Vulnérabilité IDOR détectée")
    else:
        print("✅ Protection IDOR active")
```

### 4. Tests de Sécurité Réseau

#### Scan de Ports et Services
```bash
# Scan complet des ports
nmap -p- -sS -sV -O -A trustvault.local

# Test des chiffrements SSL/TLS
nmap --script ssl-enum-ciphers -p 443 trustvault.local

# Test des vulnérabilités SSL
nmap --script ssl-* -p 443 trustvault.local
```

#### Test de Configuration SSL
```bash
# Utilisation de testssl.sh
./testssl.sh https://trustvault.local

# Vérification des certificats
openssl s_client -connect trustvault.local:443 -servername trustvault.local
```

### 5. Tests de Déni de Service (DoS)

#### Test de Charge
```python
import threading
import requests
import time

def dos_test():
    def send_request():
        try:
            response = requests.get("https://trustvault.local", 
                                  timeout=5, verify=False)
            print(f"Status: {response.status_code}")
        except:
            print("Request failed")
    
    # Lancer 100 requêtes simultanées
    threads = []
    for i in range(100):
        thread = threading.Thread(target=send_request)
        threads.append(thread)
        thread.start()
    
    for thread in threads:
        thread.join()

dos_test()
```

#### Test Slowloris
```bash
# Utilisation de slowhttptest
slowhttptest -c 1000 -H -g -o slowloris_test \
    -i 10 -r 200 -t GET -u https://trustvault.local
```

## 📊 Tests Automatisés

### Script de Test Complet
```bash
#!/bin/bash
# test-security.sh

echo "🔍 Démarrage des tests de sécurité TrustVault"

# 1. Scan de ports
echo "📡 Scan de ports..."
nmap -sS -sV trustvault.local > nmap_results.txt

# 2. Test SSL/TLS
echo "🔐 Test SSL/TLS..."
./testssl.sh https://trustvault.local > ssl_results.txt

# 3. Scan de vulnérabilités web
echo "🌐 Scan web..."
nikto -h https://trustvault.local -ssl > nikto_results.txt

# 4. Test OWASP ZAP
echo "🛡️ Test OWASP ZAP..."
docker run -t owasp/zap2docker-stable zap-baseline.py \
    -t https://trustvault.local > zap_results.txt

# 5. Test d'injection SQL
echo "💉 Test SQL Injection..."
sqlmap -u "https://api.trustvault.local/api/v1/search?q=test" \
    --batch --level=3 > sqlmap_results.txt

echo "✅ Tests terminés. Vérifiez les fichiers de résultats."
```

### Tests d'Intégration Continue
```yaml
# .github/workflows/security-tests.yml
name: Security Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  security-tests:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Start TrustVault
      run: |
        docker-compose up -d
        sleep 60
    
    - name: Run OWASP ZAP
      run: |
        docker run -t owasp/zap2docker-stable zap-baseline.py \
          -t http://localhost
    
    - name: Run Security Tests
      run: |
        python3 security_tests.py
    
    - name: Upload Results
      uses: actions/upload-artifact@v2
      with:
        name: security-test-results
        path: test-results/
```

## 📋 Checklist de Validation

### Tests Obligatoires
- [ ] **Authentification**
  - [ ] Force brute protection
  - [ ] Session management
  - [ ] MFA validation
  - [ ] Password policy

- [ ] **Autorisation**
  - [ ] RBAC enforcement
  - [ ] IDOR protection
  - [ ] Privilege escalation

- [ ] **Injection**
  - [ ] SQL injection
  - [ ] XSS protection
  - [ ] Command injection
  - [ ] LDAP injection

- [ ] **Chiffrement**
  - [ ] TLS configuration
  - [ ] Data encryption
  - [ ] Key management
  - [ ] Certificate validation

- [ ] **Configuration**
  - [ ] Security headers
  - [ ] Error handling
  - [ ] Logging security
  - [ ] File permissions

## 📈 Reporting et Suivi

### Format de Rapport
```markdown
# Rapport de Test de Pénétration TrustVault

## Résumé Exécutif
- **Période de test** : [Date]
- **Testeur** : [Nom]
- **Scope** : [Périmètre]
- **Vulnérabilités trouvées** : [Nombre]

## Vulnérabilités Identifiées

### Critique
- [Description]
- **Impact** : [Impact]
- **Recommandation** : [Solution]

### Élevé
- [Description]
- **Impact** : [Impact]
- **Recommandation** : [Solution]

## Recommandations
1. [Recommandation 1]
2. [Recommandation 2]

## Conclusion
[Évaluation globale]
```

### Métriques de Sécurité
- **Temps de détection** : < 5 minutes
- **Temps de réponse** : < 15 minutes
- **Taux de faux positifs** : < 5%
- **Couverture de test** : > 95%

Ce guide garantit une évaluation complète de la sécurité de TrustVault selon les meilleures pratiques de l'industrie.

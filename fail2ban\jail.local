# TrustVault - Fail2Ban Configuration

[DEFAULT]
# Ban hosts for 1 hour by default
bantime = 3600

# A host is banned if it has generated "maxretry" during the last "findtime" seconds
findtime = 600
maxretry = 3

# "ignoreip" can be a list of IP addresses, CIDR masks or DNS hosts
ignoreip = 127.0.0.1/8 ::1 10.0.0.0/8 **********/12 ***********/16

# Backend for log processing
backend = auto

# Email notifications
destemail = <EMAIL>
sender = <EMAIL>
mta = sendmail

# Actions
action = %(action_mwl)s

# ============================================================================
# NGINX PROTECTION
# ============================================================================

[nginx-http-auth]
enabled = true
port = http,https
logpath = /var/log/nginx/error.log
maxretry = 3
bantime = 3600

[nginx-noscript]
enabled = true
port = http,https
logpath = /var/log/nginx/access.log
maxretry = 6
bantime = 3600

[nginx-badbots]
enabled = true
port = http,https
logpath = /var/log/nginx/access.log
maxretry = 2
bantime = 86400

[nginx-noproxy]
enabled = true
port = http,https
logpath = /var/log/nginx/access.log
maxretry = 2
bantime = 3600

[nginx-limit-req]
enabled = true
port = http,https
logpath = /var/log/nginx/error.log
maxretry = 10
findtime = 600
bantime = 3600

# ============================================================================
# CUSTOM TRUSTVAULT FILTERS
# ============================================================================

[trustvault-auth]
enabled = true
port = http,https
logpath = /var/log/nginx/auth_attempts.log
filter = trustvault-auth
maxretry = 5
findtime = 300
bantime = 1800
action = %(action_mwl)s

[trustvault-api-abuse]
enabled = true
port = http,https
logpath = /var/log/nginx/api_access.log
filter = trustvault-api-abuse
maxretry = 100
findtime = 60
bantime = 3600
action = %(action_mwl)s

[trustvault-sql-injection]
enabled = true
port = http,https
logpath = /var/log/nginx/security.log
filter = trustvault-sql-injection
maxretry = 1
findtime = 300
bantime = 86400
action = %(action_mwl)s

[trustvault-xss]
enabled = true
port = http,https
logpath = /var/log/nginx/security.log
filter = trustvault-xss
maxretry = 1
findtime = 300
bantime = 86400
action = %(action_mwl)s

[trustvault-directory-traversal]
enabled = true
port = http,https
logpath = /var/log/nginx/security.log
filter = trustvault-directory-traversal
maxretry = 1
findtime = 300
bantime = 86400
action = %(action_mwl)s

# ============================================================================
# SSH PROTECTION
# ============================================================================

[sshd]
enabled = true
port = ssh
logpath = /var/log/auth.log
maxretry = 3
bantime = 3600

[sshd-ddos]
enabled = true
port = ssh
logpath = /var/log/auth.log
maxretry = 6
findtime = 120
bantime = 3600

# ============================================================================
# SYSTEM PROTECTION
# ============================================================================

[pam-generic]
enabled = true
banaction = iptables-multiport
logpath = /var/log/auth.log
maxretry = 6
findtime = 600
bantime = 3600

[xinetd-fail]
enabled = true
logpath = /var/log/daemon.log
maxretry = 2
bantime = 3600

# ============================================================================
# RECIDIVE (REPEAT OFFENDERS)
# ============================================================================

[recidive]
enabled = true
logpath = /var/log/fail2ban.log
banaction = iptables-allports
bantime = 604800  # 1 week
findtime = 86400   # 1 day
maxretry = 5

# ============================================================================
# CUSTOM ACTIONS
# ============================================================================

[trustvault-notification]
enabled = true
port = http,https
logpath = /var/log/nginx/security.log
filter = trustvault-security-alert
maxretry = 1
findtime = 60
bantime = 3600
action = %(action_)s
         trustvault-webhook[name=%(__name__)s, dest="%(destemail)s", logpath="%(logpath)s"]

# ============================================================================
# WHITELIST MANAGEMENT
# ============================================================================

# Whitelist for trusted IPs (admin access)
[trustvault-whitelist]
enabled = false
# Add trusted IP ranges here
ignoreip = 127.0.0.1/8 ::1
           10.0.0.0/8
           **********/12
           ***********/16

# Generated by Django 4.2.7 on 2025-07-27 13:20

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='DataRetentionPolicy',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('resource_type', models.CharField(max_length=100, unique=True)),
                ('retention_days', models.PositiveIntegerField()),
                ('description', models.TextField()),
                ('is_gdpr_related', models.<PERSON>oleanField(default=False)),
            ],
            options={
                'verbose_name_plural': 'Data Retention Policies',
                'db_table': 'core_data_retention_policy',
            },
        ),
        migrations.CreateModel(
            name='SystemConfiguration',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('key', models.CharField(max_length=100, unique=True)),
                ('value', models.TextField()),
                ('description', models.TextField(blank=True)),
                ('is_encrypted', models.BooleanField(default=False)),
            ],
            options={
                'db_table': 'core_system_configuration',
                'ordering': ['key'],
            },
        ),
        migrations.CreateModel(
            name='SecurityEvent',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('event_type', models.CharField(choices=[('FAILED_LOGIN', 'Failed Login'), ('SUSPICIOUS_ACTIVITY', 'Suspicious Activity'), ('UNAUTHORIZED_ACCESS', 'Unauthorized Access'), ('DATA_BREACH', 'Data Breach'), ('MALWARE_DETECTED', 'Malware Detected'), ('INTRUSION_ATTEMPT', 'Intrusion Attempt')], max_length=50)),
                ('risk_level', models.CharField(choices=[('LOW', 'Low'), ('MEDIUM', 'Medium'), ('HIGH', 'High'), ('CRITICAL', 'Critical')], max_length=10)),
                ('source_ip', models.GenericIPAddressField()),
                ('user_agent', models.TextField(blank=True)),
                ('description', models.TextField()),
                ('details', models.JSONField(default=dict)),
                ('is_resolved', models.BooleanField(default=False)),
                ('resolved_at', models.DateTimeField(blank=True, null=True)),
                ('resolved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='resolved_security_events', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'core_security_event',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['event_type', 'created_at'], name='core_securi_event_t_5d07cd_idx'), models.Index(fields=['risk_level', 'created_at'], name='core_securi_risk_le_8f8976_idx'), models.Index(fields=['source_ip', 'created_at'], name='core_securi_source__137fa6_idx'), models.Index(fields=['is_resolved', 'created_at'], name='core_securi_is_reso_e1f6da_idx')],
            },
        ),
        migrations.CreateModel(
            name='AuditLog',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('action', models.CharField(choices=[('CREATE', 'Create'), ('UPDATE', 'Update'), ('DELETE', 'Delete'), ('LOGIN', 'Login'), ('LOGOUT', 'Logout'), ('ACCESS', 'Access'), ('EXPORT', 'Export'), ('IMPORT', 'Import')], max_length=20)),
                ('resource_type', models.CharField(max_length=100)),
                ('resource_id', models.CharField(blank=True, max_length=100, null=True)),
                ('ip_address', models.GenericIPAddressField()),
                ('user_agent', models.TextField(blank=True)),
                ('details', models.JSONField(default=dict)),
                ('severity', models.CharField(choices=[('LOW', 'Low'), ('MEDIUM', 'Medium'), ('HIGH', 'High'), ('CRITICAL', 'Critical')], default='LOW', max_length=10)),
                ('timestamp', models.DateTimeField(default=django.utils.timezone.now)),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='audit_logs', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'core_audit_log',
                'ordering': ['-timestamp'],
                'indexes': [models.Index(fields=['user', 'timestamp'], name='core_audit__user_id_66db5a_idx'), models.Index(fields=['action', 'timestamp'], name='core_audit__action_1cc579_idx'), models.Index(fields=['resource_type', 'timestamp'], name='core_audit__resourc_fdebc9_idx'), models.Index(fields=['severity', 'timestamp'], name='core_audit__severit_1e5fc9_idx')],
            },
        ),
    ]

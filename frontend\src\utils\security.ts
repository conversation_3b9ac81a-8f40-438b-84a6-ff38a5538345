// TrustVault - Security Utilities

import CryptoJS from 'crypto-js';

/**
 * Validate environment security settings
 */
export const validateEnvironment = (): void => {
  // Check if running in development
  if (process.env.NODE_ENV === 'development') {
    console.warn('🔒 Running in development mode - security features may be relaxed');
  }

  // Validate required environment variables
  const requiredEnvVars = [
    'REACT_APP_API_URL',
  ];

  const missingVars = requiredEnvVars.filter(
    (varName) => {
      const envValue = varName === 'REACT_APP_API_URL' ? process.env.REACT_APP_API_URL : undefined;
      return !envValue;
    }
  );

  if (missingVars.length > 0) {
    console.error('❌ Missing required environment variables:', missingVars);
  }

  // Check for HTTPS in production
  if (process.env.NODE_ENV === 'production' && window.location.protocol !== 'https:') {
    console.warn('⚠️ Application should be served over HTTPS in production');
  }
};

/**
 * Sanitize user input to prevent XSS
 */
export const sanitizeInput = (input: string): string => {
  const div = document.createElement('div');
  div.textContent = input;
  return div.innerHTML;
};

/**
 * Validate email format
 */
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * Validate password strength
 */
export const validatePasswordStrength = (password: string): {
  isValid: boolean;
  errors: string[];
  score: number;
} => {
  const errors: string[] = [];
  let score = 0;

  // Length check
  if (password.length < 12) {
    errors.push('Password must be at least 12 characters long');
  } else {
    score += 1;
  }

  // Uppercase check
  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  } else {
    score += 1;
  }

  // Lowercase check
  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  } else {
    score += 1;
  }

  // Number check
  if (!/\d/.test(password)) {
    errors.push('Password must contain at least one number');
  } else {
    score += 1;
  }

  // Special character check
  if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    errors.push('Password must contain at least one special character');
  } else {
    score += 1;
  }

  // Common patterns check
  const commonPatterns = [
    /123456/,
    /password/i,
    /qwerty/i,
    /abc123/i,
    /admin/i,
    /letmein/i,
    /welcome/i,
  ];

  if (commonPatterns.some(pattern => pattern.test(password))) {
    errors.push('Password contains common patterns that are not allowed');
    score -= 1;
  }

  // Sequential characters check
  if (hasSequentialChars(password)) {
    errors.push('Password cannot contain sequential characters');
    score -= 1;
  }

  return {
    isValid: errors.length === 0,
    errors,
    score: Math.max(0, score),
  };
};

/**
 * Check for sequential characters in password
 */
const hasSequentialChars = (password: string): boolean => {
  const sequences = [
    'abcdefghijklmnopqrstuvwxyz',
    '0123456789',
    'qwertyuiopasdfghjklzxcvbnm', // keyboard layout
  ];

  const passwordLower = password.toLowerCase();

  for (const sequence of sequences) {
    for (let i = 0; i <= sequence.length - 3; i++) {
      const subseq = sequence.substring(i, i + 3);
      const reverseSubseq = subseq.split('').reverse().join('');
      
      if (passwordLower.includes(subseq) || passwordLower.includes(reverseSubseq)) {
        return true;
      }
    }
  }

  return false;
};

/**
 * Generate secure random string
 */
export const generateSecureRandom = (length: number = 32): string => {
  const array = new Uint8Array(length);
  window.crypto.getRandomValues(array);
  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
};

/**
 * Hash sensitive data for client-side storage
 */
export const hashData = (data: string, salt?: string): string => {
  const saltToUse = salt || generateSecureRandom(16);
  return CryptoJS.SHA256(data + saltToUse).toString();
};

/**
 * Encrypt sensitive data for client-side storage
 */
export const encryptData = (data: string, key: string): string => {
  return CryptoJS.AES.encrypt(data, key).toString();
};

/**
 * Decrypt sensitive data from client-side storage
 */
export const decryptData = (encryptedData: string, key: string): string => {
  const bytes = CryptoJS.AES.decrypt(encryptedData, key);
  return bytes.toString(CryptoJS.enc.Utf8);
};

/**
 * Secure localStorage wrapper
 */
export const secureStorage = {
  setItem: (key: string, value: string, encrypt: boolean = false): void => {
    try {
      const dataToStore = encrypt ? encryptData(value, key) : value;
      localStorage.setItem(key, dataToStore);
    } catch (error) {
      console.error('Failed to store data securely:', error);
    }
  },

  getItem: (key: string, decrypt: boolean = false): string | null => {
    try {
      const data = localStorage.getItem(key);
      if (!data) return null;
      
      return decrypt ? decryptData(data, key) : data;
    } catch (error) {
      console.error('Failed to retrieve data securely:', error);
      return null;
    }
  },

  removeItem: (key: string): void => {
    localStorage.removeItem(key);
  },

  clear: (): void => {
    localStorage.clear();
  },
};

/**
 * Detect potential security threats in user input
 */
export const detectSecurityThreats = (input: string): {
  isSafe: boolean;
  threats: string[];
} => {
  const threats: string[] = [];

  // XSS patterns
  const xssPatterns = [
    /<script[^>]*>.*?<\/script>/gi,
    /javascript:/gi,
    /vbscript:/gi,
    /on\w+\s*=/gi,
    /eval\s*\(/gi,
    /document\.cookie/gi,
    /alert\s*\(/gi,
  ];

  // SQL injection patterns
  const sqlPatterns = [
    /union\s+select/gi,
    /or\s+1\s*=\s*1/gi,
    /drop\s+table/gi,
    /insert\s+into/gi,
    /delete\s+from/gi,
    /update\s+.*set/gi,
  ];

  // Check for XSS
  if (xssPatterns.some(pattern => pattern.test(input))) {
    threats.push('Potential XSS attack detected');
  }

  // Check for SQL injection
  if (sqlPatterns.some(pattern => pattern.test(input))) {
    threats.push('Potential SQL injection detected');
  }

  return {
    isSafe: threats.length === 0,
    threats,
  };
};

/**
 * Rate limiting utility
 */
export const createRateLimiter = (maxRequests: number, windowMs: number) => {
  const requests: number[] = [];

  return {
    isAllowed: (): boolean => {
      const now = Date.now();
      
      // Remove old requests outside the window
      while (requests.length > 0 && requests[0] <= now - windowMs) {
        requests.shift();
      }

      // Check if we're under the limit
      if (requests.length < maxRequests) {
        requests.push(now);
        return true;
      }

      return false;
    },
    
    getRemainingRequests: (): number => {
      const now = Date.now();
      
      // Remove old requests
      while (requests.length > 0 && requests[0] <= now - windowMs) {
        requests.shift();
      }

      return Math.max(0, maxRequests - requests.length);
    },
  };
};

/**
 * Content Security Policy violation handler
 */
export const handleCSPViolation = (event: SecurityPolicyViolationEvent): void => {
  console.error('CSP Violation:', {
    blockedURI: event.blockedURI,
    violatedDirective: event.violatedDirective,
    originalPolicy: event.originalPolicy,
    sourceFile: event.sourceFile,
    lineNumber: event.lineNumber,
  });

  // In production, you might want to send this to your security monitoring system
  if (process.env.NODE_ENV === 'production') {
    // Send to security monitoring endpoint
    fetch('/api/v1/security/csp-violation/', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        blockedURI: event.blockedURI,
        violatedDirective: event.violatedDirective,
        sourceFile: event.sourceFile,
        lineNumber: event.lineNumber,
        timestamp: new Date().toISOString(),
      }),
    }).catch(console.error);
  }
};

// Set up CSP violation listener
if (typeof window !== 'undefined') {
  document.addEventListener('securitypolicyviolation', handleCSPViolation);
}

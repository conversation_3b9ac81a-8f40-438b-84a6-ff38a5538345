# Generated by Django 4.2.7 on 2025-07-30 12:29

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('portfolio', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='PriceAlert',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('alert_type', models.CharField(choices=[('PRICE_ABOVE', 'Price Above'), ('PRICE_BELOW', 'Price Below'), ('PRICE_CHANGE', 'Price Change %'), ('VOLUME_SPIKE', 'Volume Spike'), ('PORTFOLIO_VALUE', 'Portfolio Value'), ('PORTFOLIO_CHANGE', 'Portfolio Change %'), ('NEWS_ALERT', 'News Alert'), ('EARNINGS_ALERT', 'Earnings Alert')], max_length=20)),
                ('threshold_value', models.DecimalField(decimal_places=4, max_digits=15)),
                ('comparison_operator', models.CharField(choices=[('GT', 'Greater Than'), ('LT', 'Less Than'), ('GTE', 'Greater Than or Equal'), ('LTE', 'Less Than or Equal')], default='GT', max_length=10)),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True)),
                ('status', models.CharField(choices=[('ACTIVE', 'Active'), ('TRIGGERED', 'Triggered'), ('PAUSED', 'Paused'), ('EXPIRED', 'Expired'), ('CANCELLED', 'Cancelled')], default='ACTIVE', max_length=20)),
                ('notification_channels', models.JSONField(default=list)),
                ('notification_message', models.TextField(blank=True)),
                ('expires_at', models.DateTimeField(blank=True, null=True)),
                ('last_checked_at', models.DateTimeField(blank=True, null=True)),
                ('triggered_at', models.DateTimeField(blank=True, null=True)),
                ('max_triggers', models.IntegerField(default=1)),
                ('trigger_count', models.IntegerField(default=0)),
                ('cooldown_minutes', models.IntegerField(default=60)),
                ('asset', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='price_alerts', to='portfolio.asset')),
                ('portfolio', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='price_alerts', to='portfolio.portfolio')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='price_alerts', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'alerts_price_alert',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='NotificationPreference',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('email_enabled', models.BooleanField(default=True)),
                ('sms_enabled', models.BooleanField(default=False)),
                ('push_enabled', models.BooleanField(default=True)),
                ('in_app_enabled', models.BooleanField(default=True)),
                ('email_address', models.EmailField(blank=True, max_length=254)),
                ('phone_number', models.CharField(blank=True, max_length=20)),
                ('quiet_hours_start', models.TimeField(blank=True, null=True)),
                ('quiet_hours_end', models.TimeField(blank=True, null=True)),
                ('timezone', models.CharField(default='UTC', max_length=50)),
                ('max_emails_per_day', models.IntegerField(default=50)),
                ('max_sms_per_day', models.IntegerField(default=10)),
                ('max_push_per_hour', models.IntegerField(default=20)),
                ('price_alerts_enabled', models.BooleanField(default=True)),
                ('portfolio_alerts_enabled', models.BooleanField(default=True)),
                ('news_alerts_enabled', models.BooleanField(default=True)),
                ('security_alerts_enabled', models.BooleanField(default=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='notification_preferences', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'alerts_notification_preference',
            },
        ),
        migrations.CreateModel(
            name='Notification',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('channel', models.CharField(choices=[('EMAIL', 'Email'), ('SMS', 'SMS'), ('PUSH', 'Push Notification'), ('IN_APP', 'In-App Notification')], max_length=20)),
                ('recipient', models.CharField(max_length=255)),
                ('subject', models.CharField(max_length=255)),
                ('message', models.TextField()),
                ('status', models.CharField(choices=[('PENDING', 'Pending'), ('SENT', 'Sent'), ('DELIVERED', 'Delivered'), ('FAILED', 'Failed'), ('BOUNCED', 'Bounced')], default='PENDING', max_length=20)),
                ('sent_at', models.DateTimeField(blank=True, null=True)),
                ('delivered_at', models.DateTimeField(blank=True, null=True)),
                ('error_message', models.TextField(blank=True)),
                ('external_id', models.CharField(blank=True, max_length=255)),
                ('provider', models.CharField(blank=True, max_length=50)),
                ('metadata', models.JSONField(default=dict)),
                ('alert', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='notifications', to='alerts.pricealert')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notifications', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'alerts_notification',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='AlertHistory',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('triggered_value', models.DecimalField(decimal_places=4, max_digits=15)),
                ('threshold_value', models.DecimalField(decimal_places=4, max_digits=15)),
                ('message', models.TextField()),
                ('notifications_sent', models.JSONField(default=list)),
                ('notification_failures', models.JSONField(default=list)),
                ('alert', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='history', to='alerts.pricealert')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='alert_history', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'alerts_alert_history',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Report',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('report_type', models.CharField(choices=[('PORTFOLIO_PERFORMANCE', 'Portfolio Performance'), ('TRANSACTION_HISTORY', 'Transaction History'), ('TAX_REPORT', 'Tax Report'), ('RISK_ANALYSIS', 'Risk Analysis'), ('ALLOCATION_REPORT', 'Asset Allocation'), ('DIVIDEND_REPORT', 'Dividend Report'), ('CUSTOM', 'Custom Report')], max_length=30)),
                ('report_format', models.CharField(choices=[('PDF', 'PDF'), ('EXCEL', 'Excel'), ('CSV', 'CSV'), ('JSON', 'JSON')], default='PDF', max_length=10)),
                ('name', models.CharField(max_length=200)),
                ('description', models.TextField(blank=True)),
                ('start_date', models.DateField()),
                ('end_date', models.DateField()),
                ('status', models.CharField(choices=[('PENDING', 'Pending'), ('GENERATING', 'Generating'), ('COMPLETED', 'Completed'), ('FAILED', 'Failed'), ('EXPIRED', 'Expired')], default='PENDING', max_length=20)),
                ('parameters', models.JSONField(default=dict)),
                ('file_path', models.CharField(blank=True, max_length=500)),
                ('file_size', models.BigIntegerField(blank=True, null=True)),
                ('file_hash', models.CharField(blank=True, max_length=64)),
                ('generated_at', models.DateTimeField(blank=True, null=True)),
                ('expires_at', models.DateTimeField(blank=True, null=True)),
                ('error_message', models.TextField(blank=True)),
                ('generation_time', models.DurationField(blank=True, null=True)),
                ('portfolio', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='reports', to='portfolio.portfolio')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reports', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'alerts_report',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['user', 'created_at'], name='alerts_repo_user_id_1c6728_idx'), models.Index(fields=['portfolio', 'created_at'], name='alerts_repo_portfol_aac731_idx'), models.Index(fields=['report_type', 'status'], name='alerts_repo_report__85cf9c_idx'), models.Index(fields=['status', 'created_at'], name='alerts_repo_status_170cd6_idx'), models.Index(fields=['expires_at'], name='alerts_repo_expires_034415_idx')],
            },
        ),
        migrations.AddIndex(
            model_name='pricealert',
            index=models.Index(fields=['user', 'status'], name='alerts_pric_user_id_387ae3_idx'),
        ),
        migrations.AddIndex(
            model_name='pricealert',
            index=models.Index(fields=['asset', 'status'], name='alerts_pric_asset_i_5e29d7_idx'),
        ),
        migrations.AddIndex(
            model_name='pricealert',
            index=models.Index(fields=['portfolio', 'status'], name='alerts_pric_portfol_f94457_idx'),
        ),
        migrations.AddIndex(
            model_name='pricealert',
            index=models.Index(fields=['alert_type', 'status'], name='alerts_pric_alert_t_aa291b_idx'),
        ),
        migrations.AddIndex(
            model_name='pricealert',
            index=models.Index(fields=['expires_at'], name='alerts_pric_expires_6c27d0_idx'),
        ),
        migrations.AddIndex(
            model_name='notification',
            index=models.Index(fields=['user', 'created_at'], name='alerts_noti_user_id_d835cd_idx'),
        ),
        migrations.AddIndex(
            model_name='notification',
            index=models.Index(fields=['channel', 'status'], name='alerts_noti_channel_bc2698_idx'),
        ),
        migrations.AddIndex(
            model_name='notification',
            index=models.Index(fields=['status', 'created_at'], name='alerts_noti_status_5a4b18_idx'),
        ),
        migrations.AddIndex(
            model_name='alerthistory',
            index=models.Index(fields=['user', 'created_at'], name='alerts_aler_user_id_c75022_idx'),
        ),
        migrations.AddIndex(
            model_name='alerthistory',
            index=models.Index(fields=['alert', 'created_at'], name='alerts_aler_alert_i_d7dfce_idx'),
        ),
    ]

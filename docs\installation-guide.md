# 🚀 TrustVault - Guide d'Installation

## Prérequis Système

### Environnement Recommandé
- **OS** : Ubuntu 20.04+ / Windows 11 avec WSL2
- **RAM** : 16 GB minimum (32 GB recommandé)
- **CPU** : 4 cores minimum (8 cores recommandé)
- **Stockage** : 100 GB SSD minimum
- **R<PERSON>eau** : Connexion Internet stable

### Logiciels Requis

#### Sur Ubuntu/WSL2
```bash
# Mise à jour du système
sudo apt update && sudo apt upgrade -y

# Installation des dépendances
sudo apt install -y \
    docker.io \
    docker-compose \
    git \
    curl \
    wget \
    openssl \
    gnupg \
    software-properties-common

# Ajout de l'utilisateur au groupe docker
sudo usermod -aG docker $USER
newgrp docker
```

#### Sur Windows (avec WSL2)
1. Installer WSL2 et Ubuntu 20.04+
2. Installer Docker Desktop avec intégration WSL2
3. Configurer les ressources Docker (8GB RAM minimum)

## 📥 Installation Rapide

### 1. Clonage du Projet
```bash
git clone https://github.com/your-org/trustvault.git
cd trustvault
```

### 2. Déploiement Automatique
```bash
# Rendre le script exécutable
chmod +x scripts/deploy.sh

# Lancer le déploiement
./scripts/deploy.sh deploy
```

Le script automatique va :
- ✅ Vérifier les prérequis
- ✅ Générer les certificats SSL
- ✅ Créer les fichiers de configuration
- ✅ Initialiser les services
- ✅ Configurer la sécurité

### 3. Configuration des Hosts
Ajouter à `/etc/hosts` (Linux/WSL) ou `C:\Windows\System32\drivers\etc\hosts` (Windows) :
```
127.0.0.1 trustvault.local
127.0.0.1 api.trustvault.local
```

### 4. Import du Certificat CA
1. Ouvrir `nginx/ssl/ca.crt` dans votre navigateur
2. L'ajouter aux autorités de certification de confiance
3. Redémarrer le navigateur

## 🔧 Installation Manuelle

### 1. Configuration de l'Environnement
```bash
# Copier le fichier d'exemple
cp .env.example .env

# Éditer les variables d'environnement
nano .env
```

### 2. Génération des Secrets
```bash
# Créer le dossier secrets
mkdir -p secrets

# Générer les secrets
openssl rand -base64 50 > secrets/django_secret.txt
openssl rand -base64 32 > secrets/db_password.txt

# Sécuriser les permissions
chmod 600 secrets/*
```

### 3. Génération des Certificats SSL
```bash
# Exécuter le script de génération
chmod +x scripts/generate-ssl-certs.sh
./scripts/generate-ssl-certs.sh
```

### 4. Construction des Images Docker
```bash
# Construire toutes les images
docker-compose build --no-cache

# Ou construire individuellement
docker-compose build nginx
docker-compose build django
docker-compose build react
```

### 5. Initialisation des Services

#### Démarrage des Services Core
```bash
# Base de données et cache
docker-compose up -d postgres redis

# Attendre que les services soient prêts
sleep 30

# Vérifier le statut
docker-compose ps
```

#### Initialisation de Vault
```bash
# Démarrer Vault
docker-compose up -d vault

# Initialiser Vault
docker-compose exec vault vault operator init \
    -key-shares=1 \
    -key-threshold=1 > vault-keys.txt

# Unsealer Vault
UNSEAL_KEY=$(grep 'Unseal Key 1:' vault-keys.txt | awk '{print $NF}')
docker-compose exec vault vault operator unseal $UNSEAL_KEY
```

#### Démarrage des Services de Sécurité
```bash
# SIEM et monitoring
docker-compose up -d wazuh-manager wazuh-indexer wazuh-dashboard

# IDS/IPS
docker-compose up -d suricata

# Monitoring
docker-compose up -d prometheus grafana
```

#### Démarrage des Applications
```bash
# Backend et frontend
docker-compose up -d django react celery

# Reverse proxy
docker-compose up -d nginx
```

## 🔐 Configuration de Sécurité

### 1. Configuration Wazuh
```bash
# Accéder au dashboard Wazuh
# URL: http://localhost:5601
# User: admin
# Pass: [voir .env WAZUH_PASSWORD]

# Vérifier les agents
docker-compose exec wazuh-manager /var/ossec/bin/agent_control -l
```

### 2. Configuration Grafana
```bash
# Accéder à Grafana
# URL: http://localhost:3000
# User: admin
# Pass: [voir .env GRAFANA_PASSWORD]

# Importer les dashboards de sécurité
# Fichiers dans grafana/dashboards/
```

### 3. Configuration des Alertes
```bash
# Configurer les webhooks de notification
# Éditer prometheus/alertmanager.yml

# Tester les alertes
docker-compose exec prometheus promtool query instant \
    'up{job="django"} == 0'
```

## 🧪 Tests et Validation

### 1. Tests de Connectivité
```bash
# Test HTTPS
curl -k https://trustvault.local/health

# Test API
curl -k https://api.trustvault.local/health

# Test des services
docker-compose ps
```

### 2. Tests de Sécurité
```bash
# Test SSL
nmap --script ssl-enum-ciphers -p 443 localhost

# Test WAF
curl -k "https://trustvault.local/?id=1' OR '1'='1"

# Test rate limiting
for i in {1..10}; do curl -k https://trustvault.local; done
```

### 3. Validation des Logs
```bash
# Logs Nginx
docker-compose logs nginx | grep -i error

# Logs Wazuh
docker-compose logs wazuh-manager | grep -i alert

# Logs application
docker-compose logs django | grep -i security
```

## 🔄 Maintenance et Mise à Jour

### 1. Sauvegarde
```bash
# Sauvegarde manuelle
./scripts/backup-restic.sh backup

# Vérifier les sauvegardes
./scripts/backup-restic.sh list
```

### 2. Mise à Jour
```bash
# Mise à jour des images
./scripts/deploy.sh update

# Redémarrage des services
./scripts/deploy.sh restart
```

### 3. Monitoring
```bash
# Statut des services
./scripts/deploy.sh status

# Logs en temps réel
./scripts/deploy.sh logs

# Logs d'un service spécifique
./scripts/deploy.sh logs nginx
```

## 🚨 Dépannage

### Problèmes Courants

#### Services qui ne démarrent pas
```bash
# Vérifier les logs
docker-compose logs [service-name]

# Vérifier l'espace disque
df -h

# Vérifier la mémoire
free -h

# Redémarrer un service
docker-compose restart [service-name]
```

#### Problèmes de certificats
```bash
# Régénérer les certificats
rm -rf nginx/ssl/*
./scripts/generate-ssl-certs.sh

# Redémarrer Nginx
docker-compose restart nginx
```

#### Problèmes de base de données
```bash
# Vérifier la connexion
docker-compose exec postgres psql -U trustvault -d trustvault -c "SELECT version();"

# Restaurer depuis une sauvegarde
./scripts/backup-restic.sh restore-db [snapshot-id]
```

### Logs de Débogage
```bash
# Activer le mode debug
echo "DEBUG=True" >> .env
docker-compose restart django

# Logs détaillés
docker-compose logs -f --tail=100 django
```

## 📞 Support

### Documentation
- [Architecture de Sécurité](security-architecture.md)
- [Procédures de Sécurité](security-procedures.md)
- [Tests de Pénétration](penetration-testing.md)

### Contacts
- **Support Technique** : <EMAIL>
- **Sécurité** : <EMAIL>
- **Urgences** : +33 1 23 45 67 89

### Ressources
- **GitHub** : https://github.com/your-org/trustvault
- **Documentation** : https://docs.trustvault.local
- **Status Page** : https://status.trustvault.local

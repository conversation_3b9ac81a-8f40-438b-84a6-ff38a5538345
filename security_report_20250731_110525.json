[{"timestamp": "2025-07-31T11:04:36.561629", "test": "SSL Certificate - trustvault.crt", "status": "PASS", "details": "File exists: security/ssl\\trustvault.crt"}, {"timestamp": "2025-07-31T11:04:36.561629", "test": "SSL Certificate - trustvault.key", "status": "PASS", "details": "File exists: security/ssl\\trustvault.key"}, {"timestamp": "2025-07-31T11:04:36.562720", "test": "SSL Certificate - ca.crt", "status": "PASS", "details": "File exists: security/ssl\\ca.crt"}, {"timestamp": "2025-07-31T11:04:36.563445", "test": "SSL Certificate - ca.key", "status": "PASS", "details": "File exists: security/ssl\\ca.key"}, {"timestamp": "2025-07-31T11:04:38.021000", "test": "Authentication - <PERSON>gin", "status": "PASS", "details": "Login successful with JWT token"}, {"timestamp": "2025-07-31T11:04:42.003608", "test": "Security Header - X-Content-Type-Options", "status": "PASS", "details": "Value: nosniff"}, {"timestamp": "2025-07-31T11:04:42.004326", "test": "Security Header - X-Frame-Options", "status": "PASS", "details": "Value: DENY"}, {"timestamp": "2025-07-31T11:04:42.004808", "test": "Security Header - Content-Security-Policy", "status": "PASS", "details": "Present: default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self'; frame-ancestors 'none'; base-uri 'self'; form-action 'self'"}, {"timestamp": "2025-07-31T11:04:42.005316", "test": "Security Header - X-XSS-Protection", "status": "PASS", "details": "Value: 1; mode=block"}, {"timestamp": "2025-07-31T11:05:21.702641", "test": "Rate Limiting", "status": "WARN", "details": "No rate limiting detected in 10 requests"}, {"timestamp": "2025-07-31T11:05:21.738333", "test": "Monitoring - Prometheus", "status": "PASS", "details": "Prometheus API responding"}, {"timestamp": "2025-07-31T11:05:21.772662", "test": "Monitoring - <PERSON><PERSON>", "status": "PASS", "details": "Grafana health check passed"}, {"timestamp": "2025-07-31T11:05:25.635115", "test": "Database Security", "status": "PASS", "details": "Database connection secure"}, {"timestamp": "2025-07-31T11:05:25.934896", "test": "Container Security", "status": "PASS", "details": "6 containers running"}]
// TrustVault - Alert Creation Checker Component

import React, { useState } from 'react';
import {
  <PERSON>,
  Typography,
  Card,
  CardContent,
  Button,
  Alert,
  CircularProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Chip,
  Divider,
} from '@mui/material';
import {
  ExpandMore,
  BugReport,
  CheckCircle,
  Error,
  PlayArrow,
  Refresh,
} from '@mui/icons-material';
import { useMutation } from 'react-query';
import { toast } from 'react-hot-toast';

// Services
import apiService from '../../services/api';

interface TestResult {
  success: boolean;
  message: string;
  alert_id?: string;
  alert_name?: string;
  test_data?: any;
  validated_data?: any;
  errors?: any;
  timestamp: string;
}

const AlertCreationChecker: React.FC = () => {
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [lastError, setLastError] = useState<any>(null);

  // Test alert creation via API
  const testAlertCreationMutation = useMutation(
    () => apiService.testAlertCreation(),
    {
      onSuccess: (response) => {
        const result: TestResult = {
          ...response.data,
          timestamp: new Date().toISOString(),
        };
        setTestResults(prev => [result, ...prev]);
        setLastError(null);
        
        if (result.success) {
          toast.success('✅ Alert creation test passed!');
        } else {
          toast.error('❌ Alert creation test failed');
        }
      },
      onError: (error: any) => {
        const errorResult: TestResult = {
          success: false,
          message: 'API request failed',
          errors: error.response?.data || error.message,
          timestamp: new Date().toISOString(),
        };
        setTestResults(prev => [errorResult, ...prev]);
        setLastError(error.response?.data || error.message);
        toast.error('❌ Alert creation test failed');
      },
    }
  );

  // Test real alert creation
  const testRealAlertMutation = useMutation(
    () => {
      const testData = {
        name: 'Debug Test Alert',
        alert_type: 'PRICE_ABOVE',
        asset: '1', // Mock asset ID
        threshold_value: 150.00,
        comparison_operator: 'GT',
        notification_channels: ['EMAIL'],
        max_triggers: 3,
        cooldown_minutes: 30
      };
      
      return apiService.createAlert(testData);
    },
    {
      onSuccess: (response) => {
        const result: TestResult = {
          success: true,
          message: 'Real alert created successfully',
          alert_id: response.id,
          alert_name: response.name,
          test_data: response,
          timestamp: new Date().toISOString(),
        };
        setTestResults(prev => [result, ...prev]);
        setLastError(null);
        toast.success('✅ Real alert creation passed!');
      },
      onError: (error: any) => {
        const errorResult: TestResult = {
          success: false,
          message: 'Real alert creation failed',
          errors: error.response?.data || error.message,
          timestamp: new Date().toISOString(),
        };
        setTestResults(prev => [errorResult, ...prev]);
        setLastError(error.response?.data || error.message);
        toast.error('❌ Real alert creation failed');
      },
    }
  );

  const handleRunTest = () => {
    testAlertCreationMutation.mutate();
  };

  const handleTestRealAlert = () => {
    testRealAlertMutation.mutate();
  };

  const handleClearResults = () => {
    setTestResults([]);
    setLastError(null);
  };

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box display="flex" alignItems="center" gap={2} mb={3}>
        <BugReport color="primary" />
        <Typography variant="h5">
          Alert Creation Checker
        </Typography>
      </Box>

      <Alert severity="info" sx={{ mb: 3 }}>
        <Typography variant="body2">
          This tool helps debug alert creation issues by testing the API endpoints 
          and showing detailed error information.
        </Typography>
      </Alert>

      {/* Controls */}
      <Box display="flex" gap={2} mb={3}>
        <Button
          variant="contained"
          startIcon={<PlayArrow />}
          onClick={handleRunTest}
          disabled={testAlertCreationMutation.isLoading}
        >
          {testAlertCreationMutation.isLoading ? (
            <CircularProgress size={20} />
          ) : (
            'Run Test Endpoint'
          )}
        </Button>

        <Button
          variant="outlined"
          startIcon={<PlayArrow />}
          onClick={handleTestRealAlert}
          disabled={testRealAlertMutation.isLoading}
        >
          {testRealAlertMutation.isLoading ? (
            <CircularProgress size={20} />
          ) : (
            'Test Real Alert Creation'
          )}
        </Button>

        <Button
          variant="text"
          startIcon={<Refresh />}
          onClick={handleClearResults}
        >
          Clear Results
        </Button>
      </Box>

      {/* Last Error Summary */}
      {lastError && (
        <Alert severity="error" sx={{ mb: 3 }}>
          <Typography variant="subtitle2" gutterBottom>
            Last Error:
          </Typography>
          <Typography variant="body2" component="pre" sx={{ fontSize: '0.8rem' }}>
            {JSON.stringify(lastError, null, 2)}
          </Typography>
        </Alert>
      )}

      {/* Test Results */}
      {testResults.length > 0 && (
        <Box>
          <Typography variant="h6" gutterBottom>
            Test Results ({testResults.length})
          </Typography>

          {testResults.map((result, index) => (
            <Accordion key={index} sx={{ mb: 1 }}>
              <AccordionSummary expandIcon={<ExpandMore />}>
                <Box display="flex" alignItems="center" gap={2} width="100%">
                  {result.success ? (
                    <CheckCircle color="success" />
                  ) : (
                    <Error color="error" />
                  )}
                  <Box flex={1}>
                    <Typography variant="subtitle2">
                      {result.message}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {new Date(result.timestamp).toLocaleString()}
                    </Typography>
                  </Box>
                  <Chip
                    label={result.success ? 'SUCCESS' : 'FAILED'}
                    color={result.success ? 'success' : 'error'}
                    size="small"
                  />
                </Box>
              </AccordionSummary>
              <AccordionDetails>
                <Box>
                  {result.alert_id && (
                    <Box mb={2}>
                      <Typography variant="subtitle2" gutterBottom>
                        Created Alert:
                      </Typography>
                      <Typography variant="body2">
                        ID: {result.alert_id}
                      </Typography>
                      <Typography variant="body2">
                        Name: {result.alert_name}
                      </Typography>
                    </Box>
                  )}

                  {result.test_data && (
                    <Box mb={2}>
                      <Typography variant="subtitle2" gutterBottom>
                        Test Data:
                      </Typography>
                      <Typography
                        variant="body2"
                        component="pre"
                        sx={{
                          backgroundColor: 'grey.100',
                          p: 1,
                          borderRadius: 1,
                          fontSize: '0.8rem',
                          overflow: 'auto',
                        }}
                      >
                        {JSON.stringify(result.test_data, null, 2)}
                      </Typography>
                    </Box>
                  )}

                  {result.validated_data && (
                    <Box mb={2}>
                      <Typography variant="subtitle2" gutterBottom>
                        Validated Data:
                      </Typography>
                      <Typography
                        variant="body2"
                        component="pre"
                        sx={{
                          backgroundColor: 'success.light',
                          color: 'success.contrastText',
                          p: 1,
                          borderRadius: 1,
                          fontSize: '0.8rem',
                          overflow: 'auto',
                        }}
                      >
                        {JSON.stringify(result.validated_data, null, 2)}
                      </Typography>
                    </Box>
                  )}

                  {result.errors && (
                    <Box mb={2}>
                      <Typography variant="subtitle2" gutterBottom>
                        Errors:
                      </Typography>
                      <Typography
                        variant="body2"
                        component="pre"
                        sx={{
                          backgroundColor: 'error.light',
                          color: 'error.contrastText',
                          p: 1,
                          borderRadius: 1,
                          fontSize: '0.8rem',
                          overflow: 'auto',
                        }}
                      >
                        {JSON.stringify(result.errors, null, 2)}
                      </Typography>
                    </Box>
                  )}
                </Box>
              </AccordionDetails>
            </Accordion>
          ))}
        </Box>
      )}

      {testResults.length === 0 && (
        <Card>
          <CardContent sx={{ textAlign: 'center', py: 4 }}>
            <BugReport sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h6" color="text.secondary" gutterBottom>
              No test results yet
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Click "Run Test Endpoint" to start debugging alert creation
            </Typography>
          </CardContent>
        </Card>
      )}
    </Box>
  );
};

export default AlertCreationChecker;

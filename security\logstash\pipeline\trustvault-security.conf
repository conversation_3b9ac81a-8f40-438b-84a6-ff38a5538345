# TrustVault - Logstash Security Pipeline Configuration
# This pipeline processes security logs from various sources for SIEM analysis

input {
  # Nginx access logs
  beats {
    port => 5044
    type => "nginx-access"
  }

  # ModSecurity audit logs
  file {
    path => "/var/log/nginx/modsec_audit.log"
    type => "modsecurity"
    codec => "json"
  }

  # Django application logs
  file {
    path => "/var/log/trustvault/django.log"
    type => "django-app"
    codec => "json"
  }

  # Security events from database
  jdbc {
    jdbc_driver_library => "/usr/share/logstash/lib/postgresql.jar"
    jdbc_driver_class => "org.postgresql.Driver"
    jdbc_connection_string => "******************************************"
    jdbc_user => "trustvault"
    jdbc_password => "trustvault"
    statement => "SELECT * FROM security_securityevent WHERE timestamp > :sql_last_value ORDER BY timestamp"
    use_column_value => true
    tracking_column => "timestamp"
    tracking_column_type => "timestamp"
    schedule => "*/30 * * * * *"
    type => "security-events"
  }

  # Suricata IDS logs
  file {
    path => "/var/log/suricata/eve.json"
    type => "suricata"
    codec => "json"
  }

  # System logs
  syslog {
    port => 514
    type => "syslog"
  }
}

filter {
  # Process Nginx access logs
  if [type] == "nginx-access" {
    grok {
      match => { 
        "message" => "%{IPORHOST:remote_addr} - %{DATA:remote_user} \[%{HTTPDATE:timestamp}\] \"%{WORD:method} %{DATA:url} HTTP/%{NUMBER:http_version}\" %{NUMBER:status} %{NUMBER:body_bytes_sent} \"%{DATA:http_referer}\" \"%{DATA:http_user_agent}\" %{NUMBER:request_time} %{NUMBER:upstream_response_time} %{DATA:modsec_transaction_id}"
      }
    }

    date {
      match => [ "timestamp", "dd/MMM/yyyy:HH:mm:ss Z" ]
    }

    # Classify HTTP status codes
    if [status] >= 400 and [status] < 500 {
      mutate { add_tag => ["client_error"] }
    } else if [status] >= 500 {
      mutate { add_tag => ["server_error"] }
    }

    # Detect suspicious patterns
    if [url] =~ /\.(php|asp|aspx|jsp)/ {
      mutate { add_tag => ["suspicious_extension"] }
    }

    if [http_user_agent] =~ /(?i)(sqlmap|nikto|nmap|masscan|burp|w3af)/ {
      mutate { add_tag => ["attack_tool"] }
    }

    # GeoIP enrichment
    geoip {
      source => "remote_addr"
      target => "geoip"
    }

    # Calculate risk score
    ruby {
      code => "
        risk_score = 0
        
        # Status code scoring
        status = event.get('status').to_i
        if status >= 400 && status < 500
          risk_score += 10
        elsif status >= 500
          risk_score += 20
        end
        
        # User agent scoring
        user_agent = event.get('http_user_agent') || ''
        if user_agent.match(/(?i)(bot|crawler|spider)/)
          risk_score += 5
        elsif user_agent.match(/(?i)(sqlmap|nikto|nmap|burp)/)
          risk_score += 50
        end
        
        # URL pattern scoring
        url = event.get('url') || ''
        if url.match(/\.(php|asp|aspx|jsp)/)
          risk_score += 30
        elsif url.match(/\.\.|\/etc\/|\/proc\//)
          risk_score += 40
        end
        
        event.set('risk_score', risk_score)
        
        # Determine threat level
        if risk_score >= 50
          event.set('threat_level', 'HIGH')
        elsif risk_score >= 20
          event.set('threat_level', 'MEDIUM')
        elsif risk_score >= 10
          event.set('threat_level', 'LOW')
        else
          event.set('threat_level', 'MINIMAL')
        end
      "
    }
  }

  # Process ModSecurity logs
  if [type] == "modsecurity" {
    # Parse ModSecurity audit log format
    if [message] {
      json {
        source => "message"
      }
    }

    # Extract rule information
    if [transaction] and [transaction][messages] {
      ruby {
        code => "
          messages = event.get('[transaction][messages]') || []
          rule_ids = []
          rule_messages = []
          
          messages.each do |msg|
            if msg['details'] && msg['details']['ruleId']
              rule_ids << msg['details']['ruleId']
            end
            if msg['message']
              rule_messages << msg['message']
            end
          end
          
          event.set('rule_ids', rule_ids)
          event.set('rule_messages', rule_messages)
          event.set('rules_triggered', rule_ids.length)
        "
      }
    }

    mutate { add_tag => ["waf_event"] }
  }

  # Process Django application logs
  if [type] == "django-app" {
    json {
      source => "message"
    }

    # Parse log level
    if [levelname] {
      if [levelname] == "ERROR" or [levelname] == "CRITICAL" {
        mutate { add_tag => ["application_error"] }
      } elsif [levelname] == "WARNING" {
        mutate { add_tag => ["application_warning"] }
      }
    }

    # Extract user information if present
    if [user_id] {
      mutate { add_tag => ["authenticated_user"] }
    }
  }

  # Process security events from database
  if [type] == "security-events" {
    # Convert timestamp
    date {
      match => [ "timestamp", "yyyy-MM-dd HH:mm:ss.SSSSSS" ]
    }

    # Add security event tags
    mutate { add_tag => ["security_event"] }

    # Parse details JSON if present
    if [details] {
      json {
        source => "details"
        target => "event_details"
      }
    }

    # Classify by action
    if [action] {
      if [action] =~ /(?i)(login_failed|brute_force|intrusion)/ {
        mutate { add_tag => ["authentication_threat"] }
      } elsif [action] =~ /(?i)(sql_injection|xss|command_injection)/ {
        mutate { add_tag => ["injection_attack"] }
      } elsif [action] =~ /(?i)(privilege_escalation|unauthorized_access)/ {
        mutate { add_tag => ["authorization_threat"] }
      }
    }
  }

  # Process Suricata IDS logs
  if [type] == "suricata" {
    # Suricata logs are already in JSON format
    if [event_type] == "alert" {
      mutate { add_tag => ["ids_alert"] }
      
      # Extract signature information
      if [alert] {
        mutate {
          add_field => { "signature_id" => "%{[alert][signature_id]}" }
          add_field => { "signature" => "%{[alert][signature]}" }
          add_field => { "category" => "%{[alert][category]}" }
          add_field => { "severity" => "%{[alert][severity]}" }
        }
      }
    }
  }

  # Common enrichment for all events
  # Add timestamp if not present
  if ![timestamp] {
    mutate {
      add_field => { "timestamp" => "%{@timestamp}" }
    }
  }

  # Add source system
  mutate {
    add_field => { "source_system" => "trustvault" }
    add_field => { "log_type" => "%{type}" }
  }

  # Threat intelligence enrichment
  if [remote_addr] or [src_ip] {
    # Check against threat intelligence feeds
    translate {
      field => "[remote_addr]"
      destination => "[threat_intel][reputation]"
      dictionary_path => "/etc/logstash/threat_intel/ip_reputation.yml"
      fallback => "unknown"
    }
  }

  # Correlation with previous events
  if [remote_addr] {
    aggregate {
      task_id => "%{remote_addr}"
      code => "
        map['ip'] = event.get('remote_addr')
        map['event_count'] ||= 0
        map['event_count'] += 1
        map['first_seen'] ||= event.get('@timestamp')
        map['last_seen'] = event.get('@timestamp')
        map['risk_scores'] ||= []
        map['risk_scores'] << (event.get('risk_score') || 0)
        
        # Calculate average risk score
        if map['risk_scores'].length > 0
          avg_risk = map['risk_scores'].sum.to_f / map['risk_scores'].length
          event.set('avg_risk_score', avg_risk.round(2))
        end
        
        event.set('ip_event_count', map['event_count'])
        event.set('ip_first_seen', map['first_seen'])
      "
      push_previous_map_as_event => false
      timeout => 300
    }
  }
}

output {
  # Send to Elasticsearch
  elasticsearch {
    hosts => ["elasticsearch:9200"]
    user => "elastic"
    password => "SecureElasticPassword123!"
    index => "trustvault-security-%{+YYYY.MM.dd}"
    template_name => "trustvault-security"
    template => "/etc/logstash/templates/trustvault-security-template.json"
    template_overwrite => true
  }

  # Send high-risk events to separate index for alerting
  if [threat_level] == "HIGH" or [risk_score] and [risk_score] >= 50 {
    elasticsearch {
      hosts => ["elasticsearch:9200"]
      user => "elastic"
      password => "SecureElasticPassword123!"
      index => "trustvault-alerts-%{+YYYY.MM.dd}"
    }
  }

  # Send to file for backup
  file {
    path => "/var/log/logstash/trustvault-security-%{+YYYY-MM-dd}.log"
    codec => "json_lines"
  }

  # Debug output (remove in production)
  if [threat_level] == "HIGH" {
    stdout {
      codec => rubydebug
    }
  }
}

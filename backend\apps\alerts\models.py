# TrustVault - Alerts Models

import uuid
from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
from decimal import Decimal
from apps.core.models import BaseModel
from apps.portfolio.models import Asset, Portfolio

User = get_user_model()


class AlertType(models.TextChoices):
    """Alert type choices."""
    PRICE_ABOVE = 'PRICE_ABOVE', 'Price Above'
    PRICE_BELOW = 'PRICE_BELOW', 'Price Below'
    PRICE_CHANGE = 'PRICE_CHANGE', 'Price Change %'
    VOLUME_SPIKE = 'VOLUME_SPIKE', 'Volume Spike'
    PORTFOLIO_VALUE = 'PORTFOLIO_VALUE', 'Portfolio Value'
    PORTFOLIO_CHANGE = 'PORTFOLIO_CHANGE', 'Portfolio Change %'
    NEWS_ALERT = 'NEWS_ALERT', 'News Alert'
    EARNINGS_ALERT = 'EARNINGS_ALERT', 'Earnings Alert'


class AlertStatus(models.TextChoices):
    """Alert status choices."""
    ACTIVE = 'ACTIVE', 'Active'
    TRIGGERED = 'TRIGGERED', 'Triggered'
    PAUSED = 'PAUSED', 'Paused'
    EXPIRED = 'EXPIRED', 'Expired'
    CANCELLED = 'CANCELLED', 'Cancelled'


class NotificationChannel(models.TextChoices):
    """Notification channel choices."""
    EMAIL = 'EMAIL', 'Email'
    SMS = 'SMS', 'SMS'
    PUSH = 'PUSH', 'Push Notification'
    IN_APP = 'IN_APP', 'In-App Notification'


class PriceAlert(BaseModel):
    """Model for price-based alerts."""

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='price_alerts')
    asset = models.ForeignKey(Asset, on_delete=models.CASCADE, related_name='price_alerts', null=True, blank=True)
    portfolio = models.ForeignKey(Portfolio, on_delete=models.CASCADE, related_name='price_alerts', null=True, blank=True)

    # Alert configuration
    alert_type = models.CharField(max_length=20, choices=AlertType.choices)
    threshold_value = models.DecimalField(max_digits=15, decimal_places=4)
    comparison_operator = models.CharField(
        max_length=10,
        choices=[
            ('GT', 'Greater Than'),
            ('LT', 'Less Than'),
            ('GTE', 'Greater Than or Equal'),
            ('LTE', 'Less Than or Equal'),
        ],
        default='GT'
    )

    # Alert metadata
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    status = models.CharField(max_length=20, choices=AlertStatus.choices, default=AlertStatus.ACTIVE)

    # Notification settings
    notification_channels = models.JSONField(default=list)  # List of channels to notify
    notification_message = models.TextField(blank=True)

    # Timing
    expires_at = models.DateTimeField(null=True, blank=True)
    last_checked_at = models.DateTimeField(null=True, blank=True)
    triggered_at = models.DateTimeField(null=True, blank=True)

    # Frequency control
    max_triggers = models.IntegerField(default=1)  # Max times this alert can trigger
    trigger_count = models.IntegerField(default=0)
    cooldown_minutes = models.IntegerField(default=60)  # Cooldown between triggers

    class Meta:
        db_table = 'alerts_price_alert'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'status']),
            models.Index(fields=['asset', 'status']),
            models.Index(fields=['portfolio', 'status']),
            models.Index(fields=['alert_type', 'status']),
            models.Index(fields=['expires_at']),
        ]

    def __str__(self):
        target = self.asset.symbol if self.asset else self.portfolio.name if self.portfolio else 'Portfolio'
        return f"{self.user.email} - {target} {self.alert_type} {self.threshold_value}"

    def is_expired(self):
        """Check if alert is expired."""
        if self.expires_at:
            return timezone.now() > self.expires_at
        return False

    def can_trigger(self):
        """Check if alert can trigger based on cooldown and max triggers."""
        if self.status != AlertStatus.ACTIVE:
            return False

        if self.is_expired():
            return False

        if self.trigger_count >= self.max_triggers:
            return False

        if self.triggered_at and self.cooldown_minutes > 0:
            cooldown_end = self.triggered_at + timezone.timedelta(minutes=self.cooldown_minutes)
            if timezone.now() < cooldown_end:
                return False

        return True

    def trigger(self):
        """Mark alert as triggered."""
        self.triggered_at = timezone.now()
        self.trigger_count += 1

        if self.trigger_count >= self.max_triggers:
            self.status = AlertStatus.TRIGGERED

        self.save(update_fields=['triggered_at', 'trigger_count', 'status'])


class AlertHistory(BaseModel):
    """Model for tracking alert trigger history."""

    alert = models.ForeignKey(PriceAlert, on_delete=models.CASCADE, related_name='history')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='alert_history')

    # Trigger details
    triggered_value = models.DecimalField(max_digits=15, decimal_places=4)
    threshold_value = models.DecimalField(max_digits=15, decimal_places=4)
    message = models.TextField()

    # Notification tracking
    notifications_sent = models.JSONField(default=list)  # List of successful notifications
    notification_failures = models.JSONField(default=list)  # List of failed notifications

    class Meta:
        db_table = 'alerts_alert_history'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'created_at']),
            models.Index(fields=['alert', 'created_at']),
        ]

    def __str__(self):
        return f"Alert {self.alert.id} triggered at {self.created_at}"


class NotificationPreference(BaseModel):
    """Model for user notification preferences."""

    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='notification_preferences')

    # Channel preferences
    email_enabled = models.BooleanField(default=True)
    sms_enabled = models.BooleanField(default=False)
    push_enabled = models.BooleanField(default=True)
    in_app_enabled = models.BooleanField(default=True)

    # Contact information
    email_address = models.EmailField(blank=True)  # Override user email if needed
    phone_number = models.CharField(max_length=20, blank=True)

    # Timing preferences
    quiet_hours_start = models.TimeField(null=True, blank=True)  # e.g., 22:00
    quiet_hours_end = models.TimeField(null=True, blank=True)    # e.g., 08:00
    timezone = models.CharField(max_length=50, default='UTC')

    # Frequency limits
    max_emails_per_day = models.IntegerField(default=50)
    max_sms_per_day = models.IntegerField(default=10)
    max_push_per_hour = models.IntegerField(default=20)

    # Alert type preferences
    price_alerts_enabled = models.BooleanField(default=True)
    portfolio_alerts_enabled = models.BooleanField(default=True)
    news_alerts_enabled = models.BooleanField(default=True)
    security_alerts_enabled = models.BooleanField(default=True)

    class Meta:
        db_table = 'alerts_notification_preference'

    def __str__(self):
        return f"{self.user.email} - Notification Preferences"

    def is_quiet_hours(self):
        """Check if current time is within quiet hours."""
        if not self.quiet_hours_start or not self.quiet_hours_end:
            return False

        from django.utils import timezone as tz
        import pytz

        user_tz = pytz.timezone(self.timezone)
        current_time = tz.now().astimezone(user_tz).time()

        if self.quiet_hours_start <= self.quiet_hours_end:
            # Same day quiet hours (e.g., 22:00 to 23:59)
            return self.quiet_hours_start <= current_time <= self.quiet_hours_end
        else:
            # Overnight quiet hours (e.g., 22:00 to 08:00)
            return current_time >= self.quiet_hours_start or current_time <= self.quiet_hours_end


class Notification(BaseModel):
    """Model for tracking sent notifications."""

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='notifications')
    alert = models.ForeignKey(PriceAlert, on_delete=models.CASCADE, related_name='notifications', null=True, blank=True)

    # Notification details
    channel = models.CharField(max_length=20, choices=NotificationChannel.choices)
    recipient = models.CharField(max_length=255)  # Email address, phone number, etc.
    subject = models.CharField(max_length=255)
    message = models.TextField()

    # Delivery tracking
    status = models.CharField(
        max_length=20,
        choices=[
            ('PENDING', 'Pending'),
            ('SENT', 'Sent'),
            ('DELIVERED', 'Delivered'),
            ('FAILED', 'Failed'),
            ('BOUNCED', 'Bounced'),
        ],
        default='PENDING'
    )

    sent_at = models.DateTimeField(null=True, blank=True)
    delivered_at = models.DateTimeField(null=True, blank=True)
    error_message = models.TextField(blank=True)

    # External tracking
    external_id = models.CharField(max_length=255, blank=True)  # Provider message ID
    provider = models.CharField(max_length=50, blank=True)  # SendGrid, Twilio, etc.

    # Metadata
    metadata = models.JSONField(default=dict)

    class Meta:
        db_table = 'alerts_notification'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'created_at']),
            models.Index(fields=['channel', 'status']),
            models.Index(fields=['status', 'created_at']),
        ]

    def __str__(self):
        return f"{self.channel} to {self.recipient} - {self.status}"

    def mark_sent(self, external_id=None, provider=None):
        """Mark notification as sent."""
        self.status = 'SENT'
        self.sent_at = timezone.now()
        if external_id:
            self.external_id = external_id
        if provider:
            self.provider = provider
        self.save(update_fields=['status', 'sent_at', 'external_id', 'provider'])

    def mark_delivered(self):
        """Mark notification as delivered."""
        self.status = 'DELIVERED'
        self.delivered_at = timezone.now()
        self.save(update_fields=['status', 'delivered_at'])

    def mark_failed(self, error_message):
        """Mark notification as failed."""
        self.status = 'FAILED'
        self.error_message = error_message
        self.save(update_fields=['status', 'error_message'])


class Report(BaseModel):
    """Model for generated reports."""

    REPORT_TYPES = [
        ('PORTFOLIO_PERFORMANCE', 'Portfolio Performance'),
        ('TRANSACTION_HISTORY', 'Transaction History'),
        ('TAX_REPORT', 'Tax Report'),
        ('RISK_ANALYSIS', 'Risk Analysis'),
        ('ALLOCATION_REPORT', 'Asset Allocation'),
        ('DIVIDEND_REPORT', 'Dividend Report'),
        ('CUSTOM', 'Custom Report'),
    ]

    REPORT_FORMATS = [
        ('PDF', 'PDF'),
        ('EXCEL', 'Excel'),
        ('CSV', 'CSV'),
        ('JSON', 'JSON'),
    ]

    REPORT_STATUS = [
        ('PENDING', 'Pending'),
        ('GENERATING', 'Generating'),
        ('COMPLETED', 'Completed'),
        ('FAILED', 'Failed'),
        ('EXPIRED', 'Expired'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='reports')
    portfolio = models.ForeignKey(Portfolio, on_delete=models.CASCADE, related_name='reports', null=True, blank=True)

    # Report configuration
    report_type = models.CharField(max_length=30, choices=REPORT_TYPES)
    report_format = models.CharField(max_length=10, choices=REPORT_FORMATS, default='PDF')
    name = models.CharField(max_length=200)
    description = models.TextField(blank=True)

    # Date range
    start_date = models.DateField()
    end_date = models.DateField()

    # Generation details
    status = models.CharField(max_length=20, choices=REPORT_STATUS, default='PENDING')
    parameters = models.JSONField(default=dict)  # Report-specific parameters

    # File details
    file_path = models.CharField(max_length=500, blank=True)
    file_size = models.BigIntegerField(null=True, blank=True)  # Size in bytes
    file_hash = models.CharField(max_length=64, blank=True)  # SHA-256 hash

    # Timing
    generated_at = models.DateTimeField(null=True, blank=True)
    expires_at = models.DateTimeField(null=True, blank=True)

    # Error tracking
    error_message = models.TextField(blank=True)
    generation_time = models.DurationField(null=True, blank=True)

    class Meta:
        db_table = 'alerts_report'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'created_at']),
            models.Index(fields=['portfolio', 'created_at']),
            models.Index(fields=['report_type', 'status']),
            models.Index(fields=['status', 'created_at']),
            models.Index(fields=['expires_at']),
        ]

    def __str__(self):
        return f"{self.user.email} - {self.report_type} - {self.status}"

    def is_expired(self):
        """Check if report is expired."""
        if self.expires_at:
            return timezone.now() > self.expires_at
        return False

    def mark_generating(self):
        """Mark report as generating."""
        self.status = 'GENERATING'
        self.save(update_fields=['status'])

    def mark_completed(self, file_path, file_size=None, file_hash=None):
        """Mark report as completed."""
        self.status = 'COMPLETED'
        self.generated_at = timezone.now()
        self.file_path = file_path
        if file_size:
            self.file_size = file_size
        if file_hash:
            self.file_hash = file_hash
        self.save(update_fields=['status', 'generated_at', 'file_path', 'file_size', 'file_hash'])

    def mark_failed(self, error_message):
        """Mark report as failed."""
        self.status = 'FAILED'
        self.error_message = error_message
        self.save(update_fields=['status', 'error_message'])

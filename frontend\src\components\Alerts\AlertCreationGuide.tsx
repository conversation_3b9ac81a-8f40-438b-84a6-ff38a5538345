// TrustVault - Alert Creation Guide Component

import React from 'react';
import {
  Box,
  Typography,
  Alert,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip,
} from '@mui/material';
import {
  ExpandMore,
  TrendingUp,
  TrendingDown,
  ShowChart,
  VolumeUp,
  AccountBalance,
  ChangeCircle,
  Info,
} from '@mui/icons-material';

const AlertCreationGuide: React.FC = () => {
  const alertTypes = [
    {
      category: 'Asset Alerts',
      icon: <TrendingUp color="primary" />,
      description: 'Monitor individual stocks, crypto, or other assets',
      types: [
        {
          name: 'PRICE_ABOVE',
          label: 'Price Above',
          icon: <TrendingUp fontSize="small" />,
          description: 'Triggers when asset price goes above your threshold',
          example: 'Alert me when AAPL goes above $160',
        },
        {
          name: 'PRICE_BELOW',
          label: 'Price Below',
          icon: <TrendingDown fontSize="small" />,
          description: 'Triggers when asset price goes below your threshold',
          example: 'Alert me when TSLA goes below $200',
        },
        {
          name: 'PRICE_CHANGE',
          label: 'Price Change %',
          icon: <ShowChart fontSize="small" />,
          description: 'Triggers when asset price changes by a percentage',
          example: 'Alert me when MSFT changes by +/-5%',
        },
        {
          name: 'VOLUME_SPIKE',
          label: 'Volume Spike',
          icon: <VolumeUp fontSize="small" />,
          description: 'Triggers when trading volume spikes unusually',
          example: 'Alert me when GOOGL volume is 3x normal',
        },
      ],
    },
    {
      category: 'Portfolio Alerts',
      icon: <AccountBalance color="secondary" />,
      description: 'Monitor your entire portfolio performance',
      types: [
        {
          name: 'PORTFOLIO_VALUE',
          label: 'Portfolio Value',
          icon: <AccountBalance fontSize="small" />,
          description: 'Triggers when total portfolio value reaches threshold',
          example: 'Alert me when portfolio reaches $100,000',
        },
        {
          name: 'PORTFOLIO_CHANGE',
          label: 'Portfolio Change %',
          icon: <ChangeCircle fontSize="small" />,
          description: 'Triggers when portfolio changes by percentage',
          example: 'Alert me when portfolio gains/loses 10%',
        },
      ],
    },
  ];

  return (
    <Box sx={{ mb: 3 }}>
      <Alert severity="info" sx={{ mb: 2 }}>
        <Typography variant="body2" sx={{ mb: 1 }}>
          <strong>Quick Guide:</strong> Choose either an <strong>Asset</strong> (for individual stocks/crypto) 
          or a <strong>Portfolio</strong> (for overall performance), but not both.
        </Typography>
        <Typography variant="body2">
          The alert type you select will determine which option is required.
        </Typography>
      </Alert>

      {alertTypes.map((category) => (
        <Accordion key={category.category} sx={{ mb: 1 }}>
          <AccordionSummary expandIcon={<ExpandMore />}>
            <Box display="flex" alignItems="center" gap={2}>
              {category.icon}
              <Box>
                <Typography variant="h6">{category.category}</Typography>
                <Typography variant="body2" color="text.secondary">
                  {category.description}
                </Typography>
              </Box>
            </Box>
          </AccordionSummary>
          <AccordionDetails>
            <List dense>
              {category.types.map((type) => (
                <ListItem key={type.name} sx={{ py: 1 }}>
                  <ListItemIcon>{type.icon}</ListItemIcon>
                  <ListItemText
                    primary={
                      <Box display="flex" alignItems="center" gap={1}>
                        <Typography variant="subtitle2">{type.label}</Typography>
                        <Chip
                          label={category.category === 'Asset Alerts' ? 'Asset Required' : 'Portfolio Required'}
                          size="small"
                          color={category.category === 'Asset Alerts' ? 'primary' : 'secondary'}
                          variant="outlined"
                        />
                      </Box>
                    }
                    secondary={
                      <Box>
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 0.5 }}>
                          {type.description}
                        </Typography>
                        <Typography variant="caption" color="primary" sx={{ fontStyle: 'italic' }}>
                          Example: {type.example}
                        </Typography>
                      </Box>
                    }
                  />
                </ListItem>
              ))}
            </List>
          </AccordionDetails>
        </Accordion>
      ))}

      <Alert severity="warning" sx={{ mt: 2 }}>
        <Box display="flex" alignItems="flex-start" gap={1}>
          <Info fontSize="small" />
          <Box>
            <Typography variant="body2" sx={{ fontWeight: 'bold', mb: 0.5 }}>
              Important Notes:
            </Typography>
            <Typography variant="body2" component="div">
              • You can only select <strong>either</strong> an asset <strong>or</strong> a portfolio, not both
              <br />
              • Asset alerts monitor individual stocks/crypto prices and volume
              <br />
              • Portfolio alerts monitor your overall portfolio performance
              <br />
              • Choose notification channels: Email, SMS, Push, or In-App
              <br />
              • Set cooldown periods to avoid spam notifications
            </Typography>
          </Box>
        </Box>
      </Alert>
    </Box>
  );
};

export default AlertCreationGuide;

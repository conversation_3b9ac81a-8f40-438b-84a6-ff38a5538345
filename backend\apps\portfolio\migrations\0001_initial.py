# Generated by Django 4.2.7 on 2025-07-27 13:20

from decimal import Decimal
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Asset',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('symbol', models.CharField(max_length=20, unique=True)),
                ('name', models.CharField(max_length=200)),
                ('asset_type', models.CharField(choices=[('STOCK', 'Stock'), ('BOND', 'Bond'), ('ETF', 'ETF'), ('MUTUAL_FUND', 'Mutual Fund'), ('CRYPTO', 'Cryptocurrency'), ('COMMODITY', 'Commodity'), ('REAL_ESTATE', 'Real Estate'), ('CASH', 'Cash')], max_length=20)),
                ('description', models.TextField(blank=True)),
                ('current_price', models.DecimalField(decimal_places=4, max_digits=15)),
                ('currency', models.CharField(default='USD', max_length=3)),
                ('market_cap', models.DecimalField(blank=True, decimal_places=2, max_digits=20, null=True)),
                ('volume_24h', models.DecimalField(blank=True, decimal_places=2, max_digits=20, null=True)),
                ('change_24h', models.DecimalField(blank=True, decimal_places=4, max_digits=10, null=True)),
                ('sector', models.CharField(blank=True, max_length=100)),
                ('country', models.CharField(blank=True, max_length=100)),
            ],
            options={
                'db_table': 'portfolio_asset',
                'ordering': ['symbol'],
            },
        ),
        migrations.CreateModel(
            name='Portfolio',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True)),
                ('portfolio_type', models.CharField(choices=[('CONSERVATIVE', 'Conservative'), ('MODERATE', 'Moderate'), ('AGGRESSIVE', 'Aggressive'), ('CUSTOM', 'Custom')], default='MODERATE', max_length=20)),
                ('total_value', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=15)),
                ('currency', models.CharField(default='USD', max_length=3)),
                ('is_public', models.BooleanField(default=False)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='portfolios', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'portfolio_portfolio',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Watchlist',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True)),
            ],
            options={
                'db_table': 'portfolio_watchlist',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='WatchlistItem',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('target_price', models.DecimalField(blank=True, decimal_places=4, max_digits=15, null=True)),
                ('notes', models.TextField(blank=True)),
                ('asset', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='portfolio.asset')),
                ('watchlist', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='portfolio.watchlist')),
            ],
            options={
                'db_table': 'portfolio_watchlist_item',
            },
        ),
        migrations.AddField(
            model_name='watchlist',
            name='assets',
            field=models.ManyToManyField(through='portfolio.WatchlistItem', to='portfolio.asset'),
        ),
        migrations.AddField(
            model_name='watchlist',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='watchlists', to=settings.AUTH_USER_MODEL),
        ),
        migrations.CreateModel(
            name='Transaction',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('transaction_type', models.CharField(choices=[('BUY', 'Buy'), ('SELL', 'Sell'), ('DIVIDEND', 'Dividend'), ('SPLIT', 'Stock Split'), ('DEPOSIT', 'Cash Deposit'), ('WITHDRAWAL', 'Cash Withdrawal')], max_length=20)),
                ('quantity', models.DecimalField(blank=True, decimal_places=8, max_digits=20, null=True)),
                ('price', models.DecimalField(blank=True, decimal_places=4, max_digits=15, null=True)),
                ('total_amount', models.DecimalField(decimal_places=2, max_digits=15)),
                ('fees', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10)),
                ('notes', models.TextField(blank=True)),
                ('transaction_date', models.DateTimeField()),
                ('asset', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='transactions', to='portfolio.asset')),
                ('portfolio', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transactions', to='portfolio.portfolio')),
            ],
            options={
                'db_table': 'portfolio_transaction',
                'ordering': ['-transaction_date'],
            },
        ),
        migrations.CreateModel(
            name='PortfolioSnapshot',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('total_value', models.DecimalField(decimal_places=2, max_digits=15)),
                ('snapshot_data', models.JSONField()),
                ('snapshot_date', models.DateTimeField()),
                ('portfolio', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='snapshots', to='portfolio.portfolio')),
            ],
            options={
                'db_table': 'portfolio_snapshot',
                'ordering': ['-snapshot_date'],
            },
        ),
        migrations.CreateModel(
            name='Holding',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('quantity', models.DecimalField(decimal_places=8, max_digits=20)),
                ('average_cost', models.DecimalField(decimal_places=4, max_digits=15)),
                ('current_value', models.DecimalField(decimal_places=2, max_digits=15)),
                ('asset', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='holdings', to='portfolio.asset')),
                ('portfolio', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='holdings', to='portfolio.portfolio')),
            ],
            options={
                'db_table': 'portfolio_holding',
            },
        ),
        migrations.AddIndex(
            model_name='asset',
            index=models.Index(fields=['symbol'], name='portfolio_a_symbol_d2c1df_idx'),
        ),
        migrations.AddIndex(
            model_name='asset',
            index=models.Index(fields=['asset_type'], name='portfolio_a_asset_t_3401a4_idx'),
        ),
        migrations.AddIndex(
            model_name='asset',
            index=models.Index(fields=['sector'], name='portfolio_a_sector_dd03c6_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='watchlistitem',
            unique_together={('watchlist', 'asset')},
        ),
        migrations.AddIndex(
            model_name='watchlist',
            index=models.Index(fields=['user', 'created_at'], name='portfolio_w_user_id_a35bf5_idx'),
        ),
        migrations.AddIndex(
            model_name='transaction',
            index=models.Index(fields=['portfolio', 'transaction_date'], name='portfolio_t_portfol_7a5ee5_idx'),
        ),
        migrations.AddIndex(
            model_name='transaction',
            index=models.Index(fields=['transaction_type', 'transaction_date'], name='portfolio_t_transac_7ab0b0_idx'),
        ),
        migrations.AddIndex(
            model_name='transaction',
            index=models.Index(fields=['asset', 'transaction_date'], name='portfolio_t_asset_i_54ba65_idx'),
        ),
        migrations.AddIndex(
            model_name='portfoliosnapshot',
            index=models.Index(fields=['portfolio', 'snapshot_date'], name='portfolio_s_portfol_2ea411_idx'),
        ),
        migrations.AddIndex(
            model_name='portfolio',
            index=models.Index(fields=['user', 'created_at'], name='portfolio_p_user_id_1d8a4a_idx'),
        ),
        migrations.AddIndex(
            model_name='portfolio',
            index=models.Index(fields=['portfolio_type'], name='portfolio_p_portfol_d74bc2_idx'),
        ),
        migrations.AddIndex(
            model_name='portfolio',
            index=models.Index(fields=['is_public'], name='portfolio_p_is_publ_098245_idx'),
        ),
        migrations.AddIndex(
            model_name='holding',
            index=models.Index(fields=['portfolio', 'asset'], name='portfolio_h_portfol_b01280_idx'),
        ),
        migrations.AddIndex(
            model_name='holding',
            index=models.Index(fields=['current_value'], name='portfolio_h_current_fdcb79_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='holding',
            unique_together={('portfolio', 'asset')},
        ),
    ]

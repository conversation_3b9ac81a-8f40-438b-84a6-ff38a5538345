# TrustVault - Nginx Configuration Sécurisée
user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

# Security: Hide nginx version
server_tokens off;

events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # ========================================================================
    # SECURITY CONFIGURATIONS
    # ========================================================================
    
    # Hide server information
    server_tokens off;
    more_clear_headers Server;
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self'; frame-ancestors 'self';" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
    add_header Permissions-Policy "geolocation=(), microphone=(), camera=()" always;

    # Rate limiting zones
    limit_req_zone $binary_remote_addr zone=login:10m rate=5r/m;
    limit_req_zone $binary_remote_addr zone=api:10m rate=100r/m;
    limit_req_zone $binary_remote_addr zone=general:10m rate=200r/m;
    
    # Connection limiting
    limit_conn_zone $binary_remote_addr zone=conn_limit_per_ip:10m;
    limit_conn conn_limit_per_ip 20;

    # ========================================================================
    # LOGGING CONFIGURATION
    # ========================================================================
    
    # Custom log format for security monitoring
    log_format security_log '$remote_addr - $remote_user [$time_local] '
                           '"$request" $status $body_bytes_sent '
                           '"$http_referer" "$http_user_agent" '
                           '$request_time $upstream_response_time '
                           '$gzip_ratio "$http_x_forwarded_for" '
                           '"$http_x_real_ip" "$ssl_protocol" "$ssl_cipher"';

    # Main access log
    access_log /var/log/nginx/access.log security_log;
    
    # Security events log
    access_log /var/log/nginx/security.log security_log;

    # ========================================================================
    # PERFORMANCE OPTIMIZATIONS
    # ========================================================================
    
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 10M;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # ========================================================================
    # SSL/TLS CONFIGURATION
    # ========================================================================
    
    # SSL protocols and ciphers
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    
    # SSL session cache
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    ssl_session_tickets off;
    
    # OCSP stapling
    ssl_stapling on;
    ssl_stapling_verify on;
    resolver ******* ******* valid=300s;
    resolver_timeout 5s;

    # ========================================================================
    # UPSTREAM BACKENDS
    # ========================================================================
    
    upstream django_backend {
        least_conn;
        server django:8000 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }
    
    upstream react_frontend {
        least_conn;
        server frontend:3000 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }

    # ========================================================================
    # GEO BLOCKING (Example)
    # ========================================================================
    
    # Block specific countries (example)
    geo $blocked_country {
        default 0;
        # Add country IP ranges here if needed
        # *******/24 1; # Example blocked range
    }

    # ========================================================================
    # INCLUDE VIRTUAL HOSTS
    # ========================================================================
    
    include /etc/nginx/conf.d/*.conf;
}

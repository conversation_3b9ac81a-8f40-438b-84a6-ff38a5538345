# TrustVault - Virtual Host Configuration Sécurisée

# Redirect HTTP to HTTPS
server {
    listen 80;
    server_name trustvault.local api.trustvault.local;
    
    # Security headers even for redirects
    add_header X-Frame-Options "DENY" always;
    add_header X-Content-Type-Options "nosniff" always;
    
    # Log security events
    access_log /var/log/nginx/http_redirect.log security_log;
    
    # Redirect all HTTP traffic to HTTPS
    return 301 https://$server_name$request_uri;
}

# Main HTTPS Server - Frontend
server {
    listen 443 ssl http2;
    server_name trustvault.local;
    
    # ========================================================================
    # SSL/TLS CONFIGURATION
    # ========================================================================
    
    ssl_certificate /etc/nginx/ssl/trustvault.crt;
    ssl_certificate_key /etc/nginx/ssl/trustvault.key;
    ssl_trusted_certificate /etc/nginx/ssl/ca.crt;
    
    # SSL security settings
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    
    # ========================================================================
    # SECURITY CONFIGURATIONS
    # ========================================================================
    
    # Rate limiting
    limit_req zone=general burst=50 nodelay;
    limit_conn conn_limit_per_ip 10;
    
    # Security headers
    include /etc/nginx/security-headers.conf;
    
    # Block common attack patterns
    location ~* \.(php|asp|aspx|jsp)$ {
        deny all;
        access_log /var/log/nginx/blocked_requests.log security_log;
    }
    
    # Block hidden files
    location ~ /\. {
        deny all;
        access_log /var/log/nginx/blocked_requests.log security_log;
    }
    
    # Block common exploit attempts
    location ~* (eval\(|base64_decode|gzinflate|rot13|str_rot13) {
        deny all;
        access_log /var/log/nginx/blocked_requests.log security_log;
    }
    
    # ========================================================================
    # APPLICATION ROUTING
    # ========================================================================
    
    # Health check endpoint
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
    
    # Frontend React application
    location / {
        proxy_pass http://react_frontend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        
        # Security headers for proxied content
        proxy_hide_header X-Powered-By;
        
        # Timeouts
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        
        # Buffer settings
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
    }
    
    # Static files caching
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        proxy_pass http://react_frontend;
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header X-Content-Type-Options "nosniff";
    }
    
    # Logging
    access_log /var/log/nginx/trustvault_access.log security_log;
    error_log /var/log/nginx/trustvault_error.log warn;
}

# API Server - Backend Django
server {
    listen 443 ssl http2;
    server_name api.trustvault.local;
    
    # ========================================================================
    # SSL/TLS CONFIGURATION
    # ========================================================================
    
    ssl_certificate /etc/nginx/ssl/trustvault.crt;
    ssl_certificate_key /etc/nginx/ssl/trustvault.key;
    ssl_trusted_certificate /etc/nginx/ssl/ca.crt;
    
    # ========================================================================
    # SECURITY CONFIGURATIONS
    # ========================================================================
    
    # Strict rate limiting for API
    limit_req zone=api burst=20 nodelay;
    limit_conn conn_limit_per_ip 5;
    
    # Extra security for API
    add_header X-Frame-Options "DENY" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Strict-Transport-Security "max-age=********; includeSubDomains; preload" always;
    
    # API-specific CSP
    add_header Content-Security-Policy "default-src 'none'; frame-ancestors 'none';" always;
    
    # ========================================================================
    # API ROUTING
    # ========================================================================
    
    # Health check
    location /health {
        access_log off;
        return 200 "api-healthy\n";
        add_header Content-Type text/plain;
    }
    
    # Authentication endpoints - extra rate limiting
    location ~* ^/(api/v1/auth|api/v1/login|api/v1/register) {
        limit_req zone=login burst=3 nodelay;
        
        proxy_pass http://django_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Log all authentication attempts
        access_log /var/log/nginx/auth_attempts.log security_log;
    }
    
    # Main API endpoints
    location /api/ {
        proxy_pass http://django_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        
        # Security headers for API
        proxy_hide_header X-Powered-By;
        proxy_hide_header Server;
        
        # Timeouts for API
        proxy_connect_timeout 10s;
        proxy_send_timeout 10s;
        proxy_read_timeout 30s;
        
        # No caching for API responses
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires "0";
    }
    
    # Django admin (restricted access)
    location /admin/ {
        # Allow only specific IPs (example)
        allow 127.0.0.1;
        allow 10.0.0.0/8;
        allow **********/12;
        allow ***********/16;
        deny all;
        
        proxy_pass http://django_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Log admin access
        access_log /var/log/nginx/admin_access.log security_log;
    }
    
    # Block everything else
    location / {
        deny all;
        access_log /var/log/nginx/blocked_requests.log security_log;
    }
    
    # Logging
    access_log /var/log/nginx/api_access.log security_log;
    error_log /var/log/nginx/api_error.log warn;
}

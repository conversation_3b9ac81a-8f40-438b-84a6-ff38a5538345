# TrustVault - Alerts Metrics

import logging
from django.db.models import Count, Q
from django.utils import timezone
from datetime import timedelta
from .models import PriceAlert, AlertHistory, Notification, Report, AlertStatus

logger = logging.getLogger(__name__)


class AlertsMetrics:
    """Metrics collector for alerts system."""
    
    @staticmethod
    def get_alert_counts():
        """Get alert counts by status."""
        try:
            counts = PriceAlert.objects.aggregate(
                total=Count('id'),
                active=Count('id', filter=Q(status=AlertStatus.ACTIVE)),
                triggered=Count('id', filter=Q(status=AlertStatus.TRIGGERED)),
                paused=Count('id', filter=Q(status=AlertStatus.PAUSED)),
                expired=Count('id', filter=Q(status=AlertStatus.EXPIRED)),
                cancelled=Count('id', filter=Q(status=AlertStatus.CANCELLED))
            )
            
            logger.info(f"Alert counts collected: {counts}")
            return counts
            
        except Exception as e:
            logger.error(f"Failed to collect alert counts: {e}")
            return {}
    
    @staticmethod
    def get_alert_triggers_last_24h():
        """Get number of alert triggers in the last 24 hours."""
        try:
            since = timezone.now() - timedelta(hours=24)
            count = AlertHistory.objects.filter(created_at__gte=since).count()
            
            logger.info(f"Alert triggers in last 24h: {count}")
            return count
            
        except Exception as e:
            logger.error(f"Failed to collect alert triggers: {e}")
            return 0
    
    @staticmethod
    def get_notification_stats():
        """Get notification statistics."""
        try:
            since = timezone.now() - timedelta(hours=24)
            
            stats = Notification.objects.filter(created_at__gte=since).aggregate(
                total=Count('id'),
                sent=Count('id', filter=Q(status='SENT')),
                delivered=Count('id', filter=Q(status='DELIVERED')),
                failed=Count('id', filter=Q(status='FAILED')),
                email=Count('id', filter=Q(channel='EMAIL')),
                sms=Count('id', filter=Q(channel='SMS')),
                push=Count('id', filter=Q(channel='PUSH')),
                in_app=Count('id', filter=Q(channel='IN_APP'))
            )
            
            logger.info(f"Notification stats (24h): {stats}")
            return stats
            
        except Exception as e:
            logger.error(f"Failed to collect notification stats: {e}")
            return {}
    
    @staticmethod
    def get_report_stats():
        """Get report generation statistics."""
        try:
            since = timezone.now() - timedelta(hours=24)
            
            stats = Report.objects.filter(created_at__gte=since).aggregate(
                total=Count('id'),
                pending=Count('id', filter=Q(status='PENDING')),
                generating=Count('id', filter=Q(status='GENERATING')),
                completed=Count('id', filter=Q(status='COMPLETED')),
                failed=Count('id', filter=Q(status='FAILED'))
            )
            
            logger.info(f"Report stats (24h): {stats}")
            return stats
            
        except Exception as e:
            logger.error(f"Failed to collect report stats: {e}")
            return {}
    
    @staticmethod
    def get_user_engagement_metrics():
        """Get user engagement metrics for alerts."""
        try:
            # Users with active alerts
            users_with_alerts = PriceAlert.objects.filter(
                status=AlertStatus.ACTIVE
            ).values('user').distinct().count()
            
            # Average alerts per user
            total_alerts = PriceAlert.objects.count()
            total_users = PriceAlert.objects.values('user').distinct().count()
            avg_alerts_per_user = total_alerts / total_users if total_users > 0 else 0
            
            # Most active alert types
            alert_types = PriceAlert.objects.values('alert_type').annotate(
                count=Count('id')
            ).order_by('-count')[:5]
            
            metrics = {
                'users_with_alerts': users_with_alerts,
                'avg_alerts_per_user': round(avg_alerts_per_user, 2),
                'popular_alert_types': list(alert_types)
            }
            
            logger.info(f"User engagement metrics: {metrics}")
            return metrics
            
        except Exception as e:
            logger.error(f"Failed to collect user engagement metrics: {e}")
            return {}
    
    @classmethod
    def collect_all_metrics(cls):
        """Collect all alerts metrics."""
        try:
            metrics = {
                'timestamp': timezone.now().isoformat(),
                'alert_counts': cls.get_alert_counts(),
                'triggers_24h': cls.get_alert_triggers_last_24h(),
                'notification_stats': cls.get_notification_stats(),
                'report_stats': cls.get_report_stats(),
                'user_engagement': cls.get_user_engagement_metrics()
            }
            
            logger.info("All alerts metrics collected successfully")
            return metrics
            
        except Exception as e:
            logger.error(f"Failed to collect all metrics: {e}")
            return {}


# Prometheus metrics (if prometheus_client is available)
try:
    from prometheus_client import Counter, Gauge, Histogram
    
    # Alert metrics
    alerts_total = Counter(
        'trustvault_alerts_total',
        'Total number of alerts created',
        ['alert_type', 'user_id']
    )
    
    alerts_triggered_total = Counter(
        'trustvault_alerts_triggered_total',
        'Total number of alerts triggered',
        ['alert_type', 'user_id']
    )
    
    alerts_active_gauge = Gauge(
        'trustvault_alerts_active',
        'Number of active alerts',
        ['alert_type']
    )
    
    # Notification metrics
    notifications_sent_total = Counter(
        'trustvault_notifications_sent_total',
        'Total number of notifications sent',
        ['channel', 'status']
    )
    
    notification_delivery_time = Histogram(
        'trustvault_notification_delivery_seconds',
        'Time taken to deliver notifications',
        ['channel']
    )
    
    # Report metrics
    reports_generated_total = Counter(
        'trustvault_reports_generated_total',
        'Total number of reports generated',
        ['report_type', 'format']
    )
    
    report_generation_time = Histogram(
        'trustvault_report_generation_seconds',
        'Time taken to generate reports',
        ['report_type', 'format']
    )
    
    reports_active_gauge = Gauge(
        'trustvault_reports_active',
        'Number of reports being generated',
        ['report_type']
    )
    
    logger.info("Prometheus metrics initialized")
    
except ImportError:
    logger.warning("prometheus_client not available, metrics disabled")
    
    # Create dummy metrics
    class DummyMetric:
        def inc(self, *args, **kwargs):
            pass
        def set(self, *args, **kwargs):
            pass
        def observe(self, *args, **kwargs):
            pass
    
    alerts_total = DummyMetric()
    alerts_triggered_total = DummyMetric()
    alerts_active_gauge = DummyMetric()
    notifications_sent_total = DummyMetric()
    notification_delivery_time = DummyMetric()
    reports_generated_total = DummyMetric()
    report_generation_time = DummyMetric()
    reports_active_gauge = DummyMetric()


def record_alert_created(alert_type: str, user_id: str):
    """Record alert creation metric."""
    alerts_total.inc(alert_type=alert_type, user_id=user_id)
    logger.info(f"Alert created metric recorded: {alert_type} for user {user_id}")


def record_alert_triggered(alert_type: str, user_id: str):
    """Record alert trigger metric."""
    alerts_triggered_total.inc(alert_type=alert_type, user_id=user_id)
    logger.info(f"Alert triggered metric recorded: {alert_type} for user {user_id}")


def record_notification_sent(channel: str, status: str):
    """Record notification sent metric."""
    notifications_sent_total.inc(channel=channel, status=status)
    logger.info(f"Notification sent metric recorded: {channel} - {status}")


def record_report_generated(report_type: str, format: str):
    """Record report generation metric."""
    reports_generated_total.inc(report_type=report_type, format=format)
    logger.info(f"Report generated metric recorded: {report_type} - {format}")


def update_active_alerts_gauge():
    """Update active alerts gauge metrics."""
    try:
        alert_counts = PriceAlert.objects.filter(
            status=AlertStatus.ACTIVE
        ).values('alert_type').annotate(count=Count('id'))
        
        # Reset all gauges to 0 first
        for alert_type in ['PRICE_ABOVE', 'PRICE_BELOW', 'PRICE_CHANGE', 'VOLUME_SPIKE', 
                          'PORTFOLIO_VALUE', 'PORTFOLIO_CHANGE', 'NEWS_ALERT', 'EARNINGS_ALERT']:
            alerts_active_gauge.set(0, alert_type=alert_type)
        
        # Set actual counts
        for item in alert_counts:
            alerts_active_gauge.set(item['count'], alert_type=item['alert_type'])
        
        logger.info("Active alerts gauge updated")
        
    except Exception as e:
        logger.error(f"Failed to update active alerts gauge: {e}")


def update_active_reports_gauge():
    """Update active reports gauge metrics."""
    try:
        report_counts = Report.objects.filter(
            status='GENERATING'
        ).values('report_type').annotate(count=Count('id'))
        
        # Reset all gauges to 0 first
        for report_type in ['PORTFOLIO_PERFORMANCE', 'TRANSACTION_HISTORY', 'TAX_REPORT', 
                           'RISK_ANALYSIS', 'ALLOCATION_REPORT', 'DIVIDEND_REPORT']:
            reports_active_gauge.set(0, report_type=report_type)
        
        # Set actual counts
        for item in report_counts:
            reports_active_gauge.set(item['count'], report_type=item['report_type'])
        
        logger.info("Active reports gauge updated")
        
    except Exception as e:
        logger.error(f"Failed to update active reports gauge: {e}")


# Initialize metrics collector
metrics_collector = AlertsMetrics()

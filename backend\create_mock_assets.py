#!/usr/bin/env python
"""
Create mock assets with specific UUIDs
"""

import os
import django
import uuid

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'trustvault.settings')
django.setup()

from apps.portfolio.models import Asset
from decimal import Decimal

print("🏗️ Creating Mock Assets")
print("=" * 25)

# Define assets with specific UUIDs to match frontend
mock_assets = [
    {
        'id': uuid.UUID('2502b584-6811-49f5-9dc7-03afe790fd22'),  # AAPL (already exists)
        'symbol': 'AAPL',
        'name': 'Apple Inc.',
        'asset_type': 'STOCK',
        'current_price': Decimal('150.00'),
        'currency': 'USD'
    },
    {
        'id': uuid.UUID('b1234567-1234-4567-8901-123456789012'),
        'symbol': 'GOOGL',
        'name': 'Alphabet Inc.',
        'asset_type': 'STOCK',
        'current_price': Decimal('2800.00'),
        'currency': 'USD'
    },
    {
        'id': uuid.UUID('c1234567-1234-4567-8901-123456789012'),
        'symbol': 'MSFT',
        'name': 'Microsoft Corporation',
        'asset_type': 'STOCK',
        'current_price': Decimal('380.00'),
        'currency': 'USD'
    },
    {
        'id': uuid.UUID('d1234567-1234-4567-8901-123456789012'),
        'symbol': 'TSLA',
        'name': 'Tesla Inc.',
        'asset_type': 'STOCK',
        'current_price': Decimal('200.00'),
        'currency': 'USD'
    },
    {
        'id': uuid.UUID('e1234567-1234-4567-8901-123456789012'),
        'symbol': 'AMZN',
        'name': 'Amazon.com Inc.',
        'asset_type': 'STOCK',
        'current_price': Decimal('3200.00'),
        'currency': 'USD'
    }
]

created_count = 0
for asset_data in mock_assets:
    asset, created = Asset.objects.get_or_create(
        id=asset_data['id'],
        defaults={
            'symbol': asset_data['symbol'],
            'name': asset_data['name'],
            'asset_type': asset_data['asset_type'],
            'current_price': asset_data['current_price'],
            'currency': asset_data['currency']
        }
    )
    
    if created:
        print(f"✅ Created: {asset.symbol} (ID: {asset.id})")
        created_count += 1
    else:
        print(f"📝 Exists: {asset.symbol} (ID: {asset.id})")

print(f"\n📊 Summary: {created_count} new assets created")
print(f"📈 Total assets: {Asset.objects.count()}")

print("\n🎯 Assets ready for frontend!")
print("Frontend can now use these UUIDs:")
for asset in Asset.objects.all():
    print(f"  {asset.symbol}: {asset.id}")

print("\n✅ Setup completed!")

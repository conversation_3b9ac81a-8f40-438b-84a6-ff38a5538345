# TrustVault - Authentication Signals

import logging
from django.db.models.signals import post_save, pre_save
from django.dispatch import receiver
from django.utils import timezone
from django.contrib.auth.signals import user_logged_in, user_logged_out, user_login_failed
from .models import User, PasswordHistory
from apps.core.models import AuditLog, SecurityEvent

logger = logging.getLogger(__name__)


@receiver(post_save, sender=User)
def user_created_handler(sender, instance, created, **kwargs):
    """Handle user creation."""
    if created:
        logger.info(f"New user created: {instance.email}")
        
        # Create audit log
        AuditLog.objects.create(
            user=instance,
            action='CREATE',
            resource_type='User',
            resource_id=str(instance.id),
            ip_address='127.0.0.1',  # Default for system actions
            details={
                'email': instance.email,
                'username': instance.username,
                'gdpr_consent': instance.gdpr_consent
            }
        )


@receiver(pre_save, sender=User)
def user_password_changed_handler(sender, instance, **kwargs):
    """Handle password changes."""
    if instance.pk:  # Only for existing users
        try:
            old_user = User.objects.get(pk=instance.pk)
            if old_user.password != instance.password:
                # Password has changed, save old password to history
                PasswordHistory.objects.create(
                    user=instance,
                    password_hash=old_user.password
                )
                
                # Clean up old password history (keep only last 10)
                old_passwords = PasswordHistory.objects.filter(
                    user=instance
                ).order_by('-created_at')[10:]
                
                if old_passwords:
                    PasswordHistory.objects.filter(
                        id__in=[p.id for p in old_passwords]
                    ).delete()
                
                logger.info(f"Password changed for user: {instance.email}")
        except User.DoesNotExist:
            pass


@receiver(user_logged_in)
def user_logged_in_handler(sender, request, user, **kwargs):
    """Handle successful login."""
    # Update last login info
    user.last_login_ip = get_client_ip(request)
    user.last_login_user_agent = request.META.get('HTTP_USER_AGENT', '')
    user.save(update_fields=['last_login_ip', 'last_login_user_agent'])
    
    logger.info(f"User logged in: {user.email} from {get_client_ip(request)}")


@receiver(user_logged_out)
def user_logged_out_handler(sender, request, user, **kwargs):
    """Handle user logout."""
    if user:
        logger.info(f"User logged out: {user.email}")


@receiver(user_login_failed)
def user_login_failed_handler(sender, credentials, request, **kwargs):
    """Handle failed login attempts."""
    email = credentials.get('username', '')
    ip_address = get_client_ip(request)
    
    logger.warning(f"Failed login attempt for {email} from {ip_address}")
    
    # Check for suspicious activity
    from .models import LoginAttempt
    recent_failures = LoginAttempt.objects.filter(
        ip_address=ip_address,
        attempt_type__startswith='FAILED',
        created_at__gte=timezone.now() - timezone.timedelta(minutes=15)
    ).count()
    
    if recent_failures >= 5:
        # Create security event for multiple failed attempts
        SecurityEvent.objects.create(
            event_type='SUSPICIOUS_ACTIVITY',
            risk_level='HIGH',
            source_ip=ip_address,
            user_agent=request.META.get('HTTP_USER_AGENT', ''),
            description=f"Multiple failed login attempts from IP {ip_address}",
            details={
                'attempted_email': email,
                'failure_count': recent_failures + 1,
                'time_window': '15 minutes'
            }
        )
        
        logger.critical(f"Suspicious activity detected: {recent_failures + 1} failed login attempts from {ip_address}")


def get_client_ip(request):
    """Get client IP address from request."""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip

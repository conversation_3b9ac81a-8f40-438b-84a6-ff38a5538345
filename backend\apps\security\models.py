# TrustVault - Security Models

from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
from apps.core.models import BaseModel, SecurityEvent

User = get_user_model()


class ThreatIntelligence(BaseModel):
    """Model for storing threat intelligence data."""
    
    THREAT_TYPES = [
        ('IP_BLACKLIST', 'IP Blacklist'),
        ('DOMAIN_BLACKLIST', 'Domain Blacklist'),
        ('MALWARE_HASH', 'Malware Hash'),
        ('IOC', 'Indicator of Compromise'),
        ('CVE', 'Common Vulnerabilities and Exposures'),
        ('SIGNATURE', 'Attack Signature'),
    ]
    
    threat_type = models.CharField(max_length=20, choices=THREAT_TYPES)
    value = models.TextField()  # IP, domain, hash, etc.
    description = models.TextField()
    severity = models.CharField(max_length=10, choices=SecurityEvent.RISK_LEVELS)
    
    # Source information
    source = models.Char<PERSON>ield(max_length=100)  # MISP, AlienVault, etc.
    confidence = models.IntegerField(default=50)  # 0-100
    
    # Validity
    first_seen = models.DateTimeField(default=timezone.now)
    last_seen = models.DateTimeField(default=timezone.now)
    expires_at = models.DateTimeField(null=True, blank=True)
    
    # Additional context
    tags = models.JSONField(default=list, blank=True)
    references = models.JSONField(default=list, blank=True)

    class Meta:
        ordering = ['-last_seen']
        indexes = [
            models.Index(fields=['threat_type', 'expires_at']),
            models.Index(fields=['severity']),
            models.Index(fields=['confidence']),
        ]

    def __str__(self):
        return f"{self.threat_type} - {self.value[:50]}"

    @property
    def is_expired(self):
        """Check if this threat intelligence has expired."""
        if self.expires_at:
            return timezone.now() > self.expires_at
        return False

    @property
    def is_currently_active(self):
        """Check if this threat intelligence is currently active."""
        return not self.is_expired


class UserSession(BaseModel):
    """Model for tracking user sessions with security information."""

    user = models.ForeignKey(User, on_delete=models.CASCADE)
    session_key = models.CharField(max_length=128, unique=True)
    ip_address = models.GenericIPAddressField()
    user_agent = models.TextField()
    risk_score = models.IntegerField(default=0)
    expires_at = models.DateTimeField()
    is_active = models.BooleanField(default=True)

    # Geographic information
    country = models.CharField(max_length=100, blank=True)
    city = models.CharField(max_length=100, blank=True)

    # Device information
    device_fingerprint = models.ForeignKey(
        'DeviceFingerprint',
        on_delete=models.SET_NULL,
        null=True,
        blank=True
    )

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'is_active']),
            models.Index(fields=['session_key']),
            models.Index(fields=['expires_at']),
        ]

    def __str__(self):
        return f"{self.user.email} - {self.ip_address} - {self.created_at}"

    @property
    def is_expired(self):
        """Check if session has expired."""
        return timezone.now() > self.expires_at


class DeviceFingerprint(BaseModel):
    """Model for tracking device fingerprints for security analysis."""

    user = models.ForeignKey(User, on_delete=models.CASCADE)
    fingerprint_hash = models.CharField(max_length=64, unique=True)
    user_agent = models.TextField()
    browser_family = models.CharField(max_length=100, blank=True)
    browser_version = models.CharField(max_length=50, blank=True)
    os_family = models.CharField(max_length=100, blank=True)
    os_version = models.CharField(max_length=50, blank=True)
    device_family = models.CharField(max_length=100, blank=True)
    screen_resolution = models.CharField(max_length=20, blank=True)
    timezone = models.CharField(max_length=50, blank=True)
    language = models.CharField(max_length=10, blank=True)
    plugins = models.JSONField(default=list, blank=True)
    first_seen = models.DateTimeField(auto_now_add=True)
    last_seen = models.DateTimeField(auto_now=True)
    is_trusted = models.BooleanField(default=False)
    trust_score = models.IntegerField(default=0)  # 0-100

    class Meta:
        ordering = ['-last_seen']
        indexes = [
            models.Index(fields=['user', 'last_seen']),
            models.Index(fields=['fingerprint_hash']),
            models.Index(fields=['is_trusted']),
        ]

    def __str__(self):
        return f"{self.user.email} - {self.device_family} - {self.os_family}"


class RiskAssessment(BaseModel):
    """Model for storing risk assessment results."""

    user = models.ForeignKey(User, on_delete=models.CASCADE)
    risk_score = models.IntegerField()  # 0-100
    risk_level = models.CharField(max_length=20, choices=SecurityEvent.RISK_LEVELS)
    risk_factors = models.JSONField(default=list)
    ip_address = models.GenericIPAddressField()
    user_agent = models.TextField()

    # Geographic risk factors
    country = models.CharField(max_length=100, blank=True)
    city = models.CharField(max_length=100, blank=True)
    is_vpn = models.BooleanField(default=False)
    is_tor = models.BooleanField(default=False)

    # Behavioral risk factors
    login_frequency_score = models.IntegerField(default=0)
    device_trust_score = models.IntegerField(default=0)
    time_based_score = models.IntegerField(default=0)
    location_based_score = models.IntegerField(default=0)

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'created_at']),
            models.Index(fields=['risk_level', 'created_at']),
            models.Index(fields=['risk_score']),
        ]

    def __str__(self):
        return f"{self.user.email} - {self.risk_level} ({self.risk_score})"


# IncidentResponse model is defined later in the file


class ComplianceAudit(BaseModel):
    """Model for tracking compliance audits and requirements."""

    COMPLIANCE_FRAMEWORKS = [
        ('ISO_27001', 'ISO 27001'),
        ('SOC_2', 'SOC 2'),
        ('GDPR', 'GDPR'),
        ('HIPAA', 'HIPAA'),
        ('PCI_DSS', 'PCI DSS'),
        ('NIST', 'NIST Cybersecurity Framework'),
        ('CIS', 'CIS Controls'),
        ('COBIT', 'COBIT'),
    ]

    AUDIT_STATUS = [
        ('PLANNED', 'Planned'),
        ('IN_PROGRESS', 'In Progress'),
        ('COMPLETED', 'Completed'),
        ('FAILED', 'Failed'),
        ('REMEDIATION_REQUIRED', 'Remediation Required'),
    ]

    framework = models.CharField(max_length=20, choices=COMPLIANCE_FRAMEWORKS)
    control_id = models.CharField(max_length=50)
    control_name = models.CharField(max_length=200)
    description = models.TextField()
    status = models.CharField(max_length=30, choices=AUDIT_STATUS)

    # Audit details
    auditor = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    audit_date = models.DateTimeField()
    next_audit_date = models.DateTimeField()

    # Results
    compliance_score = models.IntegerField(default=0)  # 0-100
    findings = models.JSONField(default=list, blank=True)
    recommendations = models.JSONField(default=list, blank=True)
    remediation_actions = models.JSONField(default=list, blank=True)

    # Evidence
    evidence_files = models.JSONField(default=list, blank=True)
    documentation_links = models.JSONField(default=list, blank=True)

    class Meta:
        ordering = ['-audit_date']
        indexes = [
            models.Index(fields=['framework', 'status']),
            models.Index(fields=['audit_date']),
            models.Index(fields=['next_audit_date']),
        ]

    def __str__(self):
        return f"{self.framework} - {self.control_id} - {self.status}"


class IncidentResponse(BaseModel):
    """Model for tracking security incidents and responses."""

    INCIDENT_TYPES = [
        ('INTRUSION_ATTEMPT', 'Intrusion Attempt'),
        ('DATA_BREACH', 'Data Breach'),
        ('MALWARE_DETECTION', 'Malware Detection'),
        ('UNAUTHORIZED_ACCESS', 'Unauthorized Access'),
        ('DDOS_ATTACK', 'DDoS Attack'),
        ('PHISHING_ATTEMPT', 'Phishing Attempt'),
        ('INSIDER_THREAT', 'Insider Threat'),
        ('SYSTEM_COMPROMISE', 'System Compromise'),
        ('AUTOMATED_THREAT_RESPONSE', 'Automated Threat Response'),
        ('SECURITY_ASSESSMENT_CRITICAL_FINDINGS', 'Security Assessment Critical Findings'),
    ]

    STATUS_CHOICES = [
        ('OPEN', 'Open'),
        ('IN_PROGRESS', 'In Progress'),
        ('RESOLVED', 'Resolved'),
        ('CLOSED', 'Closed'),
        ('FALSE_POSITIVE', 'False Positive'),
    ]

    incident_type = models.CharField(max_length=50, choices=INCIDENT_TYPES)
    severity = models.CharField(max_length=20, choices=SecurityEvent.RISK_LEVELS)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='OPEN')
    title = models.CharField(max_length=200)
    description = models.TextField()

    # Affected resources
    affected_users = models.ManyToManyField(User, blank=True)
    affected_systems = models.JSONField(default=list, blank=True)

    # Response details
    response_actions = models.JSONField(default=list, blank=True)
    assigned_to = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='assigned_incidents'
    )
    resolved_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='resolved_incidents'
    )
    resolved_at = models.DateTimeField(null=True, blank=True)

    # Timeline
    detected_at = models.DateTimeField(auto_now_add=True)
    first_response_at = models.DateTimeField(null=True, blank=True)
    containment_at = models.DateTimeField(null=True, blank=True)
    eradication_at = models.DateTimeField(null=True, blank=True)
    recovery_at = models.DateTimeField(null=True, blank=True)

    # Additional data
    details = models.JSONField(default=dict, blank=True)
    lessons_learned = models.TextField(blank=True)

    class Meta:
        ordering = ['-detected_at']
        indexes = [
            models.Index(fields=['incident_type', 'status']),
            models.Index(fields=['severity', 'detected_at']),
            models.Index(fields=['assigned_to', 'status']),
        ]

    def __str__(self):
        return f"{self.incident_type} - {self.severity} - {self.status}"


class SecurityMetric(BaseModel):
    """Model for storing security metrics and KPIs."""
    
    METRIC_TYPES = [
        ('EVENT_COUNT', 'Event Count'),
        ('THREAT_LEVEL', 'Threat Level'),
        ('RESPONSE_TIME', 'Response Time'),
        ('UPTIME', 'System Uptime'),
        ('FAILED_LOGINS', 'Failed Login Attempts'),
        ('BLOCKED_IPS', 'Blocked IP Addresses'),
        ('VULNERABILITY_COUNT', 'Vulnerability Count'),
        ('COMPLIANCE_SCORE', 'Compliance Score'),
    ]
    
    metric_type = models.CharField(max_length=20, choices=METRIC_TYPES)
    value = models.DecimalField(max_digits=15, decimal_places=4)
    unit = models.CharField(max_length=20, blank=True)  # count, percentage, seconds, etc.
    
    # Time period
    period_start = models.DateTimeField()
    period_end = models.DateTimeField()
    
    # Context
    context = models.JSONField(default=dict, blank=True)
    
    class Meta:
        db_table = 'security_metric'
        ordering = ['-period_end']
        indexes = [
            models.Index(fields=['metric_type', 'period_end']),
            models.Index(fields=['period_start', 'period_end']),
        ]
    
    def __str__(self):
        return f"{self.metric_type} - {self.value} {self.unit}"


class SecurityAlert(BaseModel):
    """Model for security alerts and notifications."""
    
    ALERT_TYPES = [
        ('THRESHOLD_EXCEEDED', 'Threshold Exceeded'),
        ('ANOMALY_DETECTED', 'Anomaly Detected'),
        ('CRITICAL_EVENT', 'Critical Event'),
        ('SYSTEM_DOWN', 'System Down'),
        ('COMPLIANCE_VIOLATION', 'Compliance Violation'),
        ('MAINTENANCE_REQUIRED', 'Maintenance Required'),
    ]
    
    PRIORITY_LEVELS = [
        ('LOW', 'Low'),
        ('MEDIUM', 'Medium'),
        ('HIGH', 'High'),
        ('URGENT', 'Urgent'),
    ]
    
    alert_type = models.CharField(max_length=25, choices=ALERT_TYPES)
    priority = models.CharField(max_length=10, choices=PRIORITY_LEVELS)
    title = models.CharField(max_length=200)
    message = models.TextField()
    
    # Related objects
    security_event = models.ForeignKey(
        SecurityEvent,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='security_alerts'
    )
    
    # Alert status
    is_acknowledged = models.BooleanField(default=False)
    acknowledged_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='acknowledged_alerts'
    )
    acknowledged_at = models.DateTimeField(null=True, blank=True)
    
    # Notification tracking
    notification_sent = models.BooleanField(default=False)
    notification_channels = models.JSONField(default=list, blank=True)
    
    class Meta:
        db_table = 'security_alert'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['alert_type', 'created_at']),
            models.Index(fields=['priority', 'created_at']),
            models.Index(fields=['is_acknowledged', 'created_at']),
        ]
    
    def __str__(self):
        return f"{self.alert_type} - {self.priority} - {self.title}"
    
    def acknowledge(self, user):
        """Acknowledge this alert."""
        self.is_acknowledged = True
        self.acknowledged_by = user
        self.acknowledged_at = timezone.now()
        self.save(update_fields=['is_acknowledged', 'acknowledged_by', 'acknowledged_at'])

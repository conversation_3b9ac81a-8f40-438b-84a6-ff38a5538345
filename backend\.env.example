# TrustVault - Environment Configuration Template
# Copy this file to .env and configure your values

# Django Configuration
DEBUG=False
SECRET_KEY=your-secret-key-here-change-this-in-production
ALLOWED_HOSTS=localhost,127.0.0.1,trustvault.local

# Database Configuration
POSTGRES_DB=trustvault
POSTGRES_USER=trustvault
DB_PASSWORD=your-secure-database-password
DB_HOST=postgres
DB_PORT=5432

# Development Overrides (set to True for local development)
USE_SQLITE=False
USE_LOCAL_CACHE=False

# Redis Configuration
REDIS_PASSWORD=your-secure-redis-password

# Security Configuration
ENCRYPTION_KEY=your-32-character-encryption-key-here
JWT_SECRET_KEY=your-jwt-secret-key-here

# Email Configuration (for notifications)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-email-password

# External Services
SENTRY_DSN=your-sentry-dsn-for-error-tracking

# Security Features
ENABLE_2FA=True
ENABLE_RATE_LIMITING=True
ENABLE_AUDIT_LOGGING=True

# File Storage
MEDIA_ROOT=/app/media
STATIC_ROOT=/app/static

# Celery Configuration
CELERY_BROKER_URL=redis://redis:6379/0
CELERY_RESULT_BACKEND=redis://redis:6379/0

# Monitoring
ENABLE_MONITORING=True
LOG_LEVEL=INFO

# TrustVault - Alerts App Configuration

from django.apps import AppConfig


class AlertsConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'apps.alerts'
    verbose_name = 'Alerts and Notifications'

    def ready(self):
        """Import signals when app is ready."""
        try:
            import apps.alerts.signals  # noqa F401
        except ImportError:
            pass

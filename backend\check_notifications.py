#!/usr/bin/env python
"""
Quick check of notifications
"""

import os
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'trustvault.settings')
django.setup()

from apps.alerts.models import Notification
from django.contrib.auth import get_user_model

User = get_user_model()

print("🔔 Notification System Check")
print("=" * 30)

# Check notifications
total = Notification.objects.count()
print(f"Total notifications: {total}")

if total > 0:
    print("\nSample notifications:")
    for n in Notification.objects.all()[:5]:
        print(f"  - {n.subject} ({n.channel}) - {n.status}")

# Check users
users = User.objects.count()
print(f"\nTotal users: {users}")

print("\n✅ Check completed!")

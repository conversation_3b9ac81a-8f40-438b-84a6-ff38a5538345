// TrustVault - Notification Dropdown Component

import React, { useState } from 'react';
import {
  IconButton,
  Badge,
  Menu,
  <PERSON>po<PERSON>,
  Box,
  Divider,
  Button,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  CircularProgress,
  Tooltip,
} from '@mui/material';
import {
  Notifications,
  Email,
  Sms,
  PhoneAndroid,
  NotificationsActive,
  MarkEmailRead,
  Clear,
  CheckCircle,
  Error,
  Schedule,
} from '@mui/icons-material';
import { formatDistanceToNow } from 'date-fns';
import { useNotifications } from '../../hooks/useNotifications';

const NotificationDropdown: React.FC = () => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);
  
  const {
    notifications,
    stats,
    isLoading,
    markAsRead,
    markAllAsRead,
    isMarkingAsRead,
    isMarkingAllAsRead,
  } = useNotifications();

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleMarkAsRead = (notificationId: string) => {
    markAsRead(notificationId);
  };

  const handleMarkAllAsRead = () => {
    markAllAsRead();
  };

  const getChannelIcon = (channel: string) => {
    switch (channel) {
      case 'EMAIL':
        return <Email fontSize="small" />;
      case 'SMS':
        return <Sms fontSize="small" />;
      case 'PUSH':
        return <PhoneAndroid fontSize="small" />;
      case 'IN_APP':
        return <NotificationsActive fontSize="small" />;
      default:
        return <Notifications fontSize="small" />;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'DELIVERED':
        return <CheckCircle fontSize="small" color="success" />;
      case 'FAILED':
      case 'BOUNCED':
        return <Error fontSize="small" color="error" />;
      case 'SENT':
        return <CheckCircle fontSize="small" color="primary" />;
      case 'PENDING':
        return <Schedule fontSize="small" color="warning" />;
      default:
        return null;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'DELIVERED':
        return 'success';
      case 'FAILED':
      case 'BOUNCED':
        return 'error';
      case 'SENT':
        return 'primary';
      case 'PENDING':
        return 'warning';
      default:
        return 'default';
    }
  };

  // Filter to show only in-app notifications in the dropdown
  const inAppNotifications = notifications.filter(n => n.channel === 'IN_APP').slice(0, 10);

  return (
    <>
      <Tooltip title="Notifications">
        <IconButton
          color="inherit"
          onClick={handleClick}
          sx={{ mr: 1 }}
          aria-controls={open ? 'notification-menu' : undefined}
          aria-haspopup="true"
          aria-expanded={open ? 'true' : undefined}
        >
          <Badge badgeContent={stats.unread} color="error">
            <Notifications />
          </Badge>
        </IconButton>
      </Tooltip>

      <Menu
        id="notification-menu"
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        onClick={handleClose}
        PaperProps={{
          elevation: 0,
          sx: {
            overflow: 'visible',
            filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.32))',
            mt: 1.5,
            width: 400,
            maxHeight: 500,
            '& .MuiAvatar-root': {
              width: 32,
              height: 32,
              ml: -0.5,
              mr: 1,
            },
            '&:before': {
              content: '""',
              display: 'block',
              position: 'absolute',
              top: 0,
              right: 14,
              width: 10,
              height: 10,
              bgcolor: 'background.paper',
              transform: 'translateY(-50%) rotate(45deg)',
              zIndex: 0,
            },
          },
        }}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        {/* Header */}
        <Box sx={{ px: 2, py: 1, borderBottom: 1, borderColor: 'divider' }}>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Typography variant="h6">
              Notifications
            </Typography>
            {stats.unread > 0 && (
              <Button
                size="small"
                onClick={handleMarkAllAsRead}
                disabled={isMarkingAllAsRead}
                startIcon={isMarkingAllAsRead ? <CircularProgress size={16} /> : <MarkEmailRead />}
              >
                Mark all read
              </Button>
            )}
          </Box>
          <Typography variant="body2" color="text.secondary">
            {stats.unread} unread of {stats.total} total
          </Typography>
        </Box>

        {/* Loading State */}
        {isLoading && (
          <Box display="flex" justifyContent="center" p={3}>
            <CircularProgress size={24} />
          </Box>
        )}

        {/* Empty State */}
        {!isLoading && inAppNotifications.length === 0 && (
          <Box textAlign="center" py={4} px={2}>
            <Notifications sx={{ fontSize: 48, color: 'text.secondary', mb: 1 }} />
            <Typography variant="body2" color="text.secondary">
              No notifications yet
            </Typography>
          </Box>
        )}

        {/* Notifications List */}
        {!isLoading && inAppNotifications.length > 0 && (
          <List sx={{ py: 0, maxHeight: 350, overflow: 'auto' }}>
            {inAppNotifications.map((notification, index) => (
              <React.Fragment key={notification.id}>
                <ListItem
                  alignItems="flex-start"
                  sx={{
                    py: 1.5,
                    px: 2,
                    backgroundColor: notification.is_read ? 'transparent' : 'action.hover',
                    '&:hover': {
                      backgroundColor: 'action.selected',
                    },
                  }}
                >
                  <ListItemIcon sx={{ minWidth: 40 }}>
                    {getChannelIcon(notification.channel)}
                  </ListItemIcon>
                  <ListItemText
                    primary={
                      <Box display="flex" justifyContent="space-between" alignItems="flex-start">
                        <Typography
                          variant="body2"
                          fontWeight={notification.is_read ? 'normal' : 'bold'}
                          sx={{ flex: 1, mr: 1 }}
                        >
                          {notification.subject}
                        </Typography>
                        <Box display="flex" alignItems="center" gap={0.5}>
                          {getStatusIcon(notification.status)}
                          <Chip
                            label={notification.status}
                            size="small"
                            color={getStatusColor(notification.status) as any}
                            variant="outlined"
                            sx={{ fontSize: '0.7rem', height: 20 }}
                          />
                        </Box>
                      </Box>
                    }
                    secondary={
                      <Box>
                        <Typography
                          variant="body2"
                          color="text.secondary"
                          sx={{
                            display: '-webkit-box',
                            WebkitLineClamp: 2,
                            WebkitBoxOrient: 'vertical',
                            overflow: 'hidden',
                            mb: 0.5,
                          }}
                        >
                          {notification.message}
                        </Typography>
                        <Box display="flex" justifyContent="space-between" alignItems="center">
                          <Typography variant="caption" color="text.secondary">
                            {formatDistanceToNow(new Date(notification.created_at), { addSuffix: true })}
                          </Typography>
                          {notification.alert_name && (
                            <Chip
                              label={notification.alert_name}
                              size="small"
                              variant="outlined"
                              sx={{ fontSize: '0.65rem', height: 18 }}
                            />
                          )}
                        </Box>
                      </Box>
                    }
                  />
                  {!notification.is_read && (
                    <Tooltip title="Mark as read">
                      <IconButton
                        size="small"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleMarkAsRead(notification.id);
                        }}
                        disabled={isMarkingAsRead}
                        sx={{ ml: 1 }}
                      >
                        <Clear fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  )}
                </ListItem>
                {index < inAppNotifications.length - 1 && <Divider />}
              </React.Fragment>
            ))}
          </List>
        )}

        {/* Footer */}
        {!isLoading && inAppNotifications.length > 0 && (
          <>
            <Divider />
            <Box sx={{ p: 1 }}>
              <Button
                fullWidth
                size="small"
                onClick={() => {
                  handleClose();
                  // Navigate to notifications page if it exists
                  // navigate('/notifications');
                }}
              >
                View All Notifications
              </Button>
            </Box>
          </>
        )}
      </Menu>
    </>
  );
};

export default NotificationDropdown;

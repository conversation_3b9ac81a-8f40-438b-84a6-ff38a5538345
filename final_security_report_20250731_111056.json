{"timestamp": "2025-07-31T11:10:56.466055", "security_score": 96.0, "summary": {"total": 25, "passed": 24, "failed": 1, "warnings": 0}, "results": [{"timestamp": "2025-07-31T11:10:44.314990", "component": "Container - Backend API", "status": "PASS", "details": "trustvault-django-simple is running"}, {"timestamp": "2025-07-31T11:10:44.314990", "component": "Container - Frontend", "status": "PASS", "details": "trustvault-react-simple is running"}, {"timestamp": "2025-07-31T11:10:44.314990", "component": "Container - Database", "status": "PASS", "details": "trustvault-postgres-simple is running"}, {"timestamp": "2025-07-31T11:10:44.315529", "component": "Container - <PERSON><PERSON>", "status": "PASS", "details": "trustvault-redis-simple is running"}, {"timestamp": "2025-07-31T11:10:44.315529", "component": "Container - Monitoring", "status": "PASS", "details": "trustvault-prometheus-simple is running"}, {"timestamp": "2025-07-31T11:10:44.315529", "component": "Container - Dashboards", "status": "PASS", "details": "trustvault-grafana-simple is running"}, {"timestamp": "2025-07-31T11:10:44.316047", "component": "Container - SSL Proxy", "status": "PASS", "details": "trustvault-nginx-ssl is running"}, {"timestamp": "2025-07-31T11:10:44.316592", "component": "SSL - Server Certificate", "status": "PASS", "details": "File exists (2069 bytes)"}, {"timestamp": "2025-07-31T11:10:44.317158", "component": "SSL - Server Private Key", "status": "PASS", "details": "File exists (3268 bytes)"}, {"timestamp": "2025-07-31T11:10:44.317730", "component": "SSL - CA Certificate", "status": "PASS", "details": "File exists (2061 bytes)"}, {"timestamp": "2025-07-31T11:10:44.318885", "component": "SSL - CA Private Key", "status": "PASS", "details": "File exists (3272 bytes)"}, {"timestamp": "2025-07-31T11:10:44.455502", "component": "Endpoint - Main HTTPS Proxy", "status": "PASS", "details": "HTTP 200"}, {"timestamp": "2025-07-31T11:10:47.284032", "component": "Endpoint - HTTP Redirect", "status": "FAIL", "details": "Connection refused"}, {"timestamp": "2025-07-31T11:10:51.290186", "component": "Endpoint - Backend Health", "status": "PASS", "details": "HTTP 200"}, {"timestamp": "2025-07-31T11:10:51.308739", "component": "Endpoint - Frontend", "status": "PASS", "details": "HTTP 200"}, {"timestamp": "2025-07-31T11:10:51.329816", "component": "Endpoint - Prometheus", "status": "PASS", "details": "HTTP 200"}, {"timestamp": "2025-07-31T11:10:51.383857", "component": "Endpoint - <PERSON><PERSON>", "status": "PASS", "details": "HTTP 200"}, {"timestamp": "2025-07-31T11:10:55.258364", "component": "Security Header - X-Content-Type-Options", "status": "PASS", "details": "Value: nosniff"}, {"timestamp": "2025-07-31T11:10:55.260018", "component": "Security Header - X-Frame-Options", "status": "PASS", "details": "Value: DENY"}, {"timestamp": "2025-07-31T11:10:55.260639", "component": "Security Header - Content-Security-Policy", "status": "PASS", "details": "Present"}, {"timestamp": "2025-07-31T11:10:55.260639", "component": "Security Header - X-XSS-Protection", "status": "PASS", "details": "Value: 1; mode=block"}, {"timestamp": "2025-07-31T11:10:56.349920", "component": "Authentication - <PERSON>gin", "status": "PASS", "details": "JWT token received"}, {"timestamp": "2025-07-31T11:10:56.432539", "component": "Authentication - Protected Endpoint", "status": "PASS", "details": "Access granted with valid token"}, {"timestamp": "2025-07-31T11:10:56.449214", "component": "Monitoring - Prometheus", "status": "PASS", "details": "15 metrics available"}, {"timestamp": "2025-07-31T11:10:56.459791", "component": "Monitoring - <PERSON><PERSON>", "status": "PASS", "details": "Health check passed"}]}
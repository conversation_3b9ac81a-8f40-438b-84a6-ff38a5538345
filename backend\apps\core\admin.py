# TrustVault - Core Admin

from django.contrib import admin
from django.utils.html import format_html
from .models import AuditLog, SecurityEvent, SystemConfiguration, DataRetentionPolicy


@admin.register(AuditLog)
class AuditLogAdmin(admin.ModelAdmin):
    """Admin interface for AuditLog."""
    
    list_display = ['timestamp', 'user', 'action', 'resource_type', 'severity', 'ip_address']
    list_filter = ['action', 'resource_type', 'severity', 'timestamp']
    search_fields = ['user__email', 'resource_type', 'ip_address']
    readonly_fields = ['id', 'created_at', 'updated_at', 'timestamp']
    date_hierarchy = 'timestamp'
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('user', 'action', 'resource_type', 'resource_id', 'severity')
        }),
        ('Request Information', {
            'fields': ('ip_address', 'user_agent')
        }),
        ('Details', {
            'fields': ('details',)
        }),
        ('Timestamps', {
            'fields': ('timestamp', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    def has_add_permission(self, request):
        """Disable adding audit logs through admin."""
        return False
    
    def has_change_permission(self, request, obj=None):
        """Disable changing audit logs through admin."""
        return False
    
    def has_delete_permission(self, request, obj=None):
        """Disable deleting audit logs through admin."""
        return False


@admin.register(SecurityEvent)
class SecurityEventAdmin(admin.ModelAdmin):
    """Admin interface for SecurityEvent."""
    
    list_display = ['created_at', 'event_type', 'risk_level', 'source_ip', 'is_resolved', 'resolved_status']
    list_filter = ['event_type', 'risk_level', 'is_resolved', 'created_at']
    search_fields = ['source_ip', 'description']
    readonly_fields = ['id', 'created_at', 'updated_at']
    date_hierarchy = 'created_at'
    
    fieldsets = (
        ('Event Information', {
            'fields': ('event_type', 'risk_level', 'description')
        }),
        ('Source Information', {
            'fields': ('source_ip', 'user_agent')
        }),
        ('Resolution', {
            'fields': ('is_resolved', 'resolved_at', 'resolved_by')
        }),
        ('Details', {
            'fields': ('details',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    def resolved_status(self, obj):
        """Display resolution status with color."""
        if obj.is_resolved:
            return format_html('<span style="color: green;">✓ Resolved</span>')
        else:
            return format_html('<span style="color: red;">✗ Unresolved</span>')
    resolved_status.short_description = 'Status'


@admin.register(SystemConfiguration)
class SystemConfigurationAdmin(admin.ModelAdmin):
    """Admin interface for SystemConfiguration."""
    
    list_display = ['key', 'value_preview', 'is_encrypted', 'updated_at']
    list_filter = ['is_encrypted', 'updated_at']
    search_fields = ['key', 'description']
    readonly_fields = ['id', 'created_at', 'updated_at']
    
    fieldsets = (
        ('Configuration', {
            'fields': ('key', 'value', 'description', 'is_encrypted')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    def value_preview(self, obj):
        """Show preview of value."""
        if obj.is_encrypted:
            return "*** ENCRYPTED ***"
        return obj.value[:50] + "..." if len(obj.value) > 50 else obj.value
    value_preview.short_description = 'Value'


@admin.register(DataRetentionPolicy)
class DataRetentionPolicyAdmin(admin.ModelAdmin):
    """Admin interface for DataRetentionPolicy."""
    
    list_display = ['resource_type', 'retention_days', 'is_gdpr_related', 'updated_at']
    list_filter = ['is_gdpr_related', 'updated_at']
    search_fields = ['resource_type', 'description']
    readonly_fields = ['id', 'created_at', 'updated_at']
    
    fieldsets = (
        ('Policy Information', {
            'fields': ('resource_type', 'retention_days', 'description')
        }),
        ('Compliance', {
            'fields': ('is_gdpr_related',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

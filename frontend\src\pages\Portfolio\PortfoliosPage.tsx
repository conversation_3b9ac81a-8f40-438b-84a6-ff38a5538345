// TrustVault - Portfolios Page

import React from 'react';
import {
  <PERSON>,
  Typography,
  Button,
  Grid,
  Card,
  CardContent,
  CardActions,
  Chip,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  Add,
  TrendingUp,
  TrendingDown,
  MoreVert,
  AccountBalance,
} from '@mui/icons-material';
import { Helmet } from 'react-helmet-async';
import { useQuery } from 'react-query';
import { useNavigate } from 'react-router-dom';

// Services
import apiService from '../../services/api';

const PortfoliosPage: React.FC = () => {
  const navigate = useNavigate();

  const { data: portfolios, isLoading } = useQuery(
    'portfolios',
    apiService.getPortfolios
  );

  const formatCurrency = (value: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(parseFloat(value));
  };

  const getPortfolioTypeColor = (type: string) => {
    switch (type) {
      case 'CONSERVATIVE':
        return 'success';
      case 'MODERATE':
        return 'info';
      case 'AGGRESSIVE':
        return 'warning';
      case 'CUSTOM':
        return 'secondary';
      default:
        return 'default';
    }
  };

  return (
    <>
      <Helmet>
        <title>Portfolios - TrustVault</title>
        <meta name="description" content="Manage your investment portfolios" />
      </Helmet>

      <Box>
        {/* Header */}
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={4}>
          <Box>
            <Typography variant="h4" component="h1" gutterBottom>
              My Portfolios
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Manage and track your investment portfolios
            </Typography>
          </Box>
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={() => navigate('/portfolios/create')}
          >
            Create Portfolio
          </Button>
        </Box>

        {/* Portfolio Grid */}
        {isLoading ? (
          <Typography>Loading portfolios...</Typography>
        ) : Array.isArray(portfolios) && portfolios.length > 0 ? (
          <Grid container spacing={3}>
            {portfolios.map((portfolio) => (
              <Grid item xs={12} sm={6} md={4} key={portfolio.id}>
                <Card
                  sx={{
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    cursor: 'pointer',
                    '&:hover': {
                      boxShadow: 4,
                    },
                  }}
                  onClick={() => navigate(`/portfolios/${portfolio.id}`)}
                >
                  <CardContent sx={{ flexGrow: 1 }}>
                    <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
                      <Typography variant="h6" component="h2" noWrap>
                        {portfolio.name}
                      </Typography>
                      <Tooltip title="More options">
                        <IconButton
                          size="small"
                          onClick={(e) => {
                            e.stopPropagation();
                            // Handle menu open
                          }}
                        >
                          <MoreVert />
                        </IconButton>
                      </Tooltip>
                    </Box>

                    <Typography variant="body2" color="text.secondary" mb={2} noWrap>
                      {portfolio.description || 'No description'}
                    </Typography>

                    <Box mb={2}>
                      <Chip
                        label={portfolio.portfolio_type}
                        color={getPortfolioTypeColor(portfolio.portfolio_type)}
                        size="small"
                      />
                    </Box>

                    <Box mb={2}>
                      <Typography variant="h4" component="div" color="primary">
                        {formatCurrency(portfolio.total_value)}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {portfolio.holdings_count || 0} holdings
                      </Typography>
                    </Box>

                    {portfolio.performance_24h && (
                      <Box display="flex" alignItems="center" gap={1}>
                        {parseFloat(portfolio.performance_24h.change) >= 0 ? (
                          <TrendingUp color="success" fontSize="small" />
                        ) : (
                          <TrendingDown color="error" fontSize="small" />
                        )}
                        <Typography
                          variant="body2"
                          color={
                            parseFloat(portfolio.performance_24h.change) >= 0
                              ? 'success.main'
                              : 'error.main'
                          }
                        >
                          {portfolio.performance_24h.change_percentage}%
                        </Typography>
                      </Box>
                    )}
                  </CardContent>

                  <CardActions>
                    <Button
                      size="small"
                      onClick={(e) => {
                        e.stopPropagation();
                        navigate(`/portfolios/${portfolio.id}`);
                      }}
                    >
                      View Details
                    </Button>
                    <Button
                      size="small"
                      onClick={(e) => {
                        e.stopPropagation();
                        navigate(`/portfolios/${portfolio.id}/edit`);
                      }}
                    >
                      Edit
                    </Button>
                  </CardActions>
                </Card>
              </Grid>
            ))}
          </Grid>
        ) : (
          <Box textAlign="center" py={8}>
            <AccountBalance sx={{ fontSize: 80, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h5" color="text.secondary" gutterBottom>
              No Portfolios Yet
            </Typography>
            <Typography variant="body1" color="text.secondary" mb={4}>
              Create your first portfolio to start tracking your investments
            </Typography>
            <Button
              variant="contained"
              startIcon={<Add />}
              onClick={() => navigate('/portfolios/create')}
            >
              Create Your First Portfolio
            </Button>
          </Box>
        )}
      </Box>
    </>
  );
};

export default PortfoliosPage;

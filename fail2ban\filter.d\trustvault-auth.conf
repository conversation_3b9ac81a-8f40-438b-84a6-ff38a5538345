# TrustVault Authentication Filter

[Definition]

# Failregex patterns for authentication failures
failregex = ^<HOST> - .* "POST /api/v1/auth/login HTTP/.*" 401 .*$
            ^<HOST> - .* "POST /api/v1/auth/register HTTP/.*" 400 .*$
            ^<HOST> - .* "POST /api/v1/auth/token/refresh HTTP/.*" 401 .*$
            ^<HOST> - .* "POST /api/v1/auth/password/reset HTTP/.*" 400 .*$
            ^<HOST> - .* "POST /admin/login/ HTTP/.*" 401 .*$

# Ignore successful authentications
ignoreregex = ^<HOST> - .* "POST /api/v1/auth/login HTTP/.*" 200 .*$
              ^<HOST> - .* "POST /api/v1/auth/register HTTP/.*" 201 .*$

# Date pattern
datepattern = ^%%d/%%b/%%Y:%%H:%%M:%%S %%z

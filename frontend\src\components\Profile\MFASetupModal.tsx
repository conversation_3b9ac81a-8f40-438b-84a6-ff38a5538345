// TrustVault - MFA Setup Modal

import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Box,
  Typography,
  IconButton,
  Alert,
  CircularProgress,
  Stepper,
  Step,
  StepLabel,
  Paper,
  Divider,
} from '@mui/material';
import {
  Close,
  Security,
  QrCode,
  Key,
  CheckCircle,
} from '@mui/icons-material';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { toast } from 'react-hot-toast';

// Services
import apiService from '../../services/api';

// Store
import { useAuthStore } from '../../store/authStore';

// Types
interface MFASetupFormData {
  token: string;
}

interface MFASetupModalProps {
  open: boolean;
  onClose: () => void;
}

// Validation schema
const mfaSetupSchema = yup.object().shape({
  token: yup
    .string()
    .matches(/^\d{6}$/, 'Token must be 6 digits')
    .required('Verification token is required'),
});

const steps = ['Setup Authenticator', 'Verify Token', 'Complete'];

const MFASetupModal: React.FC<MFASetupModalProps> = ({
  open,
  onClose,
}) => {
  const [activeStep, setActiveStep] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [qrCodeUrl, setQrCodeUrl] = useState<string>('');
  const [secretKey, setSecretKey] = useState<string>('');
  const { loadUser } = useAuthStore();

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<MFASetupFormData>({
    resolver: yupResolver(mfaSetupSchema),
    defaultValues: {
      token: '',
    },
  });

  const handleClose = () => {
    reset();
    setError(null);
    setActiveStep(0);
    setQrCodeUrl('');
    setSecretKey('');
    onClose();
  };

  const initiateMFASetup = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await apiService.setupMFA();
      setQrCodeUrl(response.qr_code_url);
      setSecretKey(response.secret_key);
      setActiveStep(1);
    } catch (error: any) {
      const errorMessage = error.response?.data?.error || 
                          'Failed to setup MFA. Please try again.';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const onSubmit = async (data: MFASetupFormData) => {
    setIsLoading(true);
    setError(null);

    try {
      await apiService.verifyMFA(data.token);
      setActiveStep(2);
      toast.success('2FA enabled successfully!');
      
      // Reload user data to update MFA status
      await loadUser();
      
      // Close modal after a short delay
      setTimeout(() => {
        handleClose();
      }, 2000);
    } catch (error: any) {
      const errorMessage = error.response?.data?.token?.[0] ||
                          error.response?.data?.message ||
                          'Invalid token. Please try again.';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (open && activeStep === 0) {
      initiateMFASetup();
    }
  }, [open]);

  const renderStepContent = () => {
    switch (activeStep) {
      case 0:
        return (
          <Box textAlign="center" py={4}>
            <CircularProgress size={40} />
            <Typography variant="body1" mt={2}>
              Setting up your authenticator...
            </Typography>
          </Box>
        );

      case 1:
        return (
          <Box>
            <Typography variant="body1" mb={3}>
              Scan the QR code below with your authenticator app (Google Authenticator, Authy, etc.), 
              then enter the 6-digit code to complete setup.
            </Typography>

            {qrCodeUrl && (
              <Paper elevation={1} sx={{ p: 2, mb: 3, textAlign: 'center' }}>
                <img 
                  src={qrCodeUrl} 
                  alt="QR Code for 2FA setup" 
                  style={{ maxWidth: '200px', height: 'auto' }}
                />
              </Paper>
            )}

            <Divider sx={{ my: 2 }} />

            <Typography variant="body2" color="text.secondary" mb={1}>
              Can't scan the QR code? Enter this secret key manually:
            </Typography>
            <Paper elevation={0} sx={{ p: 2, bgcolor: 'grey.100', mb: 3 }}>
              <Typography variant="body2" fontFamily="monospace" sx={{ wordBreak: 'break-all' }}>
                {secretKey}
              </Typography>
            </Paper>

            <TextField
              {...register('token')}
              label="Verification Code"
              placeholder="000000"
              fullWidth
              error={!!errors.token}
              helperText={errors.token?.message}
              inputProps={{
                maxLength: 6,
                style: { textAlign: 'center', fontSize: '1.2rem', letterSpacing: '0.5rem' }
              }}
            />
          </Box>
        );

      case 2:
        return (
          <Box textAlign="center" py={4}>
            <CheckCircle color="success" sx={{ fontSize: 64, mb: 2 }} />
            <Typography variant="h6" mb={1}>
              2FA Enabled Successfully!
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Your account is now protected with two-factor authentication.
            </Typography>
          </Box>
        );

      default:
        return null;
    }
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: { borderRadius: 2 }
      }}
    >
      <DialogTitle>
        <Box display="flex" alignItems="center" justifyContent="space-between">
          <Box display="flex" alignItems="center" gap={1}>
            <Security color="primary" />
            <Typography variant="h6">Enable Two-Factor Authentication</Typography>
          </Box>
          <IconButton onClick={handleClose} size="small">
            <Close />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent>
        <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
          {steps.map((label) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {renderStepContent()}
      </DialogContent>

      {activeStep === 1 && (
        <form onSubmit={handleSubmit(onSubmit)}>
          <DialogActions sx={{ p: 3, pt: 0 }}>
            <Button
              onClick={handleClose}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="contained"
              disabled={isLoading}
              startIcon={isLoading ? <CircularProgress size={16} /> : <Security />}
            >
              {isLoading ? 'Verifying...' : 'Verify & Enable'}
            </Button>
          </DialogActions>
        </form>
      )}

      {activeStep === 2 && (
        <DialogActions sx={{ p: 3, pt: 0 }}>
          <Button
            onClick={handleClose}
            variant="contained"
            fullWidth
          >
            Done
          </Button>
        </DialogActions>
      )}
    </Dialog>
  );
};

export default MFASetupModal;

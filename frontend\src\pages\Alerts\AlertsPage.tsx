// TrustVault - Alerts Page

import React, { useState } from 'react';
import {
  <PERSON>,
  Typography,
  Grid,
  Card,
  CardContent,
  Button,
  Chip,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
} from '@mui/material';
import {
  Add,
  Edit,
  Delete,
  NotificationsActive,
  TrendingUp,
  TrendingDown,
  Timeline,
  AccountBalance,
} from '@mui/icons-material';
import { Helmet } from 'react-helmet-async';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { toast } from 'react-hot-toast';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';

// Services
import apiService from '../../services/api';

// Components
import Alert<PERSON>reationGuide from '../../components/Alerts/AlertCreationGuide';

// Types
interface PriceAlertData {
  id: string;
  name: string;
  alert_type: string;
  threshold_value: number;
  comparison_operator: string;
  status: string;
  asset_symbol?: string;
  portfolio_name?: string;
  notification_channels: string[];
  expires_at?: string;
  triggered_at?: string;
  trigger_count: number;
  max_triggers: number;
  created_at: string;
}

interface AlertFormData {
  name: string;
  alert_type: string;
  asset?: string;
  portfolio?: string;
  threshold_value: number;
  comparison_operator: string;
  notification_channels: string[];
  expires_at?: string;
  max_triggers: number;
  cooldown_minutes: number;
  description?: string;
}

// Validation schema
const alertSchema = yup.object().shape({
  name: yup.string().required('Alert name is required'),
  alert_type: yup.string().required('Alert type is required'),
  asset: yup.string().optional(),
  portfolio: yup.string().optional(),
  threshold_value: yup.number().required('Threshold value is required').min(0, 'Must be positive'),
  comparison_operator: yup.string().required('Comparison operator is required'),
  notification_channels: yup.array().min(1, 'At least one notification channel is required'),
  expires_at: yup.string().optional(),
  max_triggers: yup.number().min(1, 'Must be at least 1').max(100, 'Cannot exceed 100'),
  cooldown_minutes: yup.number().min(0, 'Must be non-negative'),
  description: yup.string().optional(),
});

const AlertsPage: React.FC = () => {
  const queryClient = useQueryClient();
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [editingAlert, setEditingAlert] = useState<PriceAlertData | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [alertToDelete, setAlertToDelete] = useState<PriceAlertData | null>(null);

  // Fetch alerts
  const { data: alerts = [], isLoading: alertsLoading } = useQuery(
    'alerts',
    apiService.getAlerts,
    {
      onError: (error: any) => {
        toast.error('Failed to load alerts');
      },
    }
  );

  // Fetch alert stats
  const { data: alertStats } = useQuery(
    'alert-stats',
    apiService.getAlertStats,
    {
      onError: () => {
        // Silently fail for stats
      },
    }
  );

  // Fetch alert choices
  const { data: alertChoices } = useQuery(
    'alert-choices',
    apiService.getAlertChoices,
    {
      onError: () => {
        // Silently fail for choices
      },
    }
  );

  // Fetch portfolios for dropdown
  const { data: portfolios = [] } = useQuery(
    'portfolios',
    apiService.getPortfolios,
    {
      onError: () => {
        // Silently fail for portfolios
      },
    }
  );

  // Mock assets for now (in real app, this would come from API)
  const mockAssets = [
    { id: '2502b584-6811-49f5-9dc7-03afe790fd22', symbol: 'AAPL', name: 'Apple Inc.' },
    { id: 'b1234567-1234-4567-8901-123456789012', symbol: 'GOOGL', name: 'Alphabet Inc.' },
    { id: 'c1234567-1234-4567-8901-123456789012', symbol: 'MSFT', name: 'Microsoft Corporation' },
    { id: 'd1234567-1234-4567-8901-123456789012', symbol: 'TSLA', name: 'Tesla Inc.' },
    { id: 'e1234567-1234-4567-8901-123456789012', symbol: 'AMZN', name: 'Amazon.com Inc.' },
  ];

  // Create alert mutation
  const createAlertMutation = useMutation(
    (data: AlertFormData) => apiService.createAlert(data),
    {
      onSuccess: () => {
        toast.success('Alert created successfully');
        queryClient.invalidateQueries('alerts');
        queryClient.invalidateQueries('alert-stats');
        setCreateDialogOpen(false);
        reset();
      },
      onError: (error: any) => {
        const message = error.response?.data?.message || 'Failed to create alert';
        toast.error(message);
      },
    }
  );

  // Update alert mutation
  const updateAlertMutation = useMutation(
    ({ id, data }: { id: string; data: AlertFormData }) => apiService.updateAlert(id, data),
    {
      onSuccess: () => {
        toast.success('Alert updated successfully');
        queryClient.invalidateQueries('alerts');
        setEditingAlert(null);
        reset();
      },
      onError: (error: any) => {
        const message = error.response?.data?.message || 'Failed to update alert';
        toast.error(message);
      },
    }
  );

  // Delete alert mutation
  const deleteAlertMutation = useMutation(
    (id: string) => apiService.deleteAlert(id),
    {
      onSuccess: () => {
        toast.success('Alert deleted successfully');
        queryClient.invalidateQueries('alerts');
        queryClient.invalidateQueries('alert-stats');
        setDeleteDialogOpen(false);
        setAlertToDelete(null);
      },
      onError: (error: any) => {
        const message = error.response?.data?.message || 'Failed to delete alert';
        toast.error(message);
      },
    }
  );

  // Form handling
  const {
    control,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
    watch,
  } = useForm<AlertFormData>({
    resolver: yupResolver(alertSchema) as any,
    defaultValues: {
      name: '',
      alert_type: '',
      threshold_value: 0,
      comparison_operator: 'GT',
      notification_channels: ['EMAIL'],
      max_triggers: 1,
      cooldown_minutes: 60,
      description: '',
    },
  });

  const handleCreateAlert = () => {
    setEditingAlert(null);
    reset();
    setCreateDialogOpen(true);
  };

  const handleEditAlert = (alert: PriceAlertData) => {
    setEditingAlert(alert);
    setValue('name', alert.name);
    setValue('alert_type', alert.alert_type);
    setValue('threshold_value', alert.threshold_value);
    setValue('comparison_operator', alert.comparison_operator);
    setValue('notification_channels', alert.notification_channels);
    setValue('max_triggers', alert.max_triggers);
    setCreateDialogOpen(true);
  };

  const handleDeleteAlert = (alert: PriceAlertData) => {
    setAlertToDelete(alert);
    setDeleteDialogOpen(true);
  };

  const onSubmit = (data: AlertFormData) => {
    // Validate that either asset or portfolio is selected (but not both)
    if (!data.asset && !data.portfolio) {
      toast.error('Please select either an asset or a portfolio for the alert');
      return;
    }

    if (data.asset && data.portfolio) {
      toast.error('Please select either an asset OR a portfolio, not both');
      return;
    }

    // Validate alert type compatibility
    const portfolioTypes = ['PORTFOLIO_VALUE', 'PORTFOLIO_CHANGE'];
    const assetTypes = ['PRICE_ABOVE', 'PRICE_BELOW', 'PRICE_CHANGE', 'VOLUME_SPIKE'];

    if (portfolioTypes.includes(data.alert_type) && !data.portfolio) {
      toast.error(`Alert type "${data.alert_type}" requires a portfolio to be selected`);
      return;
    }

    if (assetTypes.includes(data.alert_type) && !data.asset) {
      toast.error(`Alert type "${data.alert_type}" requires an asset to be selected`);
      return;
    }

    // Clean up data - ensure only one target is set
    const cleanData = {
      ...data,
      asset: data.asset || undefined,
      portfolio: data.portfolio || undefined,
    };

    if (editingAlert) {
      updateAlertMutation.mutate({ id: editingAlert.id, data: cleanData });
    } else {
      createAlertMutation.mutate(cleanData);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return 'success';
      case 'TRIGGERED':
        return 'warning';
      case 'PAUSED':
        return 'default';
      case 'EXPIRED':
        return 'error';
      case 'CANCELLED':
        return 'error';
      default:
        return 'default';
    }
  };

  const getAlertTypeIcon = (alertType: string) => {
    switch (alertType) {
      case 'PRICE_ABOVE':
        return <TrendingUp />;
      case 'PRICE_BELOW':
        return <TrendingDown />;
      case 'PRICE_CHANGE':
        return <Timeline />;
      case 'PORTFOLIO_VALUE':
      case 'PORTFOLIO_CHANGE':
        return <AccountBalance />;
      default:
        return <NotificationsActive />;
    }
  };

  return (
    <>
      <Helmet>
        <title>Alerts - TrustVault</title>
      </Helmet>

      <Box sx={{ p: 3 }}>
        {/* Header */}
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
          <Typography variant="h4" component="h1">
            Price Alerts
          </Typography>
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={handleCreateAlert}
          >
            Create Alert
          </Button>
        </Box>

        {/* Stats Cards */}
        {alertStats && (
          <Grid container spacing={3} mb={3}>
            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Typography color="textSecondary" gutterBottom>
                    Total Alerts
                  </Typography>
                  <Typography variant="h4">
                    {alertStats.total_alerts}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Typography color="textSecondary" gutterBottom>
                    Active Alerts
                  </Typography>
                  <Typography variant="h4" color="success.main">
                    {alertStats.active_alerts}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Typography color="textSecondary" gutterBottom>
                    Triggered
                  </Typography>
                  <Typography variant="h4" color="warning.main">
                    {alertStats.triggered_alerts}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Typography color="textSecondary" gutterBottom>
                    Recent Triggers
                  </Typography>
                  <Typography variant="h4">
                    {alertStats.recent_triggers}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        )}

        {/* Alerts Table */}
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Your Alerts
            </Typography>
            
            {alertsLoading ? (
              <Box display="flex" justifyContent="center" p={3}>
                <CircularProgress />
              </Box>
            ) : alerts.length === 0 ? (
              <Box textAlign="center" py={4}>
                <NotificationsActive sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                <Typography variant="h6" color="text.secondary" gutterBottom>
                  No alerts created yet
                </Typography>
                <Typography variant="body2" color="text.secondary" mb={3}>
                  Create your first alert to get notified about price changes
                </Typography>
                <Button
                  variant="contained"
                  startIcon={<Add />}
                  onClick={handleCreateAlert}
                >
                  Create Your First Alert
                </Button>
              </Box>
            ) : (
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Alert</TableCell>
                      <TableCell>Type</TableCell>
                      <TableCell>Target</TableCell>
                      <TableCell>Threshold</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Triggers</TableCell>
                      <TableCell>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {alerts.map((alert: PriceAlertData) => (
                      <TableRow key={alert.id}>
                        <TableCell>
                          <Box display="flex" alignItems="center" gap={1}>
                            {getAlertTypeIcon(alert.alert_type)}
                            <Box>
                              <Typography variant="body2" fontWeight="bold">
                                {alert.name}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                Created {new Date(alert.created_at).toLocaleDateString()}
                              </Typography>
                            </Box>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {alert.alert_type.replace('_', ' ')}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {alert.asset_symbol || alert.portfolio_name || 'N/A'}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            ${alert.threshold_value.toLocaleString()}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={alert.status}
                            color={getStatusColor(alert.status) as any}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {alert.trigger_count}/{alert.max_triggers}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <IconButton
                            size="small"
                            onClick={() => handleEditAlert(alert)}
                          >
                            <Edit />
                          </IconButton>
                          <IconButton
                            size="small"
                            color="error"
                            onClick={() => handleDeleteAlert(alert)}
                          >
                            <Delete />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            )}
          </CardContent>
        </Card>
      </Box>

      {/* Create/Edit Alert Dialog */}
      <Dialog
        open={createDialogOpen}
        onClose={() => setCreateDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <form onSubmit={handleSubmit(onSubmit)}>
          <DialogTitle>
            {editingAlert ? 'Edit Alert' : 'Create New Alert'}
          </DialogTitle>
          <DialogContent>
            <AlertCreationGuide />
            <Grid container spacing={3} sx={{ mt: 1 }}>
              <Grid item xs={12}>
                <Controller
                  name="name"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Alert Name"
                      fullWidth
                      error={!!errors.name}
                      helperText={errors.name?.message}
                    />
                  )}
                />
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <Controller
                  name="alert_type"
                  control={control}
                  render={({ field }) => {
                    const selectedType = field.value;
                    const getAlertTypeHelp = (type: string) => {
                      const portfolioTypes = ['PORTFOLIO_VALUE', 'PORTFOLIO_CHANGE'];
                      const assetTypes = ['PRICE_ABOVE', 'PRICE_BELOW', 'PRICE_CHANGE', 'VOLUME_SPIKE'];

                      if (portfolioTypes.includes(type)) {
                        return '📊 Requires a portfolio selection';
                      } else if (assetTypes.includes(type)) {
                        return '📈 Requires an asset selection';
                      }
                      return '';
                    };

                    return (
                      <FormControl fullWidth error={!!errors.alert_type}>
                        <InputLabel>Alert Type</InputLabel>
                        <Select {...field} label="Alert Type">
                          {alertChoices?.alert_types?.map((type: any) => (
                            <MenuItem key={type.value} value={type.value}>
                              {type.label}
                            </MenuItem>
                          ))}
                        </Select>
                        {selectedType && (
                          <Typography variant="caption" color="primary" sx={{ mt: 0.5, display: 'block' }}>
                            {getAlertTypeHelp(selectedType)}
                          </Typography>
                        )}
                        {errors.alert_type && (
                          <Typography variant="caption" color="error" sx={{ mt: 0.5, display: 'block' }}>
                            {errors.alert_type.message}
                          </Typography>
                        )}
                      </FormControl>
                    );
                  }}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <Controller
                  name="comparison_operator"
                  control={control}
                  render={({ field }) => (
                    <FormControl fullWidth>
                      <InputLabel>Condition</InputLabel>
                      <Select {...field} label="Condition">
                        <MenuItem value="GT">Greater Than</MenuItem>
                        <MenuItem value="LT">Less Than</MenuItem>
                        <MenuItem value="GTE">Greater Than or Equal</MenuItem>
                        <MenuItem value="LTE">Less Than or Equal</MenuItem>
                      </Select>
                    </FormControl>
                  )}
                />
              </Grid>

              <Grid item xs={12}>
                <Typography variant="subtitle2" gutterBottom>
                  Target Selection (Choose one)
                </Typography>
              </Grid>

              <Grid item xs={12} sm={6}>
                <Controller
                  name="asset"
                  control={control}
                  render={({ field }) => {
                    const portfolioSelected = watch('portfolio');
                    return (
                      <FormControl fullWidth disabled={!!portfolioSelected}>
                        <InputLabel>Asset</InputLabel>
                        <Select
                          {...field}
                          label="Asset"
                          onChange={(e) => {
                            field.onChange(e);
                            if (e.target.value) {
                              setValue('portfolio', ''); // Clear portfolio when asset is selected
                            }
                          }}
                        >
                          <MenuItem value="">Select an asset...</MenuItem>
                          {mockAssets.map((asset) => (
                            <MenuItem key={asset.id} value={asset.id}>
                              {asset.symbol} - {asset.name}
                            </MenuItem>
                          ))}
                        </Select>
                        {portfolioSelected && (
                          <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5 }}>
                            Disabled because a portfolio is selected
                          </Typography>
                        )}
                      </FormControl>
                    );
                  }}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <Controller
                  name="portfolio"
                  control={control}
                  render={({ field }) => {
                    const assetSelected = watch('asset');
                    return (
                      <FormControl fullWidth disabled={!!assetSelected}>
                        <InputLabel>Portfolio</InputLabel>
                        <Select
                          {...field}
                          label="Portfolio"
                          onChange={(e) => {
                            field.onChange(e);
                            if (e.target.value) {
                              setValue('asset', ''); // Clear asset when portfolio is selected
                            }
                          }}
                        >
                          <MenuItem value="">Select a portfolio...</MenuItem>
                          {portfolios.map((portfolio: any) => (
                            <MenuItem key={portfolio.id} value={portfolio.id}>
                              {portfolio.name}
                            </MenuItem>
                          ))}
                        </Select>
                        {assetSelected && (
                          <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5 }}>
                            Disabled because an asset is selected
                          </Typography>
                        )}
                      </FormControl>
                    );
                  }}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <Controller
                  name="threshold_value"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Threshold Value"
                      type="number"
                      fullWidth
                      error={!!errors.threshold_value}
                      helperText={errors.threshold_value?.message}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <Controller
                  name="max_triggers"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Max Triggers"
                      type="number"
                      fullWidth
                      error={!!errors.max_triggers}
                      helperText={errors.max_triggers?.message}
                    />
                  )}
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setCreateDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              type="submit"
              variant="contained"
              disabled={createAlertMutation.isLoading || updateAlertMutation.isLoading}
            >
              {createAlertMutation.isLoading || updateAlertMutation.isLoading ? (
                <CircularProgress size={20} />
              ) : editingAlert ? (
                'Update Alert'
              ) : (
                'Create Alert'
              )}
            </Button>
          </DialogActions>
        </form>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
      >
        <DialogTitle>Delete Alert</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete the alert "{alertToDelete?.name}"?
            This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>
            Cancel
          </Button>
          <Button
            color="error"
            variant="contained"
            onClick={() => alertToDelete && deleteAlertMutation.mutate(alertToDelete.id)}
            disabled={deleteAlertMutation.isLoading}
          >
            {deleteAlertMutation.isLoading ? <CircularProgress size={20} /> : 'Delete'}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default AlertsPage;

FROM nginx:alpine

# Install security modules and tools
RUN apk add --no-cache \
    openssl \
    curl \
    certbot \
    certbot-nginx \
    fail2ban \
    logrotate

# Create directories for SSL certificates and logs
RUN mkdir -p /etc/nginx/ssl /var/log/nginx /etc/nginx/modsecurity

# Copy custom nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf
COPY conf.d/ /etc/nginx/conf.d/

# Copy security headers configuration
COPY security-headers.conf /etc/nginx/security-headers.conf

# Set proper permissions
RUN chmod 644 /etc/nginx/nginx.conf \
    && chmod -R 644 /etc/nginx/conf.d/ \
    && chmod 600 /etc/nginx/ssl/* || true

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost/health || exit 1

EXPOSE 80 443

CMD ["nginx", "-g", "daemon off;"]

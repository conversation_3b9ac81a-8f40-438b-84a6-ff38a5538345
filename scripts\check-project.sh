#!/bin/bash

# TrustVault - Project Verification Script
# ============================================================================

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Counters
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0

# Check function
check_item() {
    local description="$1"
    local condition="$2"
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    if eval "$condition"; then
        echo -e "${GREEN}✅${NC} $description"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
        return 0
    else
        echo -e "${RED}❌${NC} $description"
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
        return 1
    fi
}

echo -e "${BLUE}🔍 TrustVault - Vérification du Projet${NC}"
echo "========================================"
echo ""

# 1. Structure des fichiers
echo -e "${YELLOW}📁 Structure des Fichiers${NC}"
echo "-------------------------"

check_item "README.md principal" "[[ -f '$PROJECT_ROOT/README.md' ]]"
check_item "PRESENTATION-PFE.md" "[[ -f '$PROJECT_ROOT/PRESENTATION-PFE.md' ]]"
check_item "docker-compose.yml" "[[ -f '$PROJECT_ROOT/docker-compose.yml' ]]"
check_item "Fichier d'environnement exemple" "[[ -f '$PROJECT_ROOT/.env.example' ]]"

echo ""

# 2. Scripts
echo -e "${YELLOW}🔧 Scripts${NC}"
echo "----------"

check_item "Script de déploiement" "[[ -f '$PROJECT_ROOT/scripts/deploy.sh' ]]"
check_item "Script de génération SSL" "[[ -f '$PROJECT_ROOT/scripts/generate-ssl-certs.sh' ]]"
check_item "Script de sauvegarde" "[[ -f '$PROJECT_ROOT/scripts/backup-restic.sh' ]]"
check_item "Script de permissions" "[[ -f '$PROJECT_ROOT/scripts/setup-permissions.sh' ]]"
check_item "Script de vérification" "[[ -f '$PROJECT_ROOT/scripts/check-project.sh' ]]"

echo ""

# 3. Configuration Nginx
echo -e "${YELLOW}🌐 Configuration Nginx${NC}"
echo "----------------------"

check_item "Dockerfile Nginx" "[[ -f '$PROJECT_ROOT/nginx/Dockerfile' ]]"
check_item "Configuration principale" "[[ -f '$PROJECT_ROOT/nginx/nginx.conf' ]]"
check_item "Virtual host TrustVault" "[[ -f '$PROJECT_ROOT/nginx/conf.d/trustvault.conf' ]]"
check_item "Headers de sécurité" "[[ -f '$PROJECT_ROOT/nginx/security-headers.conf' ]]"
check_item "Dossier SSL" "[[ -d '$PROJECT_ROOT/nginx/ssl' ]]"

echo ""

# 4. Configuration Sécurité
echo -e "${YELLOW}🛡️ Configuration Sécurité${NC}"
echo "---------------------------"

check_item "Configuration Wazuh" "[[ -f '$PROJECT_ROOT/wazuh/config/ossec.conf' ]]"
check_item "Règles personnalisées Wazuh" "[[ -f '$PROJECT_ROOT/wazuh/config/trustvault_rules.xml' ]]"
check_item "Configuration Suricata" "[[ -f '$PROJECT_ROOT/suricata/config/suricata.yaml' ]]"
check_item "Règles personnalisées Suricata" "[[ -f '$PROJECT_ROOT/suricata/rules/trustvault-custom.rules' ]]"
check_item "Configuration Fail2Ban" "[[ -f '$PROJECT_ROOT/fail2ban/jail.local' ]]"

echo ""

# 5. Configuration Monitoring
echo -e "${YELLOW}📊 Configuration Monitoring${NC}"
echo "----------------------------"

check_item "Configuration Prometheus" "[[ -f '$PROJECT_ROOT/prometheus/prometheus.yml' ]]"
check_item "Règles d'alerte sécurité" "[[ -f '$PROJECT_ROOT/prometheus/security_rules.yml' ]]"

echo ""

# 6. Configuration Base de Données
echo -e "${YELLOW}🗄️ Configuration Base de Données${NC}"
echo "--------------------------------"

check_item "Script d'initialisation PostgreSQL" "[[ -f '$PROJECT_ROOT/postgres/init/01-init-security.sql' ]]"
check_item "Configuration Redis" "[[ -f '$PROJECT_ROOT/redis/redis.conf' ]]"
check_item "Configuration Vault" "[[ -f '$PROJECT_ROOT/vault/config/vault.hcl' ]]"

echo ""

# 7. Documentation
echo -e "${YELLOW}📚 Documentation${NC}"
echo "-----------------"

check_item "Architecture de sécurité" "[[ -f '$PROJECT_ROOT/docs/security-architecture.md' ]]"
check_item "Guide d'installation" "[[ -f '$PROJECT_ROOT/docs/installation-guide.md' ]]"
check_item "Tests de pénétration" "[[ -f '$PROJECT_ROOT/docs/penetration-testing.md' ]]"
check_item "Guide de conformité" "[[ -f '$PROJECT_ROOT/docs/compliance-guide.md' ]]"

echo ""

# 8. Permissions des scripts
echo -e "${YELLOW}🔐 Permissions${NC}"
echo "---------------"

check_item "Script deploy.sh exécutable" "[[ -x '$PROJECT_ROOT/scripts/deploy.sh' ]]"
check_item "Script generate-ssl-certs.sh exécutable" "[[ -x '$PROJECT_ROOT/scripts/generate-ssl-certs.sh' ]]"
check_item "Script backup-restic.sh exécutable" "[[ -x '$PROJECT_ROOT/scripts/backup-restic.sh' ]]"
check_item "Script setup-permissions.sh exécutable" "[[ -x '$PROJECT_ROOT/scripts/setup-permissions.sh' ]]"

echo ""

# 9. Vérification Docker
echo -e "${YELLOW}🐳 Environnement Docker${NC}"
echo "------------------------"

if command -v docker &> /dev/null; then
    check_item "Docker installé" "true"
    
    if docker info &> /dev/null; then
        check_item "Docker daemon actif" "true"
    else
        check_item "Docker daemon actif" "false"
    fi
else
    check_item "Docker installé" "false"
fi

if command -v docker-compose &> /dev/null; then
    check_item "Docker Compose installé" "true"
else
    check_item "Docker Compose installé" "false"
fi

echo ""

# 10. Vérification des outils
echo -e "${YELLOW}🛠️ Outils Requis${NC}"
echo "------------------"

check_item "OpenSSL disponible" "command -v openssl &> /dev/null"
check_item "Git disponible" "command -v git &> /dev/null"
check_item "Curl disponible" "command -v curl &> /dev/null"

echo ""

# Résumé final
echo -e "${BLUE}📋 Résumé de la Vérification${NC}"
echo "=============================="
echo ""
echo -e "Total des vérifications : ${BLUE}$TOTAL_CHECKS${NC}"
echo -e "Vérifications réussies  : ${GREEN}$PASSED_CHECKS${NC}"
echo -e "Vérifications échouées  : ${RED}$FAILED_CHECKS${NC}"
echo ""

if [[ $FAILED_CHECKS -eq 0 ]]; then
    echo -e "${GREEN}🎉 Félicitations ! Tous les contrôles sont passés avec succès !${NC}"
    echo ""
    echo -e "${BLUE}🚀 Prêt pour le déploiement :${NC}"
    echo "   1. Configurez les permissions : ./scripts/setup-permissions.sh"
    echo "   2. Lancez le déploiement : ./scripts/deploy.sh deploy"
    echo ""
    echo -e "${BLUE}📖 Documentation disponible :${NC}"
    echo "   - README.md : Vue d'ensemble du projet"
    echo "   - PRESENTATION-PFE.md : Présentation pour votre PFE"
    echo "   - docs/ : Documentation technique complète"
    echo ""
    exit 0
else
    echo -e "${RED}⚠️  Attention ! $FAILED_CHECKS vérification(s) ont échoué.${NC}"
    echo ""
    echo -e "${YELLOW}🔧 Actions recommandées :${NC}"
    
    if [[ ! -f "$PROJECT_ROOT/.env.example" ]]; then
        echo "   - Créer le fichier .env.example"
    fi
    
    if ! command -v docker &> /dev/null; then
        echo "   - Installer Docker"
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        echo "   - Installer Docker Compose"
    fi
    
    if [[ ! -x "$PROJECT_ROOT/scripts/deploy.sh" ]]; then
        echo "   - Exécuter : chmod +x scripts/*.sh"
    fi
    
    echo ""
    echo "Corrigez les problèmes et relancez la vérification."
    exit 1
fi

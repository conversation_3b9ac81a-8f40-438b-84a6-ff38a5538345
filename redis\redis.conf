# TrustVault - Redis Security Configuration
# ============================================================================

# Network and Connection Security
# ============================================================================

# Bind to specific interfaces only (not 0.0.0.0 in production)
bind 127.0.0.1 ::1

# Port configuration
port 6379

# Disable protected mode for container environment
protected-mode no

# Connection limits
tcp-backlog 511
timeout 300
tcp-keepalive 300
maxclients 10000

# ============================================================================
# Authentication and Authorization
# ============================================================================

# Require authentication (password will be set via environment variable)
# requirepass will be set via command line: --requirepass ${REDIS_PASSWORD}

# Disable dangerous commands
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command KEYS ""
rename-command CONFIG "CONFIG_b835c3f8a5d2e7f1"
rename-command SHUTDOWN "SHUTDOWN_a8f7e2d4c9b1f6e3"
rename-command DEBUG ""
rename-command EVAL ""
rename-command SCRIPT ""

# ============================================================================
# Memory and Performance
# ============================================================================

# Memory management
maxmemory 256mb
maxmemory-policy allkeys-lru
maxmemory-samples 5

# Persistence configuration
save 900 1
save 300 10
save 60 10000

# RDB configuration
stop-writes-on-bgsave-error yes
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
dir /data

# AOF configuration
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb
aof-load-truncated yes
aof-use-rdb-preamble yes

# ============================================================================
# Logging and Monitoring
# ============================================================================

# Log level
loglevel notice

# Log file
logfile /var/log/redis/redis-server.log

# Syslog
syslog-enabled yes
syslog-ident redis
syslog-facility local0

# Slow log
slowlog-log-slower-than 10000
slowlog-max-len 128

# ============================================================================
# Security Hardening
# ============================================================================

# Disable Lua debugging
lua-time-limit 5000

# Client output buffer limits
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit replica 256mb 64mb 60
client-output-buffer-limit pubsub 32mb 8mb 60

# Hash configuration for security
hash-max-ziplist-entries 512
hash-max-ziplist-value 64

# List configuration
list-max-ziplist-size -2
list-compress-depth 0

# Set configuration
set-max-intset-entries 512

# Sorted set configuration
zset-max-ziplist-entries 128
zset-max-ziplist-value 64

# HyperLogLog configuration
hll-sparse-max-bytes 3000

# Stream configuration
stream-node-max-bytes 4096
stream-node-max-entries 100

# ============================================================================
# TLS Configuration (for production)
# ============================================================================

# Uncomment and configure for TLS in production
# port 0
# tls-port 6380
# tls-cert-file /etc/redis/tls/redis.crt
# tls-key-file /etc/redis/tls/redis.key
# tls-ca-cert-file /etc/redis/tls/ca.crt
# tls-dh-params-file /etc/redis/tls/redis.dh
# tls-protocols "TLSv1.2 TLSv1.3"
# tls-ciphers "ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-GCM-SHA256"
# tls-ciphersuites "TLS_AES_256_GCM_SHA384:TLS_AES_128_GCM_SHA256"
# tls-prefer-server-ciphers yes
# tls-session-caching no
# tls-session-cache-size 5000
# tls-session-cache-timeout 60

# ============================================================================
# Replication Security (for cluster setup)
# ============================================================================

# Replica configuration
# replicaof <masterip> <masterport>
# masterauth <master-password>
# replica-serve-stale-data yes
# replica-read-only yes
# repl-diskless-sync no
# repl-diskless-sync-delay 5
# repl-ping-replica-period 10
# repl-timeout 60
# repl-disable-tcp-nodelay no
# repl-backlog-size 1mb
# repl-backlog-ttl 3600

# ============================================================================
# Cluster Configuration (for Redis Cluster)
# ============================================================================

# cluster-enabled yes
# cluster-config-file nodes-6379.conf
# cluster-node-timeout 15000
# cluster-replica-validity-factor 10
# cluster-migration-barrier 1
# cluster-require-full-coverage yes

# ============================================================================
# Module Configuration
# ============================================================================

# Load modules if needed
# loadmodule /path/to/module.so

# ============================================================================
# Monitoring and Stats
# ============================================================================

# Latency monitoring
latency-monitor-threshold 100

# Enable keyspace notifications for monitoring
notify-keyspace-events "Ex"

# ============================================================================
# Advanced Security Settings
# ============================================================================

# Disable potentially dangerous features
enable-protected-configs no
enable-debug-command no
enable-module-command no

# ACL configuration (Redis 6+)
# aclfile /etc/redis/users.acl

# Default user configuration
# user default on nopass ~* &* -@all +@read +@write +@connection

# Application user
# user trustvault_app on >${REDIS_APP_PASSWORD} ~trustvault:* +@all -@dangerous

# Read-only user for monitoring
# user monitoring on >${REDIS_MONITOR_PASSWORD} ~* +@read +info +ping

# ============================================================================
# Custom Configuration for TrustVault
# ============================================================================

# Database selection
databases 16

# Key expiration
# Set default TTL for session keys
# This will be handled by the application

# Memory optimization for session storage
hash-max-ziplist-entries 512
hash-max-ziplist-value 64

# Optimize for session data patterns
list-max-ziplist-size -2
set-max-intset-entries 512

# Enable RDB and AOF for data durability
save 900 1
save 300 10
save 60 10000
appendonly yes
appendfsync everysec

# Optimize for high availability
replica-serve-stale-data yes
replica-read-only yes

# Security headers for Redis protocol
# (These are handled at the application level)

# Custom logging for security events
# This will be integrated with the application logging

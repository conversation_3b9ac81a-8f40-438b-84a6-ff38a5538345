// TrustVault - Create Portfolio Page

import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Switch,
  Alert,
  CircularProgress,
} from '@mui/material';
import { ArrowBack, Save } from '@mui/icons-material';
import { Helmet } from 'react-helmet-async';
import { useNavigate } from 'react-router-dom';
import { useMutation, useQueryClient } from 'react-query';
import { toast } from 'react-hot-toast';

// Services
import apiService from '../../services/api';

// Types
interface CreatePortfolioForm {
  name: string;
  description: string;
  portfolio_type: 'CONSERVATIVE' | 'MODERATE' | 'AGGRESSIVE' | 'CUSTOM';
  is_public: boolean;
}

const CreatePortfolioPage: React.FC = () => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();



  const [formData, setFormData] = useState<CreatePortfolioForm>({
    name: '',
    description: '',
    portfolio_type: 'MODERATE',
    is_public: false,
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  // Move hooks before any conditional returns
  const createPortfolioMutation = useMutation(
    (data: CreatePortfolioForm) => apiService.createPortfolio(data),
    {
      onSuccess: (portfolio) => {
        toast.success('Portfolio created successfully!');
        queryClient.invalidateQueries('portfolios');
        navigate(`/portfolios/${portfolio.id}`);
      },
      onError: (error: any) => {
        console.error('Create portfolio error:', error);
        if (error.response?.data) {
          setErrors(error.response.data);
        } else {
          toast.error('Failed to create portfolio. Please try again.');
        }
      },
    }
  );



  const handleInputChange = (field: keyof CreatePortfolioForm) => (
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement> | any
  ) => {
    const value = event.target.type === 'checkbox' ? event.target.checked : event.target.value;
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
    
    // Clear error when user starts typing
    if (field in errors) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  const handleSubmit = (event: React.FormEvent) => {
    event.preventDefault();
    
    // Basic validation
    const newErrors: Record<string, string> = {};
    
    if (!formData.name.trim()) {
      newErrors.name = 'Portfolio name is required';
    }
    
    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }
    
    createPortfolioMutation.mutate(formData);
  };

  const portfolioTypes = [
    { value: 'CONSERVATIVE', label: 'Conservative', description: 'Low risk, stable returns' },
    { value: 'MODERATE', label: 'Moderate', description: 'Balanced risk and return' },
    { value: 'AGGRESSIVE', label: 'Aggressive', description: 'High risk, high potential returns' },
    { value: 'CUSTOM', label: 'Custom', description: 'Custom allocation strategy' },
  ];

  return (
    <>
      <Helmet>
        <title>Create Portfolio - TrustVault</title>
        <meta name="description" content="Create a new investment portfolio" />
      </Helmet>

      <Box sx={{ maxWidth: 800, mx: 'auto', p: 3 }}>
        {/* Header */}
        <Box display="flex" alignItems="center" mb={3}>
          <Button
            startIcon={<ArrowBack />}
            onClick={() => navigate('/portfolios')}
            sx={{ mr: 2 }}
          >
            Back to Portfolios
          </Button>
          <Typography variant="h4" component="h1">
            Create New Portfolio
          </Typography>
        </Box>

        {/* Form */}
        <Paper sx={{ p: 4 }}>
          <form onSubmit={handleSubmit}>
            <Box display="flex" flexDirection="column" gap={3}>
              {/* Portfolio Name */}
              <TextField
                label="Portfolio Name"
                value={formData.name}
                onChange={handleInputChange('name')}
                error={!!errors.name}
                helperText={errors.name}
                required
                fullWidth
                placeholder="e.g., My Investment Portfolio"
              />

              {/* Description */}
              <TextField
                label="Description"
                value={formData.description}
                onChange={handleInputChange('description')}
                error={!!errors.description}
                helperText={errors.description}
                multiline
                rows={3}
                fullWidth
                placeholder="Describe your investment strategy and goals..."
              />

              {/* Portfolio Type */}
              <FormControl fullWidth>
                <InputLabel>Portfolio Type</InputLabel>
                <Select
                  value={formData.portfolio_type}
                  onChange={handleInputChange('portfolio_type')}
                  label="Portfolio Type"
                >
                  {portfolioTypes.map((type) => (
                    <MenuItem key={type.value} value={type.value}>
                      <Box>
                        <Typography variant="body1">{type.label}</Typography>
                        <Typography variant="caption" color="text.secondary">
                          {type.description}
                        </Typography>
                      </Box>
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>

              {/* Public Portfolio */}
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.is_public}
                    onChange={handleInputChange('is_public')}
                  />
                }
                label={
                  <Box>
                    <Typography variant="body1">Make Portfolio Public</Typography>
                    <Typography variant="caption" color="text.secondary">
                      Allow others to view your portfolio performance (holdings remain private)
                    </Typography>
                  </Box>
                }
              />

              {/* Error Display */}
              {Object.keys(errors).length > 0 && (
                <Alert severity="error">
                  Please fix the errors above and try again.
                </Alert>
              )}

              {/* Submit Button */}
              <Box display="flex" justifyContent="flex-end" gap={2} mt={2}>
                <Button
                  variant="outlined"
                  onClick={() => navigate('/portfolios')}
                  disabled={createPortfolioMutation.isLoading}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  variant="contained"
                  startIcon={createPortfolioMutation.isLoading ? <CircularProgress size={20} /> : <Save />}
                  disabled={createPortfolioMutation.isLoading}
                >
                  {createPortfolioMutation.isLoading ? 'Creating...' : 'Create Portfolio'}
                </Button>
              </Box>
            </Box>
          </form>
        </Paper>

        {/* Info Box */}
        <Paper sx={{ p: 3, mt: 3, bgcolor: 'background.default' }}>
          <Typography variant="h6" gutterBottom>
            Getting Started
          </Typography>
          <Typography variant="body2" color="text.secondary">
            After creating your portfolio, you can:
          </Typography>
          <Box component="ul" sx={{ mt: 1, pl: 2 }}>
            <Typography component="li" variant="body2" color="text.secondary">
              Add assets and track your investments
            </Typography>
            <Typography component="li" variant="body2" color="text.secondary">
              Record transactions and monitor performance
            </Typography>
            <Typography component="li" variant="body2" color="text.secondary">
              View detailed analytics and reports
            </Typography>
            <Typography component="li" variant="body2" color="text.secondary">
              Set up alerts and notifications
            </Typography>
          </Box>
        </Paper>
      </Box>
    </>
  );
};

export default CreatePortfolioPage;

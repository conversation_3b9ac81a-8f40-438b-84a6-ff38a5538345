// TrustVault - Security Page

import React from 'react';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Alert,
} from '@mui/material';
import {
  Security,
  Warning,
  CheckCircle,
  Error,
} from '@mui/icons-material';
import { Helmet } from 'react-helmet-async';
import { useQuery } from 'react-query';

// Services
import apiService from '../../services/api';

const SecurityPage: React.FC = () => {
  const { data: securityDashboard, isLoading, error: dashboardError } = useQuery(
    'security-dashboard',
    apiService.getSecurityDashboard,
    {
      retry: 1,
      refetchOnWindowFocus: false,
    }
  );

  const { data: securityEvents, error: eventsError } = useQuery(
    'security-events',
    () => apiService.getSecurityEvents({ page: 1 }),
    {
      retry: 1,
      refetchOnWindowFocus: false,
    }
  );

  const getRiskLevelColor = (level: string) => {
    switch (level) {
      case 'CRITICAL':
        return 'error';
      case 'HIGH':
        return 'warning';
      case 'MEDIUM':
        return 'info';
      case 'LOW':
        return 'success';
      default:
        return 'default';
    }
  };

  const getRiskLevelIcon = (level: string) => {
    switch (level) {
      case 'CRITICAL':
        return <Error color="error" />;
      case 'HIGH':
        return <Warning color="warning" />;
      case 'MEDIUM':
        return <Warning color="info" />;
      case 'LOW':
        return <CheckCircle color="success" />;
      default:
        return <Security />;
    }
  };

  // Gestion des erreurs
  if (dashboardError || eventsError) {
    return (
      <>
        <Helmet>
          <title>Security - TrustVault</title>
          <meta name="description" content="Security monitoring and events" />
        </Helmet>
        <Box>
          <Alert severity="error" sx={{ mb: 2 }}>
            Unable to load security data. Please check your connection and try again.
          </Alert>
        </Box>
      </>
    );
  }

  return (
    <>
      <Helmet>
        <title>Security - TrustVault</title>
        <meta name="description" content="Security monitoring and events" />
      </Helmet>

      <Box>
        {/* Header */}
        <Box mb={4}>
          <Typography variant="h4" component="h1" gutterBottom>
            Security Center
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Monitor security events and system status
          </Typography>
        </Box>

        {/* Security Status Alert */}
        {securityDashboard && securityDashboard.security_events && securityDashboard.security_events.unresolved > 0 && (
          <Alert severity="warning" sx={{ mb: 3 }}>
            You have {securityDashboard.security_events.unresolved} unresolved security events that require attention.
          </Alert>
        )}

        {/* Security Metrics */}
        <Grid container spacing={3} mb={4}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Box>
                    <Typography color="text.secondary" gutterBottom>
                      Total Events
                    </Typography>
                    <Typography variant="h4">
                      {securityDashboard?.security_events?.total_events || 0}
                    </Typography>
                  </Box>
                  <Security color="primary" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Box>
                    <Typography color="text.secondary" gutterBottom>
                      Last 24h
                    </Typography>
                    <Typography variant="h4">
                      {securityDashboard?.security_events?.last_24h || 0}
                    </Typography>
                  </Box>
                  <Warning color="warning" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Box>
                    <Typography color="text.secondary" gutterBottom>
                      Critical Events
                    </Typography>
                    <Typography variant="h4" color="error">
                      {securityDashboard?.security_events?.critical || 0}
                    </Typography>
                  </Box>
                  <Error color="error" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Box>
                    <Typography color="text.secondary" gutterBottom>
                      Unresolved
                    </Typography>
                    <Typography variant="h4" color="warning">
                      {securityDashboard?.security_events?.unresolved || 0}
                    </Typography>
                  </Box>
                  <Warning color="warning" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Login Activity */}
        <Grid container spacing={3} mb={4}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Login Activity (24h)
                </Typography>

                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <Box textAlign="center">
                      <Typography variant="h3" color="success.main">
                        {securityDashboard?.login_attempts?.successful_last_24h || 0}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Successful
                      </Typography>
                    </Box>
                  </Grid>
                  
                  <Grid item xs={6}>
                    <Box textAlign="center">
                      <Typography variant="h3" color="error.main">
                        {securityDashboard?.login_attempts?.failed_last_24h || 0}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Failed
                      </Typography>
                    </Box>
                  </Grid>
                </Grid>

                {(securityDashboard?.login_attempts?.suspicious_last_24h ?? 0) > 0 && (
                  <Alert severity="warning" sx={{ mt: 2 }}>
                    {securityDashboard?.login_attempts?.suspicious_last_24h} suspicious login attempts detected
                  </Alert>
                )}
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Top Threat Sources
                </Typography>
                
                {securityDashboard?.threat_sources && securityDashboard.threat_sources.length > 0 ? (
                  <Box>
                    {securityDashboard.threat_sources.slice(0, 5).map((source, index) => (
                      <Box
                        key={source.source_ip}
                        display="flex"
                        justifyContent="space-between"
                        alignItems="center"
                        py={1}
                        borderBottom={index < 4 ? 1 : 0}
                        borderColor="divider"
                      >
                        <Typography variant="body2" fontFamily="monospace">
                          {source.source_ip}
                        </Typography>
                        <Chip
                          label={`${source.count} events`}
                          size="small"
                          color="warning"
                        />
                      </Box>
                    ))}
                  </Box>
                ) : (
                  <Typography variant="body2" color="text.secondary">
                    No threat sources detected
                  </Typography>
                )}
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Recent Security Events */}
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Recent Security Events
            </Typography>

            {isLoading ? (
              <Typography>Loading security events...</Typography>
            ) : securityEvents && securityEvents.results && securityEvents.results.length > 0 ? (
              <TableContainer component={Paper} variant="outlined">
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Event Type</TableCell>
                      <TableCell>Risk Level</TableCell>
                      <TableCell>Source IP</TableCell>
                      <TableCell>Description</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Date</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {securityEvents.results.slice(0, 10).map((event) => (
                      <TableRow key={event.id}>
                        <TableCell>
                          <Box display="flex" alignItems="center" gap={1}>
                            {getRiskLevelIcon(event.risk_level)}
                            <Typography variant="body2">
                              {event.event_type.replace('_', ' ')}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={event.risk_level}
                            color={getRiskLevelColor(event.risk_level)}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2" fontFamily="monospace">
                            {event.source_ip}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2" noWrap>
                            {event.description}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={event.is_resolved ? 'Resolved' : 'Open'}
                            color={event.is_resolved ? 'success' : 'warning'}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {new Date(event.created_at).toLocaleString()}
                          </Typography>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            ) : (
              <Box textAlign="center" py={4}>
                <CheckCircle sx={{ fontSize: 64, color: 'success.main', mb: 2 }} />
                <Typography variant="h6" color="text.secondary" gutterBottom>
                  No Security Events
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Your system is secure with no recent security events
                </Typography>
              </Box>
            )}
          </CardContent>
        </Card>
      </Box>
    </>
  );
};

export default SecurityPage;

# 🚀 TrustVault Deployment Guide

This guide covers deploying TrustVault to various environments, from local development to production cloud deployments.

## 📋 Pre-Deployment Checklist

### Security Checklist
- [ ] Change all default passwords and secret keys
- [ ] Configure HTTPS/SSL certificates
- [ ] Set up proper firewall rules
- [ ] Enable security headers
- [ ] Configure CORS properly
- [ ] Set up rate limiting
- [ ] Enable audit logging
- [ ] Configure backup strategy

### Performance Checklist
- [ ] Configure database connection pooling
- [ ] Set up Redis caching
- [ ] Configure static file serving
- [ ] Enable gzip compression
- [ ] Set up CDN (if needed)
- [ ] Configure monitoring and alerting

### Environment Checklist
- [ ] Set production environment variables
- [ ] Configure database settings
- [ ] Set up email configuration
- [ ] Configure external services
- [ ] Test all functionality
- [ ] Set up health checks

## 🏠 Local Development Deployment

### Quick Start
```bash
# Clone repository
git clone <repository-url>
cd trustvault

# Start development environment
./start.sh development  # Linux/Mac
start.bat development   # Windows
```

### Manual Setup
```bash
# Backend
cd backend
python -m venv venv
source venv/bin/activate  # Linux/Mac
venv\Scripts\activate     # Windows
pip install -r requirements.txt
python manage.py migrate
python manage.py runserver

# Frontend (new terminal)
cd frontend
npm install
npm start
```

## 🐳 Docker Deployment

### Development with Docker
```bash
# Start all services
docker-compose -f docker-compose.dev.yml up -d

# View logs
docker-compose -f docker-compose.dev.yml logs -f

# Stop services
docker-compose -f docker-compose.dev.yml down
```

### Production with Docker
```bash
# Prepare environment
cp .env.example .env.production
# Edit .env.production with production values

# Start production services
docker-compose --env-file .env.production up -d

# Monitor services
docker-compose logs -f
```

### Docker Swarm Deployment
```bash
# Initialize swarm
docker swarm init

# Deploy stack
docker stack deploy -c docker-compose.yml trustvault

# Monitor services
docker service ls
docker service logs trustvault_backend
```

## ☁️ Cloud Deployments

### AWS Deployment

#### Using AWS ECS (Elastic Container Service)

1. **Prepare ECR repositories**:
```bash
# Create repositories
aws ecr create-repository --repository-name trustvault-backend
aws ecr create-repository --repository-name trustvault-frontend

# Build and push images
docker build -t trustvault-backend ./backend
docker tag trustvault-backend:latest <account-id>.dkr.ecr.<region>.amazonaws.com/trustvault-backend:latest
docker push <account-id>.dkr.ecr.<region>.amazonaws.com/trustvault-backend:latest

docker build -t trustvault-frontend ./frontend
docker tag trustvault-frontend:latest <account-id>.dkr.ecr.<region>.amazonaws.com/trustvault-frontend:latest
docker push <account-id>.dkr.ecr.<region>.amazonaws.com/trustvault-frontend:latest
```

2. **Create ECS task definition**:
```json
{
  "family": "trustvault",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "1024",
  "memory": "2048",
  "executionRoleArn": "arn:aws:iam::<account-id>:role/ecsTaskExecutionRole",
  "containerDefinitions": [
    {
      "name": "backend",
      "image": "<account-id>.dkr.ecr.<region>.amazonaws.com/trustvault-backend:latest",
      "portMappings": [{"containerPort": 8000}],
      "environment": [
        {"name": "DJANGO_DEBUG", "value": "False"},
        {"name": "DATABASE_URL", "value": "postgresql://..."}
      ]
    },
    {
      "name": "frontend",
      "image": "<account-id>.dkr.ecr.<region>.amazonaws.com/trustvault-frontend:latest",
      "portMappings": [{"containerPort": 3000}]
    }
  ]
}
```

3. **Set up RDS and ElastiCache**:
```bash
# Create RDS PostgreSQL instance
aws rds create-db-instance \
  --db-instance-identifier trustvault-db \
  --db-instance-class db.t3.micro \
  --engine postgres \
  --master-username trustvault \
  --master-user-password <secure-password> \
  --allocated-storage 20

# Create ElastiCache Redis cluster
aws elasticache create-cache-cluster \
  --cache-cluster-id trustvault-redis \
  --cache-node-type cache.t3.micro \
  --engine redis \
  --num-cache-nodes 1
```

#### Using AWS EKS (Kubernetes)

1. **Create EKS cluster**:
```bash
eksctl create cluster --name trustvault --region us-west-2
```

2. **Deploy with Kubernetes manifests**:
```yaml
# trustvault-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: trustvault-backend
spec:
  replicas: 2
  selector:
    matchLabels:
      app: trustvault-backend
  template:
    metadata:
      labels:
        app: trustvault-backend
    spec:
      containers:
      - name: backend
        image: <account-id>.dkr.ecr.<region>.amazonaws.com/trustvault-backend:latest
        ports:
        - containerPort: 8000
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: trustvault-secrets
              key: database-url
```

### Google Cloud Platform Deployment

#### Using Google Cloud Run

1. **Build and push to Container Registry**:
```bash
# Configure Docker for GCR
gcloud auth configure-docker

# Build and push backend
docker build -t gcr.io/<project-id>/trustvault-backend ./backend
docker push gcr.io/<project-id>/trustvault-backend

# Build and push frontend
docker build -t gcr.io/<project-id>/trustvault-frontend ./frontend
docker push gcr.io/<project-id>/trustvault-frontend
```

2. **Deploy to Cloud Run**:
```bash
# Deploy backend
gcloud run deploy trustvault-backend \
  --image gcr.io/<project-id>/trustvault-backend \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated

# Deploy frontend
gcloud run deploy trustvault-frontend \
  --image gcr.io/<project-id>/trustvault-frontend \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated
```

3. **Set up Cloud SQL and Memorystore**:
```bash
# Create Cloud SQL PostgreSQL instance
gcloud sql instances create trustvault-db \
  --database-version=POSTGRES_13 \
  --tier=db-f1-micro \
  --region=us-central1

# Create Memorystore Redis instance
gcloud redis instances create trustvault-redis \
  --size=1 \
  --region=us-central1
```

### DigitalOcean Deployment

#### Using DigitalOcean App Platform

1. **Create app specification**:
```yaml
# .do/app.yaml
name: trustvault
services:
- name: backend
  source_dir: backend
  github:
    repo: your-username/trustvault
    branch: main
  run_command: gunicorn --bind 0.0.0.0:8000 trustvault.wsgi:application
  environment_slug: python
  instance_count: 1
  instance_size_slug: basic-xxs
  envs:
  - key: DJANGO_DEBUG
    value: "False"
  - key: DATABASE_URL
    value: "${db.DATABASE_URL}"

- name: frontend
  source_dir: frontend
  github:
    repo: your-username/trustvault
    branch: main
  run_command: serve -s build -l 3000
  environment_slug: node-js
  instance_count: 1
  instance_size_slug: basic-xxs

databases:
- name: db
  engine: PG
  version: "13"
  size: db-s-dev-database
```

2. **Deploy using doctl**:
```bash
# Install doctl and authenticate
doctl apps create --spec .do/app.yaml

# Monitor deployment
doctl apps list
doctl apps logs <app-id>
```

## 🔧 Environment Configuration

### Production Environment Variables

Create a `.env.production` file:

```env
# Django Settings
DJANGO_DEBUG=False
DJANGO_SECRET_KEY=your-very-long-and-secure-secret-key-here
DJANGO_ALLOWED_HOSTS=your-domain.com,www.your-domain.com
DJANGO_SECURE_SSL_REDIRECT=True
DJANGO_SECURE_HSTS_SECONDS=31536000

# Database
DATABASE_URL=************************************/database
POSTGRES_DB=trustvault
POSTGRES_USER=trustvault
POSTGRES_PASSWORD=very-secure-password

# Redis
REDIS_URL=redis://redis-host:6379/0

# Security
ENCRYPTION_KEY=your-32-character-encryption-key-here
JWT_SECRET_KEY=your-jwt-secret-key-here
JWT_ACCESS_TOKEN_LIFETIME=15
JWT_REFRESH_TOKEN_LIFETIME=7

# Email
EMAIL_HOST=smtp.your-provider.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-email-password

# Frontend
REACT_APP_API_URL=https://your-domain.com/api/v1
REACT_APP_ENVIRONMENT=production

# Monitoring (Optional)
SENTRY_DSN=your-sentry-dsn-here
```

### SSL/TLS Configuration

#### Using Let's Encrypt with Certbot

1. **Install Certbot**:
```bash
# Ubuntu/Debian
sudo apt install certbot python3-certbot-nginx

# CentOS/RHEL
sudo yum install certbot python3-certbot-nginx
```

2. **Obtain SSL certificate**:
```bash
sudo certbot --nginx -d your-domain.com -d www.your-domain.com
```

3. **Auto-renewal**:
```bash
# Add to crontab
0 12 * * * /usr/bin/certbot renew --quiet
```

#### Using CloudFlare SSL

1. **Configure CloudFlare**:
   - Add your domain to CloudFlare
   - Set SSL/TLS mode to "Full (strict)"
   - Enable "Always Use HTTPS"

2. **Update nginx configuration**:
```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    ssl_certificate /path/to/cloudflare/cert.pem;
    ssl_certificate_key /path/to/cloudflare/key.pem;
    
    # ... rest of configuration
}
```

## 📊 Monitoring and Logging

### Application Monitoring

#### Using Sentry for Error Tracking

1. **Install Sentry SDK**:
```bash
pip install sentry-sdk[django]
npm install @sentry/react
```

2. **Configure Django**:
```python
import sentry_sdk
from sentry_sdk.integrations.django import DjangoIntegration

sentry_sdk.init(
    dsn="your-sentry-dsn",
    integrations=[DjangoIntegration()],
    traces_sample_rate=1.0,
    send_default_pii=True
)
```

#### Using Prometheus and Grafana

1. **Add monitoring to docker-compose**:
```yaml
services:
  prometheus:
    image: prom/prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml

  grafana:
    image: grafana/grafana
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
```

### Log Management

#### Centralized Logging with ELK Stack

```yaml
services:
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:7.14.0
    environment:
      - discovery.type=single-node
    ports:
      - "9200:9200"

  logstash:
    image: docker.elastic.co/logstash/logstash:7.14.0
    volumes:
      - ./logstash/pipeline:/usr/share/logstash/pipeline

  kibana:
    image: docker.elastic.co/kibana/kibana:7.14.0
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
```

## 🔄 CI/CD Pipeline

### GitHub Actions Example

```yaml
# .github/workflows/deploy.yml
name: Deploy TrustVault

on:
  push:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    - name: Run tests
      run: |
        docker-compose -f docker-compose.test.yml up --abort-on-container-exit

  deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
    - uses: actions/checkout@v2
    - name: Deploy to production
      run: |
        # Your deployment script here
        ./deploy.sh production
```

## 🆘 Troubleshooting Deployment Issues

### Common Issues

1. **Database Connection Errors**:
   - Check database credentials
   - Verify network connectivity
   - Ensure database is running

2. **Static Files Not Loading**:
   - Run `python manage.py collectstatic`
   - Check nginx configuration
   - Verify file permissions

3. **CORS Errors**:
   - Update `CORS_ALLOWED_ORIGINS` setting
   - Check frontend API URL configuration

4. **SSL Certificate Issues**:
   - Verify certificate validity
   - Check nginx SSL configuration
   - Ensure proper certificate chain

### Health Checks

```bash
# Backend health
curl -f https://your-domain.com/health/

# Database connectivity
docker-compose exec backend python manage.py dbshell

# Redis connectivity
docker-compose exec redis redis-cli ping
```

## 📈 Scaling Considerations

### Horizontal Scaling

1. **Load Balancer Configuration**:
```nginx
upstream backend_servers {
    server backend1:8000;
    server backend2:8000;
    server backend3:8000;
}

upstream frontend_servers {
    server frontend1:3000;
    server frontend2:3000;
}
```

2. **Database Scaling**:
   - Use read replicas for read-heavy workloads
   - Implement connection pooling
   - Consider database sharding for large datasets

3. **Caching Strategy**:
   - Redis cluster for session storage
   - CDN for static assets
   - Application-level caching

### Vertical Scaling

- Increase CPU and memory allocation
- Optimize database queries
- Use database indexing
- Implement query optimization

## 🔐 Security Hardening

### Container Security
```dockerfile
# Use non-root user
RUN groupadd -r trustvault && useradd -r -g trustvault trustvault
USER trustvault

# Remove unnecessary packages
RUN apt-get remove --purge -y build-essential && \
    apt-get autoremove -y && \
    rm -rf /var/lib/apt/lists/*
```

### Network Security
- Use private networks for internal communication
- Implement firewall rules
- Enable VPN for administrative access
- Use security groups/network ACLs

### Data Protection
- Encrypt data at rest
- Use encrypted connections (TLS/SSL)
- Implement proper backup encryption
- Regular security audits

---

**For more deployment options and troubleshooting, see the [Setup Guide](SETUP_GUIDE.md)**

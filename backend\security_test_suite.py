#!/usr/bin/env python
"""
TrustVault - Security Test Suite
Comprehensive security testing and attack simulation
"""

import os
import sys
import django
import requests
import time
import threading
import json
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'trustvault.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.test import Client
from apps.security.models import SecurityEvent, ThreatIntelligence
from apps.authentication.models import LoginAttempt

User = get_user_model()

class SecurityTestSuite:
    def __init__(self):
        self.base_url = "http://localhost:8000"
        self.results = []
        
    def log_result(self, test_name, status, details):
        """Log test result."""
        result = {
            'test': test_name,
            'status': status,
            'details': details,
            'timestamp': datetime.now().isoformat()
        }
        self.results.append(result)
        status_icon = "✅" if status == "PASS" else "❌" if status == "FAIL" else "⚠️"
        print(f"{status_icon} {test_name}: {details}")

    def test_brute_force_protection(self):
        """Test brute force attack protection."""
        print("\n🔒 Testing Brute Force Protection")
        print("=" * 40)
        
        session = requests.Session()
        failed_attempts = 0
        
        # Attempt multiple failed logins
        for i in range(15):
            try:
                response = session.post(f"{self.base_url}/api/v1/auth/login/", 
                    json={
                        'email': '<EMAIL>',
                        'password': f'wrong_password_{i}'
                    },
                    timeout=5
                )
                
                if response.status_code in [400, 401, 429]:
                    failed_attempts += 1
                    
                if response.status_code == 429:  # Rate limited
                    self.log_result("Brute Force Protection", "PASS", 
                        f"Rate limiting activated after {failed_attempts} attempts")
                    return True
                    
            except requests.exceptions.RequestException as e:
                self.log_result("Brute Force Protection", "WARNING", 
                    f"Connection error: {e}")
                return False
                
            time.sleep(0.1)  # Small delay between attempts
        
        self.log_result("Brute Force Protection", "FAIL", 
            "No rate limiting detected after 15 failed attempts")
        return False

    def test_sql_injection_protection(self):
        """Test SQL injection protection."""
        print("\n💉 Testing SQL Injection Protection")
        print("=" * 40)
        
        sql_payloads = [
            "' OR '1'='1",
            "'; DROP TABLE users; --",
            "' UNION SELECT * FROM users --",
            "admin'--",
            "' OR 1=1#"
        ]
        
        session = requests.Session()
        
        # Login first
        try:
            login_response = session.post(f"{self.base_url}/api/v1/auth/login/", 
                json={
                    'email': '<EMAIL>',
                    'password': 'password123'
                })
            
            if login_response.status_code != 200:
                self.log_result("SQL Injection Test", "WARNING", 
                    "Could not authenticate for testing")
                return False
        except:
            self.log_result("SQL Injection Test", "WARNING", 
                "Authentication failed")
            return False
        
        blocked_count = 0
        
        for payload in sql_payloads:
            try:
                # Test on search endpoint
                response = session.get(f"{self.base_url}/api/v1/portfolio/", 
                    params={'search': payload},
                    timeout=5
                )
                
                if response.status_code in [400, 403, 422]:
                    blocked_count += 1
                elif response.status_code == 500:
                    self.log_result("SQL Injection Test", "FAIL", 
                        f"Server error with payload: {payload}")
                    return False
                    
            except requests.exceptions.RequestException:
                blocked_count += 1  # Connection refused = blocked
        
        if blocked_count >= len(sql_payloads) * 0.8:  # 80% blocked
            self.log_result("SQL Injection Protection", "PASS", 
                f"{blocked_count}/{len(sql_payloads)} payloads blocked")
            return True
        else:
            self.log_result("SQL Injection Protection", "FAIL", 
                f"Only {blocked_count}/{len(sql_payloads)} payloads blocked")
            return False

    def test_xss_protection(self):
        """Test XSS protection."""
        print("\n🕷️ Testing XSS Protection")
        print("=" * 35)
        
        xss_payloads = [
            "<script>alert('XSS')</script>",
            "javascript:alert('XSS')",
            "<img src=x onerror=alert('XSS')>",
            "<svg onload=alert('XSS')>",
            "';alert('XSS');//"
        ]
        
        session = requests.Session()
        
        # Login first
        try:
            session.post(f"{self.base_url}/api/v1/auth/login/", 
                json={'email': '<EMAIL>', 'password': 'password123'})
        except:
            pass
        
        blocked_count = 0
        
        for payload in xss_payloads:
            try:
                # Test on profile update
                response = session.patch(f"{self.base_url}/api/v1/auth/profile/", 
                    json={'first_name': payload},
                    timeout=5
                )
                
                if response.status_code in [400, 403, 422]:
                    blocked_count += 1
                elif response.status_code == 200:
                    # Check if payload was sanitized
                    profile_response = session.get(f"{self.base_url}/api/v1/auth/profile/")
                    if profile_response.status_code == 200:
                        profile_data = profile_response.json()
                        if payload not in str(profile_data):
                            blocked_count += 1
                            
            except requests.exceptions.RequestException:
                blocked_count += 1
        
        if blocked_count >= len(xss_payloads) * 0.8:
            self.log_result("XSS Protection", "PASS", 
                f"{blocked_count}/{len(xss_payloads)} payloads blocked/sanitized")
            return True
        else:
            self.log_result("XSS Protection", "FAIL", 
                f"Only {blocked_count}/{len(xss_payloads)} payloads blocked")
            return False

    def test_ddos_protection(self):
        """Test DDoS protection."""
        print("\n🌊 Testing DDoS Protection")
        print("=" * 30)
        
        def make_request():
            try:
                response = requests.get(f"{self.base_url}/api/v1/alerts/alerts/choices/", 
                    timeout=2)
                return response.status_code
            except:
                return 0
        
        # Send 100 concurrent requests
        with ThreadPoolExecutor(max_workers=50) as executor:
            futures = [executor.submit(make_request) for _ in range(100)]
            results = [future.result() for future in futures]
        
        rate_limited = sum(1 for r in results if r == 429)
        successful = sum(1 for r in results if r == 200)
        failed = sum(1 for r in results if r == 0)
        
        if rate_limited > 10 or failed > 50:
            self.log_result("DDoS Protection", "PASS", 
                f"Rate limiting active: {rate_limited} limited, {failed} failed")
            return True
        else:
            self.log_result("DDoS Protection", "WARNING", 
                f"Minimal protection: {successful} successful, {rate_limited} limited")
            return False

    def test_authentication_security(self):
        """Test authentication security."""
        print("\n🔐 Testing Authentication Security")
        print("=" * 40)
        
        tests_passed = 0
        total_tests = 4
        
        # Test 1: Weak password rejection
        try:
            response = requests.post(f"{self.base_url}/api/v1/auth/register/", 
                json={
                    'email': '<EMAIL>',
                    'password': '123',
                    'first_name': 'Test',
                    'last_name': 'User'
                })
            
            if response.status_code in [400, 422]:
                self.log_result("Weak Password Rejection", "PASS", "Weak password rejected")
                tests_passed += 1
            else:
                self.log_result("Weak Password Rejection", "FAIL", "Weak password accepted")
        except:
            self.log_result("Weak Password Rejection", "WARNING", "Could not test")
        
        # Test 2: Email validation
        try:
            response = requests.post(f"{self.base_url}/api/v1/auth/register/", 
                json={
                    'email': 'invalid-email',
                    'password': 'StrongPassword123!',
                    'first_name': 'Test',
                    'last_name': 'User'
                })
            
            if response.status_code in [400, 422]:
                self.log_result("Email Validation", "PASS", "Invalid email rejected")
                tests_passed += 1
            else:
                self.log_result("Email Validation", "FAIL", "Invalid email accepted")
        except:
            self.log_result("Email Validation", "WARNING", "Could not test")
        
        # Test 3: Session security
        session = requests.Session()
        try:
            login_response = session.post(f"{self.base_url}/api/v1/auth/login/", 
                json={'email': '<EMAIL>', 'password': 'password123'})
            
            if login_response.status_code == 200:
                # Check if session cookies are secure
                cookies = session.cookies
                secure_cookies = any('secure' in str(cookie).lower() for cookie in cookies)
                
                if secure_cookies or 'localhost' in self.base_url:
                    self.log_result("Session Security", "PASS", "Secure session handling")
                    tests_passed += 1
                else:
                    self.log_result("Session Security", "FAIL", "Insecure session cookies")
            else:
                self.log_result("Session Security", "WARNING", "Could not test session")
        except:
            self.log_result("Session Security", "WARNING", "Could not test")
        
        # Test 4: Logout functionality
        try:
            logout_response = session.post(f"{self.base_url}/api/v1/auth/logout/")
            if logout_response.status_code in [200, 204]:
                # Try to access protected resource
                protected_response = session.get(f"{self.base_url}/api/v1/auth/profile/")
                if protected_response.status_code in [401, 403]:
                    self.log_result("Logout Security", "PASS", "Session properly invalidated")
                    tests_passed += 1
                else:
                    self.log_result("Logout Security", "FAIL", "Session not invalidated")
            else:
                self.log_result("Logout Security", "WARNING", "Logout failed")
        except:
            self.log_result("Logout Security", "WARNING", "Could not test")
        
        return tests_passed >= total_tests * 0.75

    def test_data_protection(self):
        """Test data protection measures."""
        print("\n🛡️ Testing Data Protection")
        print("=" * 35)
        
        session = requests.Session()
        
        # Login
        try:
            session.post(f"{self.base_url}/api/v1/auth/login/", 
                json={'email': '<EMAIL>', 'password': 'password123'})
        except:
            self.log_result("Data Protection", "WARNING", "Could not authenticate")
            return False
        
        tests_passed = 0
        total_tests = 3
        
        # Test 1: Unauthorized access prevention
        try:
            # Try to access another user's data
            response = session.get(f"{self.base_url}/api/v1/portfolio/99999999-9999-9999-9999-999999999999/")
            
            if response.status_code in [403, 404]:
                self.log_result("Unauthorized Access Prevention", "PASS", 
                    "Unauthorized access blocked")
                tests_passed += 1
            else:
                self.log_result("Unauthorized Access Prevention", "FAIL", 
                    "Unauthorized access allowed")
        except:
            self.log_result("Unauthorized Access Prevention", "WARNING", "Could not test")
        
        # Test 2: Data encryption in transit
        try:
            response = session.get(f"{self.base_url}/api/v1/auth/profile/")
            
            if response.status_code == 200:
                # Check response headers for security
                headers = response.headers
                security_headers = ['X-Content-Type-Options', 'X-Frame-Options', 'X-XSS-Protection']
                present_headers = sum(1 for h in security_headers if h in headers)
                
                if present_headers >= 2:
                    self.log_result("Security Headers", "PASS", 
                        f"{present_headers}/{len(security_headers)} security headers present")
                    tests_passed += 1
                else:
                    self.log_result("Security Headers", "FAIL", 
                        f"Only {present_headers}/{len(security_headers)} security headers")
        except:
            self.log_result("Security Headers", "WARNING", "Could not test")
        
        # Test 3: Input validation
        try:
            # Try to send malformed data
            response = session.post(f"{self.base_url}/api/v1/alerts/alerts/", 
                json={'invalid': 'data', 'malicious': '<script>alert("xss")</script>'})
            
            if response.status_code in [400, 422]:
                self.log_result("Input Validation", "PASS", "Malformed data rejected")
                tests_passed += 1
            else:
                self.log_result("Input Validation", "FAIL", "Malformed data accepted")
        except:
            self.log_result("Input Validation", "WARNING", "Could not test")
        
        return tests_passed >= total_tests * 0.67

    def run_all_tests(self):
        """Run all security tests."""
        print("🚀 TrustVault Security Test Suite")
        print("=" * 50)
        print(f"Target: {self.base_url}")
        print(f"Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 50)
        
        # Run all tests
        test_results = []
        test_results.append(self.test_brute_force_protection())
        test_results.append(self.test_sql_injection_protection())
        test_results.append(self.test_xss_protection())
        test_results.append(self.test_ddos_protection())
        test_results.append(self.test_authentication_security())
        test_results.append(self.test_data_protection())
        
        # Summary
        passed = sum(test_results)
        total = len(test_results)
        
        print("\n" + "=" * 50)
        print("📊 SECURITY TEST SUMMARY")
        print("=" * 50)
        print(f"Tests Passed: {passed}/{total}")
        print(f"Success Rate: {(passed/total)*100:.1f}%")
        
        if passed == total:
            print("🎉 ALL SECURITY TESTS PASSED!")
        elif passed >= total * 0.8:
            print("✅ GOOD SECURITY POSTURE")
        elif passed >= total * 0.6:
            print("⚠️ MODERATE SECURITY - IMPROVEMENTS NEEDED")
        else:
            print("❌ POOR SECURITY - IMMEDIATE ACTION REQUIRED")
        
        print("\n📝 Detailed Results:")
        for result in self.results:
            status_icon = "✅" if result['status'] == "PASS" else "❌" if result['status'] == "FAIL" else "⚠️"
            print(f"   {status_icon} {result['test']}: {result['details']}")
        
        return passed >= total * 0.8


if __name__ == '__main__':
    suite = SecurityTestSuite()
    success = suite.run_all_tests()
    
    if success:
        print("\n🎯 Security system is functioning correctly!")
    else:
        print("\n⚠️ Security improvements needed!")
    
    sys.exit(0 if success else 1)

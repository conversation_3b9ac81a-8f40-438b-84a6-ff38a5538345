# Generated by Django 4.2.7 on 2025-07-29 10:24

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('security', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='ComplianceAudit',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('framework', models.CharField(choices=[('ISO_27001', 'ISO 27001'), ('SOC_2', 'SOC 2'), ('GDPR', 'GDPR'), ('HIPAA', 'HIPAA'), ('PCI_DSS', 'PCI DSS'), ('NIST', 'NIST Cybersecurity Framework'), ('CIS', 'CIS Controls'), ('COBIT', 'COBIT')], max_length=20)),
                ('control_id', models.CharField(max_length=50)),
                ('control_name', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('status', models.CharField(choices=[('PLANNED', 'Planned'), ('IN_PROGRESS', 'In Progress'), ('COMPLETED', 'Completed'), ('FAILED', 'Failed'), ('REMEDIATION_REQUIRED', 'Remediation Required')], max_length=30)),
                ('audit_date', models.DateTimeField()),
                ('next_audit_date', models.DateTimeField()),
                ('compliance_score', models.IntegerField(default=0)),
                ('findings', models.JSONField(blank=True, default=list)),
                ('recommendations', models.JSONField(blank=True, default=list)),
                ('remediation_actions', models.JSONField(blank=True, default=list)),
                ('evidence_files', models.JSONField(blank=True, default=list)),
                ('documentation_links', models.JSONField(blank=True, default=list)),
            ],
            options={
                'ordering': ['-audit_date'],
            },
        ),
        migrations.CreateModel(
            name='DeviceFingerprint',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('fingerprint_hash', models.CharField(max_length=64, unique=True)),
                ('user_agent', models.TextField()),
                ('browser_family', models.CharField(blank=True, max_length=100)),
                ('browser_version', models.CharField(blank=True, max_length=50)),
                ('os_family', models.CharField(blank=True, max_length=100)),
                ('os_version', models.CharField(blank=True, max_length=50)),
                ('device_family', models.CharField(blank=True, max_length=100)),
                ('screen_resolution', models.CharField(blank=True, max_length=20)),
                ('timezone', models.CharField(blank=True, max_length=50)),
                ('language', models.CharField(blank=True, max_length=10)),
                ('plugins', models.JSONField(blank=True, default=list)),
                ('first_seen', models.DateTimeField(auto_now_add=True)),
                ('last_seen', models.DateTimeField(auto_now=True)),
                ('is_trusted', models.BooleanField(default=False)),
                ('trust_score', models.IntegerField(default=0)),
            ],
            options={
                'ordering': ['-last_seen'],
            },
        ),
        migrations.CreateModel(
            name='IncidentResponse',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('incident_type', models.CharField(choices=[('INTRUSION_ATTEMPT', 'Intrusion Attempt'), ('DATA_BREACH', 'Data Breach'), ('MALWARE_DETECTION', 'Malware Detection'), ('UNAUTHORIZED_ACCESS', 'Unauthorized Access'), ('DDOS_ATTACK', 'DDoS Attack'), ('PHISHING_ATTEMPT', 'Phishing Attempt'), ('INSIDER_THREAT', 'Insider Threat'), ('SYSTEM_COMPROMISE', 'System Compromise'), ('AUTOMATED_THREAT_RESPONSE', 'Automated Threat Response'), ('SECURITY_ASSESSMENT_CRITICAL_FINDINGS', 'Security Assessment Critical Findings')], max_length=50)),
                ('severity', models.CharField(choices=[('LOW', 'Low'), ('MEDIUM', 'Medium'), ('HIGH', 'High'), ('CRITICAL', 'Critical')], max_length=20)),
                ('status', models.CharField(choices=[('OPEN', 'Open'), ('IN_PROGRESS', 'In Progress'), ('RESOLVED', 'Resolved'), ('CLOSED', 'Closed'), ('FALSE_POSITIVE', 'False Positive')], default='OPEN', max_length=20)),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('affected_systems', models.JSONField(blank=True, default=list)),
                ('response_actions', models.JSONField(blank=True, default=list)),
                ('resolved_at', models.DateTimeField(blank=True, null=True)),
                ('detected_at', models.DateTimeField(auto_now_add=True)),
                ('first_response_at', models.DateTimeField(blank=True, null=True)),
                ('containment_at', models.DateTimeField(blank=True, null=True)),
                ('eradication_at', models.DateTimeField(blank=True, null=True)),
                ('recovery_at', models.DateTimeField(blank=True, null=True)),
                ('details', models.JSONField(blank=True, default=dict)),
                ('lessons_learned', models.TextField(blank=True)),
            ],
            options={
                'ordering': ['-detected_at'],
            },
        ),
        migrations.CreateModel(
            name='RiskAssessment',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('risk_score', models.IntegerField()),
                ('risk_level', models.CharField(choices=[('LOW', 'Low'), ('MEDIUM', 'Medium'), ('HIGH', 'High'), ('CRITICAL', 'Critical')], max_length=20)),
                ('risk_factors', models.JSONField(default=list)),
                ('ip_address', models.GenericIPAddressField()),
                ('user_agent', models.TextField()),
                ('country', models.CharField(blank=True, max_length=100)),
                ('city', models.CharField(blank=True, max_length=100)),
                ('is_vpn', models.BooleanField(default=False)),
                ('is_tor', models.BooleanField(default=False)),
                ('login_frequency_score', models.IntegerField(default=0)),
                ('device_trust_score', models.IntegerField(default=0)),
                ('time_based_score', models.IntegerField(default=0)),
                ('location_based_score', models.IntegerField(default=0)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='UserSession',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('session_key', models.CharField(max_length=128, unique=True)),
                ('ip_address', models.GenericIPAddressField()),
                ('user_agent', models.TextField()),
                ('risk_score', models.IntegerField(default=0)),
                ('expires_at', models.DateTimeField()),
                ('is_active', models.BooleanField(default=True)),
                ('country', models.CharField(blank=True, max_length=100)),
                ('city', models.CharField(blank=True, max_length=100)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.RemoveIndex(
            model_name='threatintelligence',
            name='security_th_threat__c27c21_idx',
        ),
        migrations.RemoveIndex(
            model_name='threatintelligence',
            name='security_th_severit_6869ab_idx',
        ),
        migrations.RemoveIndex(
            model_name='threatintelligence',
            name='security_th_expires_82f8fe_idx',
        ),
        migrations.AddField(
            model_name='threatintelligence',
            name='is_active',
            field=models.BooleanField(default=True),
        ),
        migrations.AddIndex(
            model_name='threatintelligence',
            index=models.Index(fields=['threat_type', 'expires_at'], name='security_th_threat__0c544d_idx'),
        ),
        migrations.AddIndex(
            model_name='threatintelligence',
            index=models.Index(fields=['severity'], name='security_th_severit_820a95_idx'),
        ),
        migrations.AddIndex(
            model_name='threatintelligence',
            index=models.Index(fields=['confidence'], name='security_th_confide_d04c9d_idx'),
        ),
        migrations.AlterModelTable(
            name='threatintelligence',
            table=None,
        ),
        migrations.AddField(
            model_name='usersession',
            name='device_fingerprint',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='security.devicefingerprint'),
        ),
        migrations.AddField(
            model_name='usersession',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='riskassessment',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='incidentresponse',
            name='affected_users',
            field=models.ManyToManyField(blank=True, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='incidentresponse',
            name='assigned_to',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_incidents', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='incidentresponse',
            name='resolved_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='resolved_incidents', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='devicefingerprint',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='complianceaudit',
            name='auditor',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddIndex(
            model_name='usersession',
            index=models.Index(fields=['user', 'is_active'], name='security_us_user_id_9aa362_idx'),
        ),
        migrations.AddIndex(
            model_name='usersession',
            index=models.Index(fields=['session_key'], name='security_us_session_cd98d8_idx'),
        ),
        migrations.AddIndex(
            model_name='usersession',
            index=models.Index(fields=['expires_at'], name='security_us_expires_f5812f_idx'),
        ),
        migrations.AddIndex(
            model_name='riskassessment',
            index=models.Index(fields=['user', 'created_at'], name='security_ri_user_id_66cd3e_idx'),
        ),
        migrations.AddIndex(
            model_name='riskassessment',
            index=models.Index(fields=['risk_level', 'created_at'], name='security_ri_risk_le_a67352_idx'),
        ),
        migrations.AddIndex(
            model_name='riskassessment',
            index=models.Index(fields=['risk_score'], name='security_ri_risk_sc_d03de0_idx'),
        ),
        migrations.AddIndex(
            model_name='incidentresponse',
            index=models.Index(fields=['incident_type', 'status'], name='security_in_inciden_69547a_idx'),
        ),
        migrations.AddIndex(
            model_name='incidentresponse',
            index=models.Index(fields=['severity', 'detected_at'], name='security_in_severit_99d19a_idx'),
        ),
        migrations.AddIndex(
            model_name='incidentresponse',
            index=models.Index(fields=['assigned_to', 'status'], name='security_in_assigne_b96c56_idx'),
        ),
        migrations.AddIndex(
            model_name='devicefingerprint',
            index=models.Index(fields=['user', 'last_seen'], name='security_de_user_id_badfac_idx'),
        ),
        migrations.AddIndex(
            model_name='devicefingerprint',
            index=models.Index(fields=['fingerprint_hash'], name='security_de_fingerp_d2a57a_idx'),
        ),
        migrations.AddIndex(
            model_name='devicefingerprint',
            index=models.Index(fields=['is_trusted'], name='security_de_is_trus_2c5694_idx'),
        ),
        migrations.AddIndex(
            model_name='complianceaudit',
            index=models.Index(fields=['framework', 'status'], name='security_co_framewo_baabcc_idx'),
        ),
        migrations.AddIndex(
            model_name='complianceaudit',
            index=models.Index(fields=['audit_date'], name='security_co_audit_d_02eb4a_idx'),
        ),
        migrations.AddIndex(
            model_name='complianceaudit',
            index=models.Index(fields=['next_audit_date'], name='security_co_next_au_e4d2eb_idx'),
        ),
    ]

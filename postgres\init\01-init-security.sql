-- TrustVault - PostgreSQL Security Initialization

-- ============================================================================
-- SECURITY EXTENSIONS
-- ============================================================================

-- Enable pgcrypto for encryption functions
CREATE EXTENSION IF NOT EXISTS pgcrypto;

-- Enable uuid-ossp for UUID generation
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Enable pg_stat_statements for query monitoring
CREATE EXTENSION IF NOT EXISTS pg_stat_statements;

-- Enable pg_audit for audit logging (if available)
-- CREATE EXTENSION IF NOT EXISTS pgaudit;

-- ============================================================================
-- SECURITY ROLES AND USERS
-- ============================================================================

-- Create application role with limited privileges
CREATE ROLE trustvault_app;

-- Grant necessary privileges to application role
GRANT CONNECT ON DATABASE trustvault TO trustvault_app;
GRANT USAGE ON SCHEMA public TO trustvault_app;
GRANT CREATE ON SCHEMA public TO trustvault_app;

-- Create read-only role for reporting
CREATE ROLE trustvault_readonly;
GRANT CONNECT ON DATABASE trustvault TO trustvault_readonly;
GRANT USAGE ON SCHEMA public TO trustvault_readonly;

-- Create backup role
CREATE ROLE trustvault_backup;
GRANT CONNECT ON DATABASE trustvault TO trustvault_backup;

-- Assign roles to main user
GRANT trustvault_app TO trustvault;

-- ============================================================================
-- AUDIT TABLES
-- ============================================================================

-- Create audit log table
CREATE TABLE IF NOT EXISTS audit_log (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    table_name VARCHAR(255) NOT NULL,
    operation VARCHAR(10) NOT NULL,
    old_values JSONB,
    new_values JSONB,
    user_id UUID,
    username VARCHAR(255),
    ip_address INET,
    user_agent TEXT,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    session_id VARCHAR(255)
);

-- Create index for performance
CREATE INDEX IF NOT EXISTS idx_audit_log_timestamp ON audit_log(timestamp);
CREATE INDEX IF NOT EXISTS idx_audit_log_table_name ON audit_log(table_name);
CREATE INDEX IF NOT EXISTS idx_audit_log_user_id ON audit_log(user_id);

-- ============================================================================
-- SECURITY FUNCTIONS
-- ============================================================================

-- Function to encrypt sensitive data
CREATE OR REPLACE FUNCTION encrypt_sensitive_data(data TEXT, key TEXT DEFAULT 'trustvault_encryption_key')
RETURNS TEXT AS $$
BEGIN
    RETURN encode(pgp_sym_encrypt(data, key), 'base64');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to decrypt sensitive data
CREATE OR REPLACE FUNCTION decrypt_sensitive_data(encrypted_data TEXT, key TEXT DEFAULT 'trustvault_encryption_key')
RETURNS TEXT AS $$
BEGIN
    RETURN pgp_sym_decrypt(decode(encrypted_data, 'base64'), key);
EXCEPTION
    WHEN OTHERS THEN
        RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to hash passwords
CREATE OR REPLACE FUNCTION hash_password(password TEXT)
RETURNS TEXT AS $$
BEGIN
    RETURN crypt(password, gen_salt('bf', 12));
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to verify passwords
CREATE OR REPLACE FUNCTION verify_password(password TEXT, hash TEXT)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN hash = crypt(password, hash);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ============================================================================
-- AUDIT TRIGGER FUNCTION
-- ============================================================================

CREATE OR REPLACE FUNCTION audit_trigger_function()
RETURNS TRIGGER AS $$
DECLARE
    old_data JSONB;
    new_data JSONB;
    current_user_id UUID;
    current_username VARCHAR(255);
    current_ip INET;
    current_user_agent TEXT;
    current_session_id VARCHAR(255);
BEGIN
    -- Get current user context (these would be set by the application)
    current_user_id := current_setting('app.current_user_id', true)::UUID;
    current_username := current_setting('app.current_username', true);
    current_ip := current_setting('app.current_ip', true)::INET;
    current_user_agent := current_setting('app.current_user_agent', true);
    current_session_id := current_setting('app.current_session_id', true);

    -- Handle different operations
    IF TG_OP = 'DELETE' THEN
        old_data := to_jsonb(OLD);
        new_data := NULL;
    ELSIF TG_OP = 'UPDATE' THEN
        old_data := to_jsonb(OLD);
        new_data := to_jsonb(NEW);
    ELSIF TG_OP = 'INSERT' THEN
        old_data := NULL;
        new_data := to_jsonb(NEW);
    END IF;

    -- Insert audit record
    INSERT INTO audit_log (
        table_name,
        operation,
        old_values,
        new_values,
        user_id,
        username,
        ip_address,
        user_agent,
        session_id
    ) VALUES (
        TG_TABLE_NAME,
        TG_OP,
        old_data,
        new_data,
        current_user_id,
        current_username,
        current_ip,
        current_user_agent,
        current_session_id
    );

    -- Return appropriate record
    IF TG_OP = 'DELETE' THEN
        RETURN OLD;
    ELSE
        RETURN NEW;
    END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ============================================================================
-- SECURITY POLICIES
-- ============================================================================

-- Enable Row Level Security on audit_log
ALTER TABLE audit_log ENABLE ROW LEVEL SECURITY;

-- Policy to allow users to see only their own audit records
CREATE POLICY audit_log_user_policy ON audit_log
    FOR SELECT
    USING (user_id = current_setting('app.current_user_id', true)::UUID);

-- Policy to allow admins to see all audit records
CREATE POLICY audit_log_admin_policy ON audit_log
    FOR ALL
    USING (current_setting('app.user_role', true) = 'admin');

-- ============================================================================
-- SECURITY VIEWS
-- ============================================================================

-- View for security monitoring
CREATE OR REPLACE VIEW security_events AS
SELECT 
    id,
    table_name,
    operation,
    user_id,
    username,
    ip_address,
    timestamp,
    CASE 
        WHEN operation = 'DELETE' AND table_name IN ('users', 'portfolios', 'transactions') THEN 'HIGH'
        WHEN operation = 'UPDATE' AND table_name IN ('users', 'portfolios') THEN 'MEDIUM'
        WHEN ip_address NOT IN (SELECT unnest(string_to_array(current_setting('app.trusted_ips', true), ','))) THEN 'HIGH'
        ELSE 'LOW'
    END as risk_level
FROM audit_log
WHERE timestamp >= NOW() - INTERVAL '24 hours'
ORDER BY timestamp DESC;

-- View for failed login attempts
CREATE OR REPLACE VIEW failed_login_attempts AS
SELECT 
    ip_address,
    COUNT(*) as attempt_count,
    MAX(timestamp) as last_attempt,
    MIN(timestamp) as first_attempt
FROM audit_log 
WHERE table_name = 'auth_attempts' 
    AND new_values->>'success' = 'false'
    AND timestamp >= NOW() - INTERVAL '1 hour'
GROUP BY ip_address
HAVING COUNT(*) >= 3
ORDER BY attempt_count DESC;

-- ============================================================================
-- SECURITY CONSTRAINTS
-- ============================================================================

-- Function to validate IP addresses
CREATE OR REPLACE FUNCTION validate_ip_address()
RETURNS TRIGGER AS $$
BEGIN
    -- Block known malicious IP ranges (example)
    IF NEW.ip_address <<= '10.0.0.0/8'::inet OR 
       NEW.ip_address <<= '**********/12'::inet OR 
       NEW.ip_address <<= '***********/16'::inet THEN
        -- Allow private IPs
        RETURN NEW;
    END IF;
    
    -- Add additional IP validation logic here
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- ============================================================================
-- CLEANUP PROCEDURES
-- ============================================================================

-- Function to clean old audit logs
CREATE OR REPLACE FUNCTION cleanup_audit_logs(retention_days INTEGER DEFAULT 2555)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM audit_log 
    WHERE timestamp < NOW() - (retention_days || ' days')::INTERVAL;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    -- Log the cleanup operation
    INSERT INTO audit_log (table_name, operation, new_values, username)
    VALUES ('audit_log', 'CLEANUP', 
            jsonb_build_object('deleted_records', deleted_count, 'retention_days', retention_days),
            'system');
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ============================================================================
-- SECURITY SETTINGS
-- ============================================================================

-- Set secure defaults
ALTER DATABASE trustvault SET log_statement = 'all';
ALTER DATABASE trustvault SET log_min_duration_statement = 1000;
ALTER DATABASE trustvault SET log_connections = on;
ALTER DATABASE trustvault SET log_disconnections = on;
ALTER DATABASE trustvault SET log_lock_waits = on;

-- Grant execute permissions on security functions
GRANT EXECUTE ON FUNCTION encrypt_sensitive_data(TEXT, TEXT) TO trustvault_app;
GRANT EXECUTE ON FUNCTION decrypt_sensitive_data(TEXT, TEXT) TO trustvault_app;
GRANT EXECUTE ON FUNCTION hash_password(TEXT) TO trustvault_app;
GRANT EXECUTE ON FUNCTION verify_password(TEXT, TEXT) TO trustvault_app;

-- Grant select on security views
GRANT SELECT ON security_events TO trustvault_app;
GRANT SELECT ON failed_login_attempts TO trustvault_app;

COMMIT;

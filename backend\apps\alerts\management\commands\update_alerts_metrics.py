# TrustVault - Update Alerts Metrics Command

from django.core.management.base import BaseCommand
from django.utils import timezone
from apps.alerts.metrics import update_active_alerts_gauge, update_active_reports_gauge, metrics_collector
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Update alerts system metrics for monitoring'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Enable verbose output',
        )
    
    def handle(self, *args, **options):
        verbose = options['verbose']
        
        if verbose:
            self.stdout.write(
                self.style.SUCCESS(f'Starting alerts metrics update at {timezone.now()}')
            )
        
        try:
            # Update Prometheus gauges
            update_active_alerts_gauge()
            update_active_reports_gauge()
            
            if verbose:
                self.stdout.write(
                    self.style.SUCCESS('✅ Prometheus gauges updated')
                )
            
            # Collect comprehensive metrics
            metrics = metrics_collector.collect_all_metrics()
            
            if verbose:
                self.stdout.write(
                    self.style.SUCCESS('✅ Comprehensive metrics collected')
                )
                self.stdout.write(f"Alert counts: {metrics.get('alert_counts', {})}")
                self.stdout.write(f"Triggers (24h): {metrics.get('triggers_24h', 0)}")
                self.stdout.write(f"Notification stats: {metrics.get('notification_stats', {})}")
                self.stdout.write(f"Report stats: {metrics.get('report_stats', {})}")
            
            logger.info("Alerts metrics updated successfully")
            
            self.stdout.write(
                self.style.SUCCESS(
                    f'Successfully updated alerts metrics at {timezone.now()}'
                )
            )
            
        except Exception as e:
            error_msg = f'Failed to update alerts metrics: {str(e)}'
            logger.error(error_msg)
            self.stdout.write(
                self.style.ERROR(error_msg)
            )
            raise
